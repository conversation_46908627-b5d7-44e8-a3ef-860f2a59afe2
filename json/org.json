{
    "_id" : ObjectId("6425eef367c2b869c563d7be"),
    "name" : "<PERSON><PERSON><PERSON>",
    "url" : "lactalis.bmgmarketing.com",
    "ui" : {
        "styles" : "a{text-decoration:none;} a:hover, a:active{text-decoration:underline} .on{background:#ffc107;color:#333; border:1px solid #ffc107;} button.on:hover, button.on:active{ background:#ffd147; color:#333;border:1px solid #ffc107;}.brand-button{background:#0b4381;color:#ffffff;} .brand-button:hover{background:#0b4381;color:#ffffff;} .card{border-radius:5px !important;} .nav-tabs  .nav-link{ color:#0b4381 !important;}  .nav-tabs .nav-item > a.active{ font-weight:bold; !important;} td.label{width:20%; font-weight:bold; }.brand-button:hover{background:#4a698c;color:#ffffff;} .brand-button:active{background:#4a698c;color:#ffffff;} ",
        "templates" : {
            "portal" : {
                "head" : "<div class=\"container-fluid\">\n<div class=\"row\">\n    <div class=\"col-12 col-md-6 __tc\" style=\"background:#0b4381\"><img src=\"https://portalmint.s3.amazonaws.com/site_assets/6425eef367c2b869c563d7be/lactalis_logo.png\" class=\"__my-2\" style=\"max-height:100px; max-width:100%;\"></div>\n    <div class=\"col-12 col-md-6 __tc\" style=\"background:#fff;\"><img src=\"https://portalmint.s3.amazonaws.com/site_assets/6425eef367c2b869c563d7be/lactalis_product_logos.png\" class=\"__my-2\" style=\"max-height:100px; max-width:100%;\"></div>\n</div>\n</div>",
                "itemGallery" : "{{#each items}}\n<div class=\"col-12 col-lg-3 col-md-3 col-sm-6 __mb-2\">\n    <div class=\"card\" style=\"width: 100%;\">\n        <div class=\"galleryItemContainer __tc\">\n            <img src=\"{{#if this.image}}{{objectStorageUrl}}/thumbs/{{this.image}}{{else}}/assets/img/noimage.png{{/if}}\" class=\"galleryItemImage __pointer __mt-2\" style=\"width:75%;\" onclick=\"__.js.details('{{this._id}}', this);\">\n        </div>\n        <div class=\"card-body\">\n            <p class=\"card-text\"><b>{{this.name}}</b></p>\n            <div class=\"__tr\">\n                {{#if this.actions.email}}\n                {{#hasPermissions x context=\"items\" action=\"outbox\"}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05{{#if this.is_outbox}} on{{/if}}\" onclick=\"__.js.outbox('{{this._id}}', this);\" title=\"Email this item\"><i class=\"bi bi-envelope-plus-fill\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.actions.order}}\n                {{#hasPermissions x context=\"orders\" action=\"c\"}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05{{#if this.in_cart}} on{{/if}}\" onclick=\"__.js.cart('{{this._id}}', this);\" title=\"Add item to cart\"><i class=\"bi bi-cart\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.actions.download}}\n                {{#hasPermissions x context=\"items\" action=\"download\"}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05\" onclick=\"__.js.download('{{this._id}}','specsheet', this);\" title=\"Download item\"><i class=\"bi bi-download\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.linked_items}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05\" onclick=\"__.js.renderLinkeditems('{{this._id}}', this);\" title=\"Associated items\"><i class=\"bi bi-diagram-3-fill\"></i></button>\n                {{/if}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05\" onclick=\"__.js.createLink('{{this._id}}');\" title=\"Copy a link to this item\"><i class=\"bi bi-link\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05\" onclick=\"__.js.details('{{this._id}}', this);\" title=\"Item details\"><i class=\"bi bi-info-square-fill\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05{{#if this.is_fav}} on{{/if}}\" onclick=\"__.js.favorite('{{this._id}}', this);\" title=\"Add item to favorites\"><i class=\"bi bi-star-fill\"></i></button>\n            </div>\n        </div>\n    </div>\n</div>\n{{/each}}",
                "itemGalleryHeader" : "",
                "itemList" : "<div class=\"container\">\n    {{#each items}}\n    <div class=\"row __my-2 __py-1 itemListRow\">\n        <div class=\"col-12 col-md-6\">{{this.name}}</div>\n        <div class=\"col-6 col-md-2\">{{taggroup \"Season\" this.tags}}</div>\n        <div class=\"col-6 col-md-2\">{{taggroup \"Brand\" this.tags}}</div>\n        <div class=\"col-12 col-md-2\">\n            {{#if this.actions.email}}\n                {{#hasPermissions x context=\"items\" action=\"outbox\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if this.is_outbox}} on{{/if}}\" onclick=\"__.js.outbox('{{this._id}}', this);\" title=\"Email this item\"><i class=\"bi bi-envelope-plus-fill\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.actions.order}}\n                {{#hasPermissions x context=\"orders\" action=\"c\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if this.in_cart}} on{{/if}}\" onclick=\"__.js.cart('{{this._id}}', this);\" title=\"Add item to cart\"><i class=\"bi bi-cart\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.actions.download}}\n                {{#hasPermissions x context=\"items\" action=\"download\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.download('{{this._id}}','specsheet', this);\" title=\"Download item\"><i class=\"bi bi-download\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if this.linked_items}}\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.renderLinkeditems('{{this._id}}', this);\" title=\"Associated items\"><i class=\"bi bi-diagram-3-fill\"></i></button>\n                {{/if}}\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.createLink('{{this._id}}');\" title=\"Copy a link to this item\"><i class=\"bi bi-link\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.details('{{this._id}}', this);\" title=\"Item details\"><i class=\"bi bi-info-square-fill\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if this.is_fav}} on{{/if}}\" onclick=\"__.js.favorite('{{this._id}}', this);\" title=\"Add item to favorites\"><i class=\"bi bi-star-fill\"></i></button>\n        </div>\n    </div>\n    {{/each}}\n</div>",
                "itemListHeader" : "<div class=\"container\">\n    <div id=\"itemListHeader\" class=\"row __mb-3 fs-4 __bold\">\n        <div class=\"col-12 col-md-6\">Item</div>\n        <div class=\"col-6 col-md-2\">Season</div>\n        <div class=\"col-6 col-md-2\">Brand</div>\n        <div class=\"col-12 col-md-2\">Actions</div>\n    </div>\n</div>",
                "dashboard" : "<div class=\"input-group __mb-4\">\n    <input id=\"searchBox\" type=\"text\" class=\"form-control\" placeholder=\"Keyword Search\" onkeypress=\"__.js.searchKeyPress(event)\">\n    <span class=\"input-group-btn\">\n        <button class=\"btn btn-secondary btn-lg __ml-1\" type=\"button\" onclick=\"__.js.search();\"><i class=\"bi bi-search\"></i></button>\n    </span>\n</div> \n\n<div class=\"container\">\n    <div class=\"row\">\n        <div class=\"col-12 col-md-6 order-md-2\">\n            <div class=\"card __mb-3\"><div class=\"card-body\">\n                <h2>Quick Links</h2>\n                \n                {{#eachTagInGroup x groups=\"QuickLinks\"}}\n                    <button class=\"btn brand-button __block __my-2 __p-1\" onclick=\"__.routeTo('/portal/items?t={{this.tag.id}}')\">{{this.value}}</button>\n                {{/eachTagInGroup}}\n            </div></div>\n        </div>\n        \n        <div class=\"col-12 col-md-6\">\n            <div class=\"card __mb-3\"><div class=\"card-body\">\n                <h2>Featured Items</h2>\n                <div class=\"row\">\n                {{#itemsWithTags x tags=\"Featured Items\"}} \n                   <div class=\"col-12 col-md-4 __tc __my-1\">\n                    <a href=\"/portal/item/details/{{this._id}}\" onclick=\"__.routeTo('/portal/item/details/{{this._id}}'); return false;\">\n                    <img src=\"/assets/img/noimage.png\" style=\"max-width:100%;\"><br>\n                    {{this.name}}</a>\n                </div>\n                {{/itemsWithTags}}\n                </div>\n            </div></div>\n            \n        </div>\n        \n    </div>\n</div>\n\n<!--{{#hasTag T tags=\"BMG\" src=\"account\"}}HI BMG STAFF{{/hasTag}}-->",
                "itemDetailsLinkedItems" : "<h4>Related Items</h4>\n<div>\n    <ul>{{#each linkedItems}}\n        <li><a href=\"/portal/item/details/{{_id}}\" onclick=\"__.routeTo('/portal/item/details/{{_id}}'); bootbox.hideAll(); return false;\">{{name}}</a></li>\n    {{/each}}</ul>\n</div>",
                "orderPlaced" : "<h2 class=\"__tc __text-muted __my-6\">Your Order Has Been Placed!</h2>",
                "itemDetails" : "<div class=\"container\">\n    <div class=\"row\">\n        <div class=\"col __my-2\">\n            <h4>{{item.name}}</h4>\n        </div>\n    </div>\n    <div class=\"row\">\n        <div class=\"col-md-4\">\n            <img src=\"{{item.image}}\" style=\"width:90%;\">\n        </div>\n        <div class=\"col-md-8\">\n            <div class=\"__tr\">\n                {{#if item.actions.email}}\n                {{#hasPermissions x context=\"items\" action=\"outbox\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if item.is_outbox}} on{{/if}}\" onclick=\"__.js.outbox('{{item._id}}', this);\" title=\"Email this item\"><i class=\"bi bi-envelope-plus-fill\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if item.actions.order}}\n                {{#hasPermissions x context=\"orders\" action=\"c\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if item.in_cart}} on{{/if}}\" onclick=\"__.js.cart('{{item._id}}', this);\" title=\"Add item to cart\"><i class=\"bi bi-cart\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                {{#if item.actions.download}}\n                {{#hasPermissions x context=\"items\" action=\"download\"}}\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.download('{{item._id}}','specsheet', this);\" title=\"Download item\"><i class=\"bi bi-download\"></i></button>\n                {{/hasPermissions}}{{/if}}\n                <button class=\"btn btn-secondary btn-sm __ml-05\" onclick=\"__.js.createLink('{{item._id}}');\" title=\"Copy a link to this item\"><i class=\"bi bi-link\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __ml-05{{#if item.is_fav}} on{{/if}}\" onclick=\"__.js.favorite('{{item._id}}', this);\" title=\"Add item to favorites\"><i class=\"bi bi-star-fill\"></i></button>\n            </div>\n            <div class=\"table-responsive\">\n                <table class=\"table\">\n                    <tbody>\n                        <tr>\n                            <td class=\"label\">Brand:</td>\n                            <td>{{taggroup \"Brand\" item.searchable_item_tag_labels empty=\"-\"}}</td>\n                        </tr>\n                        <tr>\n                            <td class=\"label\">Category:</td>\n                            <td>{{taggroup \"Product Category\" item.searchable_item_tag_labels empty=\"-\"}}</td>\n                        </tr>\n                        <tr>\n                            <td class=\"label\">Type:</td>\n                            <td>{{taggroup \"Type\" item.searchable_item_tag_labels empty=\"-\"}}</td>\n                        </tr>\n                        <tr>\n                            <td class=\"label\">Updated:</td>\n                            <td>{{formatDate item.updated format=\"MM/DD/YY\"}}</td>\n                        </tr>\n                        <tr>\n                            <td class=\"label\">Resource No. (SKU):</td>\n                            <td>{{item.fields.SKU}}</td>\n                        </tr>\n                        {{#if item.fields.GTIN}}\n                        <tr>\n                            <td class=\"label\">GTIN:</td>\n                            <td>{{item.fields.GTIN}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.ITEMUPC}}\n                        <tr>\n                            <td class=\"label\">Item UPC:</td>\n                            <td>{{item.fields.ITEMUPC}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.UNITSIZE}}\n                        <tr>\n                            <td class=\"label\">Unit Size:</td>\n                            <td>{{item.fields.UNITSIZE}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.SHELFLIFE}}\n                        <tr>\n                            <td class=\"label\">Shelf Life (Total / At Shipping):</td>\n                            <td>{{item.fields.SHELFLIFE}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.CASEWEIGHT}}\n                        <tr>\n                            <td class=\"label\">Case Weight (Gross / Net):</td>\n                            <td>{{item.fields.CASEWEIGHT}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.CASEDIM}}\n                        <tr>\n                            <td class=\"label\">Case L, W, H:</td>\n                            <td>{{item.fields.CASEDIM}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.CUBE}}\n                        <tr>\n                            <td class=\"label\">Cube:</td>\n                            <td>{{item.fields.CUBE}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.TIExHEIGHT}}\n                        <tr>\n                            <td class=\"label\">Tie x High:</td>\n                            <td>{{item.fields.TIExHEIGHT}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.COA}}\n                        <tr>\n                            <td class=\"label\">Country Of Origin:</td>\n                            <td>{{item.fields.COA}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.PRICETYPE}}\n                        <tr>\n                            <td class=\"label\">Price Type:</td>\n                            <td>{{item.fields.PRICETYPE}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.WEIGHTMETHOD}}\n                        <tr>\n                            <td class=\"label\">Weight Method:</td>\n                            <td>{{item.fields.WEIGHTMETHOD}}</td>\n                        </tr>\n                        {{/if}}\n                        {{#if item.fields.DESCRIPTION}}\n                        <tr>\n                            <td class=\"label\">Description:</td>\n                            <td>{{item.fields.DESCRIPTION}}</td>\n                        </tr>\n                        {{/if}}\n                        \n                        <!--<tr>\n                            <td class=\"label\">Tags:</td>\n                            <td>{{{arrayToString item.searchable_item_tag_labels join=\"<br>\" empty=\"-\"}}}</td>\n                        </tr>-->\n                        \n                    </tbody>\n                </table>\n            </div>\n            {{#if linkedItems}}\n            <div>\n                <b>See Also:</b>\n            </div>\n            <div>\n                <ul>{{#each linkedItems}}\n                    <li><a href=\"#\" onclick=\"__.js.renderDetails('{{_id}}'); bootbox.hideAll(); return false;\">{{name}}</a></li>\n                {{/each}}</ul>\n            </div>\n            {{/if}}\n            \n            \n        </div>\n    </div>\n\n</div>",
                "linkDetails" : "<div class=\"container\">\n    <div class=\"card __p-3 __mt-3\">\n        <div class=\"row\">\n            <div class=\"col __my-2\">\n                <h4>{{item.name}}</h4>\n            </div>\n        </div>\n        <div class=\"row\">\n            <div class=\"col-md-4\">\n                <img src=\"/assets/img/noimage.png\" style=\"width:90%;\">\n            </div>\n            <div class=\"col-md-8\">\n                <div class=\"table-responsive\">\n                    <table class=\"table\">\n                        <tbody>\n                            <tr>\n                                <td class=\"label\">SKU:</td>\n                                <td>{{item.fields.SKU}}</td>\n                            </tr>\n                            <tr>\n                                <td class=\"label\">Something else:</td>\n                                <td>Hi</td>\n                            </tr>\n                            <tr>\n                                <td class=\"label\">Updated:</td>\n                                <td>{{formatDate item.updated format=\"MM/DD/YY hh:mm a\"}}</td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <p>{{item.fields.DESCRIPTION}}</p> \n                    <p class=\"__tc __mt-3\"><button class=\"btn bnt-large brand-button __block\" onclick=\"__.js.download('specsheet');\">Download Spec Sheet</button></p>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>",
                "activityOrders" : "<div class=\"row dataRowHeader d-none d-md-flex\">\n    <div class=\"col-6 col-md\">Updated</div>\n    <div class=\"col-6 col-md\">Status</div>\n    <div class=\"col-12 col-md\">Company</div>\n    <div class=\"col-12 col-md\">Recipient</div>\n    <div class=\"col-12 col-md\">Email</div>\n    <div class=\"col\"></div>\n</div>\n{{#each orders}}\n<div class=\"row dataRow\">\n    <div class=\"col-6 col-md\">{{formatDate this.updated format=\"MM/DD/YY @ hh:mm a\"}}</div>\n    <div class=\"col-6 col-md\">{{this.status.label}}</div>\n    <div class=\"col-12 col-md\">{{this.fields.COMPANY}}</div>  \n    <div class=\"col-12 col-md\">{{this.fields.LNAME}} {{this.fields.FNAME}}</div>\n    <div class=\"col-12 col-md\">{{this.fields.EMAIL}}</div>\n    <div class=\"col __tr\">\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.details('{{this._id}}', this);\" title=\"Details\"><i class=\"bi bi-info-square-fill\"></i></button>\n        {{#if this.status.editable}}\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.edit('{{this._id}}', this);\" title=\"Edit\"><i class=\"bi bi-pencil\"></i></button>\n        {{/if}}\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.duplicate('{{this._id}}', this);\" title=\"Duplicate Order\"><i class=\"bi bi-stickies-fill\"></i></button>\n        {{#if this.status.editable}}\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.changeStatus('{{this._id}}', 'EFDgh');\" title=\"Cancel Order\"><i class=\"bi bi-slash-circle\"></i></button>\n        {{/if}}\n    </div>\n</div>\n{{/each}}",
                "activityEmails" : "<div class=\"row dataRowHeader d-none d-md-flex\">\n    <div class=\"col-6 col-md\">Sent</div>\n    <div class=\"col-6 col-md-1\">Views</div>\n    <div class=\"col-6 col-md\">Last View</div>\n    <div class=\"col-12 col-md\">To</div>\n    <div class=\"col-12 col-md\">Subject</div>\n    <div class=\"col\"></div>\n</div>\n{{#each emails}}\n<div class=\"row dataRow\">\n    <div class=\"col-6 col-md\">{{formatDate this.created format=\"MM/DD/YY @ hh:mm a\"}}</div>\n    <div class=\"col-2 col-md-1\">{{this.views}}</div>\n    <div class=\"col-4 col-md\">{{formatDate this.last_viewed}}</div>\n    <div class=\"col-12 col-md\">{{this.to}}</div>        \n    <div class=\"col-12 col-md\">{{this.subject}}</div>\n    <div class=\"col __tr\">\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.details('{{this._id}}', this);\" title=\"Details\"><i class=\"bi bi-info-square-fill\"></i></button>\n        <button class=\"btn btn-secondary btn-sm __mr-05\" onclick=\"__.js.duplicate('{{this._id}}', this);\" title=\"Duplicate Order\"><i class=\"bi bi-stickies-fill\"></i></button>\n    </div>\n</div>\n{{/each}}",
                "activityEmailDetails" : "<div class=\"row\">\n    <div class=\"col __mb-2\">\n      <b>Sent:</b> {{formatDate order.created format=\"MM/DD/YY @ hh:mm a\"}}<br>\n      <b>To:</b> {{email.to}}<br>\n      <b>Subject:</b> {{email.subject}}<br>\n      <b>Views:</b> {{#if email.views}} {{email.views}} {{else}} 0 {{/if}}<br>\n      <b>Last Viewed:</b> {{#if email.views}}{{formatDate email.last_viewed}}{{/if}}<br>\n    </div>    \n</div>\n\n<div class=\"row\">\n    <div class=\"col\"><b>Items Sent:</b></div>\n</div>\n\n{{#each email.items}}\n<div class=\"row __my-1\">    \n    <div class=\"col\">{{this.name}}</div>\n</div>\n{{/each}}",
                "activityOrderDetails" : "<div class=\"row\">\n    <div class=\"col-12 col-md-6 __mb-2\">\n      <b>To:</b><br>\n      {{order.fields.FNAME}} {{order.fields.LNAME}}<br>\n      {{#if order.fields.COMPANY}} {{order.fields.COMPANY}}<br>{{/if}}\n      {{order.fields.EMAIL}}<br>\n    </div>\n    <div class=\"col-12 col-md-6\">\n      <p>\n        <b>Confirmation #:</b> {{order._id}}<br>\n        <b>Placed:</b> {{formatDate order.created}}<br>\n        {{#if order.fields.NEEDEDBY}}<b>Needed By:</b> {{formatDate order.fields.NEEDEDBY format=\"MM/DD/YY\"}}<br>{{/if}}\n      </p>\n    </div>\n</div>\n\n{{#if order.fields.INSTRUCTIONS}}\n<div class=\"row __mb-2\">\n    <div class=\"col-12\"><b>Instructons</b><br>{{order.fields.INSTRUCTIONS}}</div>\n</div>\n{{/if}}\n\n<div class=\"row\">\n    <div class=\"col-1\"><b>Qty</b></div>\n    <div class=\"col-11\"><b>Item</b></div>\n</div>\n  \n{{#each order.items}}\n<div class=\"row __my-1\">\n    <div class=\"col-1\">{{this.qty}}</div>\n    <div class=\"col-11\">{{this.name}}</div>\n</div>\n{{/each}}\n\n{{#if order.notes}}\n<div class=\"row __mt-3 __brdr __bg-1 __p-1 __mx-1\">\n    <div class=\"col\">    \n    {{#each order.notes}}\n      <div><i>{{formatDate this.created format=\"MM/DD/YY @ hh:mm a\"}}</i>\n      <p>{{this.note}}</p></div>\n    {{/each}}\n    </div>\n</div>\n{{/if}}"
            },
            "admin" : {
                "head" : "<div style=\"background:#428bca; color:#fff; padding:5px 20px; border-bottom:1px solid #ddd;\"><b>BMG Hub Admin</b></div>",
                "orderApproval" : "<div class=\"card\">\n    <div class=\"card-body\">\n        \n<div class=\"row __mb-2 __pb-2\" style=\"border-bottom:1px solid #000;\">\n    <div class=\"col-12 col-md-6\">\n        <b>Order Number:</b> {{order._id}}<br>\n        <b>Placed:</b> {{formatDate order.created}}<br>\n        {{#if order.fields.NEEDEDBY}}<b>Needed By:</b> {{formatDate order.fields.NEEDEDBY format=\"MM/DD/YY\"}}<br>{{/if}}\n    </div>\n    <div class=\"col-12 col-md-6 __tr\">\n        {{#ifIn order.status.id \"wW9DK,xYjGO\"}}\n        <p>This order has already been reviewed.<br>No action is required.<br>Current Status: <b>{{order.status.label}}</b></p>\n        {{else}}\n        <button class=\"btn btn-success __mr-2\" onclick=\"__.js.updateStatus('wW9DK');\">Approve Order</button>\n        <button class=\"btn btn-danger\" onclick=\"__.js.updateStatus('xYjGO');\">Reject Order</button>\n        {{/ifIn}}\n    </div>\n</div>\n        \n<div class=\"row\">\n    <div class=\"col-12 col-md-6 __mb-3\">\n      <b>Placed By:</b><br>\n      {{order.account.first_name}} {{order.account.last_name}}<br>\n      {{#if order.account.company}}{{order.account.company}}<br>{{/if}}\n      <a href=\"mailto:{{order.account.email}}\">{{order.account.email}}</a><br>\n      {{#if order.account.phone}}{{formatPhone order.account.phone format=\"formal\"}}<br>{{/if}}\n    </div>\n    <div class=\"col-12 col-md-6\">\n      <b>Shipping Information:</b><br>\n      {{order.fields.FNAME}} {{order.fields.LNAME}}<br>\n      {{#if order.fields.COMPANY}} {{order.fields.COMPANY}}<br>{{/if}}\n      {{order.fields.STREET}}<br>\n      {{#if order.fields.STREET_2}} {{order.fields.STREET_2}}<br>{{/if}}\n      {{order.fields.CITY}}, {{order.fields.STATE}} {{order.fields.ZIP}}<br>\n      <a href=\"mailto:{{order.fields.EMAIL}}\">{{order.fields.EMAIL}}</a><br>\n      {{#if order.fields.PHONE}} {{formatPhone order.fields.PHONE format=\"formal\"}}<br>{{/if}}\n    </div>\n</div>\n\n{{#if order.fields.INSTRUCTIONS}}\n<div class=\"row __my-3 __brdr __bg-1 __p-1 __mx-1\">\n    <div class=\"col\">    \n      <div><b>Instructions</b>\n      <p>{{order.fields.INSTRUCTIONS}}</p></div>\n    </div>\n</div>\n{{/if}}\n\n<div class=\"row\">\n    <div class=\"col-1\"><b>Qty</b></div>\n    <div class=\"col-11\"><b>Item</b></div>\n</div>\n  \n{{#each order.items}}\n<div class=\"row __my-1\">\n    <div class=\"col-1\">{{this.qty}}</div>\n    <div class=\"col-11\">{{this.name}}</div>\n</div>\n{{/each}}\n\n\n{{#if order.notes}}\n<div class=\"row __mt-3 __brdr __bg-1 __p-1 __mx-1\">\n    <div class=\"col\">    \n    {{#each order.notes}}\n      <div><i>{{formatDate this.created format=\"MM/DD/YY @ hh:mm a\"}}</i>\n      <p>{{this.note}}</p></div>\n    {{/each}}\n    </div>\n</div>\n{{/if}}\n    </div>\n</div>",
                "orderPackingSlip" : "{{#each orders}}\n<div style=\"font-family:Arial, Helvetica, sans-serif\">\n    <div class=\"row\">\n        <div class=\"col\">\n            <h5 class=\"__mt-2 __p-05\" style=\"background:#d2d2d2;\">Order Details</h5>\n            <div><b>Order Number:</b> {{this._id}}</div>\n            <div><b>Order Date:</b> {{formatDate this.created format=\"MM/DD/YYYY\"}}</div>\n            <div><b>Placed By:</b> {{this.account.first_name}} {{this.account.last_name}}</div>\n          {{#if this.account.phone}}<div><b>Phone:</b> {{this.account.phone}}</div>{{/if}}\n          {{#if this.account.email}}<div><b>Email:</b> {{this.account.email}}</div>{{/if}}\n        </div>\n        <div class=\"col\">\n          <h5 class=\"__mt-2 __p-05\" style=\"background:#d2d2d2;\">Delivery Details</h5>\n        <div style=\"margin-bottom:20px;\">\n          {{this.fields.FNAME}} {{this.fields.LNAME}}<br>\n          {{#if this.fields.COMPANY}}{{this.fields.COMPANY}}<br>{{/if}}\n          {{this.fields.STREET}}<br>\n          {{#if this.fields.STREET_2}}{{this.fields.STREET_2}}<br>{{/if}}\n          {{this.fields.CITY}}, {{this.fields.STATE}} {{this.fields.ZIP}}<br>\n          {{#if this.fields.PHONE}}{{this.fields.PHONE}}<br>{{/if}}\n          {{#if this.fields.EMAIL}}{{this.fields.EMAIL}}<br>{{/if}}\n        </div>\n        {{#if this.fields.NEEDEDBY}}<div><b>Date Needed:</b> {{formatDate this.fields.NEEDEDBY format=\"MM/DD/YYYY\"}}</div>{{/if}}  \n        </div>\n    </div>\n\n    {{#if this.fields.INSTRUCTIONS}} \n    <h5 class=\"__mt-2 __p-05\" style=\"background:#d2d2d2;\">Instructions</h5>\n    <div>{{this.fields.INSTRUCTIONS}}</div>\n    {{/if}}\n  \n    <h5 class=\"__mt-2 __p-05\" style=\"background:#d2d2d2;\">Items</h5>\n      <div class=\"row\">\n        <div class=\"col-1 __bold\">QTY</div>\n        <div class=\"col-2 __bold\">SKU</div>\n        <div class=\"col-9 __bold\">Product</div>      \n      </div>\n      {{#each this.items}}\n      <div class=\"row\">\n        <div class=\"col-1\">{{this.qty}}</div>\n        <div class=\"col-2\">{{this.fields.SKU}}</div>\n        <div class=\"col-9\">{{this.name}}</div>      \n      </div>\n      {{/each}}   \n      \n    \n      \n    {{#if this.notes}} \n    <h5 class=\"__mt-2 __p-05\" style=\"background:#d2d2d2;\">Notes</h5>\n    {{#each this.notes}}\n    <div class=\"__mb-1 __pb-1 __bb\"><i class=\"__smaller\">{{formatDate this.ts format=\"MM/DD/YYYY HH:mma\"}}</i><br>{{this.note}}</div>\n    {{/each}}\n    {{/if}}\n    \n  </div>\n{{/each}}",
                "itemGallery" : "{{#each items}}\n<div class=\"col-12 col-lg-3 col-md-3 col-sm-6 __mb-2\">\n    <div class=\"card\" style=\"width: 100%;\">\n        <div class=\"galleryItemContainer __tc\">\n            <img src=\"{{#if this.image}}{{objectStorageUrl}}/thumbs/{{this.image}}{{else}}/assets/img/noimage.png{{/if}}\" class=\"galleryItemImage __pointer __mt-2\" style=\"width:75%;\" onclick=\"__.js.edit('{{this._id}}', this);\">\n        </div>\n        <div class=\"card-body\">\n            <p class=\"card-text\"><b>{{this.name}}</b></p>\n            <div class=\"__tr\">\n                {{#hasPermissions x context=\"items\" action=\"outbox\"}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05{{#if this.is_outbox}} on{{/if}}\" onclick=\"__.js.outbox('{{this._id}}', this);\" title=\"Email this item\"><i class=\"bi bi-envelope-plus-fill\"></i></button>\n                {{/hasPermissions}}\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05{{#if this.is_fav}} on{{/if}}\" onclick=\"__.js.favorite('{{this._id}}', this);\" title=\"Add item to favorites\"><i class=\"bi bi-star-fill\"></i></button>\n                <button class=\"btn btn-secondary btn-sm __mb-1 __ml-05\" onclick=\"__.js.edit('{{this._id}}', this);\" title=\"Edit\"><i class=\"bi bi-pencil-fill\"></i></button>\n            </div>\n        </div>\n    </div>\n</div>\n{{/each}}",
                "itemEdit" : "<h5 class=\"__mt-0 __mb-2\">{{item.name}}</h5>\n<ul class=\"nav nav-tabs\">  \n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"basicTab\"  onclick=\"__.js.tabSwitcher('basic'); return false;\">Basic</a>\n      </li>\n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"fieldsTab\" onclick=\"__.js.tabSwitcher('fields'); return false;\">Fields</a>\n      </li>\n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"tagsTab\" onclick=\"__.js.tabSwitcher('tags'); return false;\">Tags</a>\n      </li>\n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"filesTab\" onclick=\"__.js.tabSwitcher('files'); return false;\">Files</a>\n      </li>\n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"permissionsTab\" onclick=\"__.js.tabSwitcher('permissions'); return false;\">Permissions</a>\n      </li>\n      <li class=\"nav-item\">\n        <a class=\"nav-link __pointer tab\" id=\"inventoryTab\" onclick=\"__.js.tabSwitcher('inventory'); return false;\">Inventory</a>\n      </li>\n      \n    </ul>\n\n<!-- BASIC FORM -->\n    <div class=\"container-fluid __mt-4 __px-2 __hide editFormContainer\" id=\"basicForm\">      \n      <div class=\"row __my-2 __bb-1 __pb-2\">\n        <div class=\"col\">\n          <label class=\"form-label\">Name</label>\n          <input type=\"text\" class=\"form-control form-control-lg\" id=\"name\" name=\"name\" placeholder=\"Product name\">\n        </div>\n      </div>\n      <div class=\"row __my-2\">\n        <div class=\"col-7\">\n          <label class=\"form-label\">Upload Image</label>\n          <input type=\"file\" class=\"form-control form-control-lg\" id=\"imageFile\" name=\"image_file\" onchange=\"__.js.readImage(this);\">\n          <div class=\"__tc __my-2\">- Or -</div>\n          <label class=\"form-label\">Existing Image</label>\n          <div id=\"imageLibrarySelect\"></div>\n        </div>\n        <div class=\"col-5 __tc\">\n            <img src=\"\" id=\"imagePreview\" class=\"__brdr\" style=\"width:100%; max-width:300px;\" />\n            <input type=\"hidden\" id=\"imageSrc\" name=\"image_src\">\n            <input type=\"hidden\" id=\"imageName\" name=\"image_name\">\n            <input type=\"hidden\" id=\"imageType\" name=\"image_type\">\n        </div>\n      </div>\n      <div class=\"row __my-2\">\n          <div class=\"col\"><button class=\"btn __block brand-button\" onclick=\"__.js.save('basic');\" id=\"saveButton_basic\">Save Changes</button></div>\n      </div>\n    </div>\n    \n<!-- FIELDS FORM -->\n    <div class=\"container-fluid __mt-4 __px-2 __hide editFormContainer\" id=\"fieldsForm\">      \n      <div class=\"row\"><div class=\"col\" id=\"itemFieldsHolder\"></div></div>\n      <div class=\"row __my-2\">\n          <div class=\"col\"><button class=\"btn __block brand-button\" onclick=\"__.js.save('fields');\" id=\"saveButton_files\">Save Changes</button></div>\n      </div>\n    </div>\n    \n<!-- PERMISSIONS FORM -->\n    <div class=\"container-fluid __mt-4 __px-2 __hide editFormContainer\" id=\"permissionsForm\">      \n        <div class=\"row __my-2 __bb-1 __pb-2\">\n            <div class=\"col\">\n                <h4 class=\"__bb\">Portal Actions</h4>\n                <div class=\"form-check\">\n                    <input class=\"form-check-input\" type=\"checkbox\" value=\"link\" id=\"linkable\" name=\"link\">\n                    <label class=\"form-check-label\" for=\"flexCheckDefault\">Linkable</label>\n                </div>\n                <div class=\"form-check\">\n                    <input class=\"form-check-input\" type=\"checkbox\" value=\"download\" id=\"downloadable\" name=\"download\">\n                    <label class=\"form-check-label\" for=\"flexCheckChecked\">Downloadable</label>\n                </div>\n                <div class=\"form-check\">\n                    <input class=\"form-check-input\" type=\"checkbox\" value=\"email\" id=\"emailable\" name=\"email\">\n                    <label class=\"form-check-label\" for=\"flexCheckChecked\">Emailable</label>\n                </div>\n                <div class=\"form-check\">\n                    <input class=\"form-check-input\" type=\"checkbox\" value=\"order\" id=\"Orderable\" name=\"order\">\n                    <label class=\"form-check-label\" for=\"flexCheckChecked\">Orderable</label>\n                </div>\n            </div>\n      </div>\n      \n      <div class=\"row __my-2\">\n          <div class=\"col\"><button class=\"btn __block brand-button\" onclick=\"__.js.save('permissions');\" id=\"saveButton_basic\">Save Changes</button></div>\n      </div>\n    </div>\n    \n<!-- TAGS FORM -->\n    <div class=\"container-fluid __mt-4 __px-2 __hide editFormContainer\" id=\"tagsForm\">      \n        <div class=\"row\">\n            <div class=\"col-6\" id=\"itemTagsHolder\">\n                <h4>Available Tags</h4>\n                <ul id=\"availableTags\" class=\"list-group __m-auto\"></ul>\n            </div>\n            <div class=\"col-6\" id=\"itemTagsHolder\">\n                <h4>Item Tags</h4>\n                <ul id=\"activeTags\" class=\"list-group __m-auto\"></ul>\n            </div>\n        </div>\n      <div class=\"row __my-2\">\n          <div class=\"col\"><button class=\"btn __block brand-button\" onclick=\"__.js.save('tags');\" id=\"saveButton_tags\">Save Changes</button></div>\n      </div>\n    </div>\n    \n<!-- FILES FORM -->\n    <div class=\"container-fluid __mt-4 __px-2 __hide editFormContainer\" id=\"filesForm\">      \n      <div class=\"row\"><div class=\"col\" id=\"itemFilesHolder\"></div></div>\n      <div class=\"row __my-2\">\n          <div class=\"col\"><button class=\"btn __block brand-button\" onclick=\"__.js.save('files');\" id=\"saveButton_files\">Save Changes</button></div>\n      </div>\n    </div>"
            },
            "auth" : {
                "head" : "<div class=\"container-fluid\">\n<div class=\"row\">\n    <div class=\"col-12 col-md-6 __tc\" style=\"background:#0b4381\"><img src=\"https://portalmint.s3.amazonaws.com/site_assets/6425eef367c2b869c563d7be/lactalis_logo.png\" class=\"__my-2\" style=\"max-height:100px; max-width:100%;\"></div>\n    <div class=\"col-12 col-md-6 __tc\" style=\"background:#fff;\"><img src=\"https://portalmint.s3.amazonaws.com/site_assets/6425eef367c2b869c563d7be/lactalis_product_logos.png\" class=\"__my-2\" style=\"max-height:100px; max-width:100%;\"></div>\n</div>\n</div>",
                "loginHead" : "<h5 class=\"card-title\">Signin</h5><p class=\"card-text\">Enter your email below and click the Continue button.</p>",
                "loginFooter" : "<div class=\"__mt-3 __text-muted __smaller text-center\">Need an access? <a href=\"/register\">Request an Account</a></div>",
                "registerHead" : "<div class=\"__bb-1 __pb-2\"><h5 class=\"card-title\">New Account</h5><p class=\"card-text\">Request a Lactalis Sales Team Portal Account.</p></div>",
                "registerComplete" : "<h5>Request Sent!</h5>\n<p class=\"__my-3\">Thank you for submitting your request. Your submission will be reviewed shortly.</p>"
            }
        },
        "navs" : {
            "portalTop" : [
                {
                    "label" : "Dashboard",
                    "target" : "/portal"
                },
                {
                    "label" : "Items",
                    "target" : "/portal/items"
                },
                {
                    "id" : "cart",
                    "label" : "Cart",
                    "target" : "/portal/cart"
                },
                {
                    "id" : "outbox",
                    "label" : "Outbox",
                    "target" : "/portal/outbox"
                },
                {
                    "label" : "My Activity",
                    "options" : [
                        {
                            "label" : "Orders",
                            "target" : "/portal/activity/orders"
                        },
                        {
                            "label" : "Emails",
                            "target" : "/portal/activity/emails"
                        }
                    ]
                },
                {
                    "label" : "Landing Pages",
                    "options" : [
                        {
                            "label" : "Premio Mozzarella",
                            "target" : "https://www.lactalisculinary.com/products/galbani-premio-mozzarella/index.html"
                        },
                        {
                            "label" : "Tigrato Cut Shreds",
                            "target" : "https://www.lactalisculinary.com/products/galbani-premio-mozzarella/index.html#tigrato-cut"
                        },
                        {
                            "label" : "Fresh Mozzarella",
                            "target" : "https://www.lactalisculinary.com/products/galbani-fresh-mozzarella/index.html"
                        },
                        {
                            "label" : "Thin Sliced Fresh Mozzarella",
                            "target" : "https://www.lactalisculinary.com/products/galbani-thin-sliced-fresh-mozzarella/index.html"
                        },
                        {
                            "label" : "Feta",
                            "target" : "https://www.lactalisculinary.com/products/president-feta/"
                        },
                        {
                            "label" : "Goat Cheese",
                            "target" : "https://www.lactalisculinary.com/products/president-goat-cheese/"
                        },
                        {
                            "label" : "Parmesan Packets",
                            "target" : "https://www.lactalisculinary.com/products/galbani-parmesan-packets/index.html"
                        }
                    ]
                },
                {
                    "label" : "Portal Support",
                    "target" : "https://pages.lactalisculinary.com/lactalis-sales-team-portal-support"
                },
                {
                    "label" : "Admin",
                    "target" : "/admin",
                    "tab" : true,
                    "visible_to" : [
                        "fF01zd2W",
                        "qtK6Hdsv0l"
                    ]
                }
            ],
            "admin" : [
                {
                    "label" : "Dashboard",
                    "target" : "/admin"
                },
                {
                    "label" : "Orders",
                    "options" : [
                        {
                            "label" : "Manage",
                            "target" : "/admin/orders"
                        }
                    ]
                },
                {
                    "label" : "Items",
                    "options" : [
                        {
                            "label" : "Manage",
                            "target" : "/admin/items"
                        }
                    ]
                },
                {
                    "label" : "Communications",
                    "options" : [
                        {
                            "label" : "Item Promoter",
                            "target" : "/admin/emailer/itempromoter"
                        }
                    ]
                },
                {
                    "label" : "Developer Zone",
                    "options" : [
                        {
                            "label" : "Portal Templates",
                            "target" : "/admin/templates/ui"
                        },
                        {
                            "label" : "Email Templates",
                            "target" : "/admin/templates/email"
                        }
                    ]
                },
                {
                    "label" : "Portal",
                    "target" : "/portal"
                }
            ]
        },
        "default_home" : {
            "BMG" : "/admin",
            "Admin" : "/admin",
            "Broker" : "/portal"
        }
    },
    "server" : {
        "templates" : {
            "otp" : {
                "from" : "Lactalis Broker Portal",
                "subject" : "Your Lactalis Single Use Passcode",
                "body" : "<p>Welcome Back To Your <b>Lactalis Broker Portal</b>!</p><p>Below is your secure one-time passcode. Enter it into the login screen to continue.</p><p>This passcode will expire in 10 minutes.</p><h1 style=\"letter-spacing:5px; margin-top:30px; font-family: 'Courier New', monospace;\">{{OTP}}</h1>"
            },
            "outbox" : {
                "from" : "Lactalis Broker Portal",
                "body" : "<p>{{message}}</p><p>{{#each items}}<a href=\"{{this.url}}\">{{this.name}}</a><br>{{/each}}</p>"
            },
            "order" : {
                "newToAccount" : {
                    "from" : "Lactalis Broker Portal",
                    "body" : "<div style=\"font-family:Arial, Helvetica, sans-serif\">\n  <h2 style=\"background:#d2d2d2; padding:5px;\">Order Details</h2>\n  <div><b>Order Number:</b> {{order_id}}</div>\n  <div><b>Order Date:</b> {{formatDate order_date format=\"MM/DD/YYYY\"}}</div>\n\n  <h2 style=\"background:#d2d2d2; padding:5px;\">Delivery Details</h2>\n  <div><b>To:</b></div>\n  <div style=\"margin-bottom:20px;\">\n    {{fields.FNAME}} {{fields.LNAME}}<br>\n    {{#if fields.COMPANY}}{{fields.COMPANY}}<br>{{/if}}\n    {{fields.STREET}}<br>\n    {{#if fields.STREET_2}}{{fields.STREET_2}}<br>{{/if}}\n    {{fields.CITY}}, {{fields.STATE}} {{fields.ZIP}}<br>\n    {{#if fields.PHONE}}{{fields.PHONE}}<br>{{/if}}\n    {{#if fields.EMAIL}}{{fields.EMAIL}}<br>{{/if}}\n  </div>\n  {{#if fields.NEEDEDBY}}<div><b>Date Needed:</b> {{formatDate fields.NEEDEDBY format=\"MM/DD/YYYY\"}}</div>{{/if}}\n  {{#if fields.INSTRUCTIONS}}<div><b>Instructions:</b> {{fields.INSTRUCTIONS}}</div>{{/if}}\n\n  <h2 style=\"background:#d2d2d2; padding:5px;\">Items</h2>\n    <div style=\"width:100%; height:20px;\">\n      <div style=\"float:left; width:150px; font-weight: bold;\">QTY</div>\n      <div style=\"float:left; width:150px; font-weight: bold;\">SKU</div>\n      <div style=\"float:left; width:400px; font-weight: bold;\">Product</div>      \n    </div>\n    {{#each items}}\n    <div style=\"width:100%; min-width:700px; padding-top:5px; padding-bottom:5px;\">\n      <div style=\"float:left; width:150px;\">{{this.qty}}</div>\n      <div style=\"float:left; width:150px;\">{{this.fields.SKU}}</div>\n      <div style=\"float:left; width:400px;\">{{this.name}}</div>      \n    </div>\n    {{/each}}   \n</div>",
                    "subject" : "Your order has been placed"
                },
                "newToRecipient" : {
                    "from" : "Lactalis Broker Portal",
                    "subject" : "You will be receiving an order from Lactalis",
                    "body" : "<h1>Items</h1>{{#each items}}<p>{{this.name}} x {{this.qty}}</p>{{/each}}"
                },
                "statusChangedToAccount" : {
                    "from" : "Lactalis Broker Portal",
                    "body" : "<div style=\"font-family:Arial, Helvetica, sans-serif\">\n    <h2 style=\"background:#d2d2d2; padding:5px;\">Order Details</h2>\n    <div><b>Order Number:</b> {{order._id}}</div>\n    <div><b>Order Date:</b> {{formatDate order_date format=\"MM/DD/YYYY\"}}</div>\n  \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Staus Changed</h2>\n    <p>Your order has been updated to: <b>{{newStatus}}</b></p>\n  \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Delivery Details</h2>\n    <div><b>To:</b></div>\n    <div style=\"margin-bottom:20px;\">\n      {{order.fields.FNAME}} {{order.fields.LNAME}}<br>\n      {{#if order.fields.COMPANY}}{{order.fields.COMPANY}}<br>{{/if}}\n      {{order.fields.STREET}}<br>\n      {{#if order.fields.STREET_2}}{{order.fields.STREET_2}}<br>{{/if}}\n      {{order.fields.CITY}}, {{order.fields.STATE}} {{order.fields.ZIP}}<br>\n      {{#if order.fields.PHONE}}{{order.fields.PHONE}}<br>{{/if}}\n      {{#if order.fields.EMAIL}}{{order.fields.EMAIL}}<br>{{/if}}\n    </div>\n    {{#if order.fields.NEEDEDBY}}<div><b>Date Needed:</b> {{formatDate order.fields.NEEDEDBY format=\"MM/DD/YYYY\"}}</div>{{/if}}\n    {{#if order.fields.INSTRUCTIONS}}<div><b>Instructions:</b> {{order.fields.INSTRUCTIONS}}</div>{{/if}}\n  \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Items</h2>\n      <div style=\"width:100%; height:20px;\">\n        <div style=\"float:left; width:150px; font-weight: bold;\">QTY</div>\n        <div style=\"float:left; width:150px; font-weight: bold;\">SKU</div>\n        <div style=\"float:left; width:400px; font-weight: bold;\">Product</div>      \n      </div>\n      {{#each items}}\n      <div style=\"width:100%; min-width:700px; padding-top:5px; padding-bottom:5px;\">\n        <div style=\"float:left; width:150px;\">{{this.qty}}</div>\n        <div style=\"float:left; width:150px;\">{{this.fields.SKU}}</div>\n        <div style=\"float:left; width:400px;\">{{this.name}}</div>      \n      </div>\n      {{/each}}   \n      \n    {{#if order.notes}} \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Notes</h2>\n    {{#each order.notes}}\n    <div style=\"padding:5px; border-bottom:1px solid #a2a2a2\">On {{formatDate this.ts format=\"MM/DD/YYYY HH:mm\"}}<br>{{this.note}}</div>\n    {{/each}}\n    {{/if}}\n    \n  </div>",
                    "subject" : "Order Status Update"
                },
                "lactalisBroker" : {
                    "from" : "Lactalis Broker Portal",
                    "body" : "<div style=\"font-family:Arial, Helvetica, sans-serif\">\n\n  <p>An order has been placed and is pending your approval. Please follow the link below to review and approve / reject the order</p>\n  <p style=\"text-align: center; font-size: large;\"><a href=\"https://lactalis.bmghub.us/admin/order/get/{{order_id}}\">Click here to approve / reject order.</a></p>\n\n  <h2 style=\"background:#d2d2d2; padding:5px;\">Order Details</h2>\n  <div><b>Order Number:</b> {{order_id}}</div>\n  <div><b>Order Date:</b> {{formatDate order_date format=\"MM/DD/YYYY\"}}</div>\n  <div><b>Placed By:</b> {{account.first_name}} {{account.last_name}}</div>\n  {{#if account.phone}}<div><b>Phone:</b> {{account.phone}}</div>{{/if}}\n  {{#if account.email}}<div><b>Email:</b> {{account.email}}</div>{{/if}}\n\n  <h2 style=\"background:#d2d2d2; padding:5px;\">Delivery Details</h2>\n  <div><b>To:</b></div>\n  <div style=\"margin-bottom:20px;\">\n    {{fields.FNAME}} {{fields.LNAME}}<br>\n    {{#if fields.COMPANY}}{{fields.COMPANY}}<br>{{/if}}\n    {{fields.STREET}}<br>\n    {{#if fields.STREET_2}}{{fields.STREET_2}}<br>{{/if}}\n    {{fields.CITY}}, {{fields.STATE}} {{fields.ZIP}}<br>\n    {{#if fields.PHONE}}{{fields.PHONE}}<br>{{/if}}\n    {{#if fields.EMAIL}}{{fields.EMAIL}}<br>{{/if}}\n  </div>\n  {{#if fields.NEEDEDBY}}<div><b>Date Needed:</b> {{formatDate fields.NEEDEDBY format=\"MM/DD/YYYY\"}}</div>{{/if}}\n  {{#if fields.INSTRUCTIONS}}<div><b>Instructions:</b> {{fields.INSTRUCTIONS}}</div>{{/if}}\n\n</div>",
                    "subject" : "New Order Pending Approval"
                },
                "lactalisWarehouse" : {
                    "from" : "Lactalis Broker Portal",
                    "body" : "<div style=\"font-family:Arial, Helvetica, sans-serif\">\n    \n    <p style=\"text-align: center; font-size: large;\"><a href=\"https://lactalis.bmghub.us/admin/order/get/{{order._id}}\">Click here to print packing slip.</a></p>\n    \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Order Details</h2>\n    <div><b>Order Number:</b> {{order._id}}</div>\n    <div><b>Order Date:</b> {{formatDate order_date format=\"MM/DD/YYYY\"}}</div>\n    <div><b>Placed By:</b> {{account.first_name}} {{account.last_name}}</div>\n  {{#if account.phone}}<div><b>Phone:</b> {{account.phone}}</div>{{/if}}\n  {{#if account.email}}<div><b>Email:</b> {{account.email}}</div>{{/if}}\n  \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Delivery Details</h2>\n    <div><b>To:</b></div>\n    <div style=\"margin-bottom:20px;\">\n      {{order.fields.FNAME}} {{order.fields.LNAME}}<br>\n      {{#if order.fields.COMPANY}}{{order.fields.COMPANY}}<br>{{/if}}\n      {{order.fields.STREET}}<br>\n      {{#if order.fields.STREET_2}}{{order.fields.STREET_2}}<br>{{/if}}\n      {{order.fields.CITY}}, {{order.fields.STATE}} {{order.fields.ZIP}}<br>\n      {{#if order.fields.PHONE}}{{order.fields.PHONE}}<br>{{/if}}\n      {{#if order.fields.EMAIL}}{{order.fields.EMAIL}}<br>{{/if}}\n    </div>\n    {{#if order.fields.NEEDEDBY}}<div><b>Date Needed:</b> {{formatDate order.fields.NEEDEDBY format=\"MM/DD/YYYY\"}}</div>{{/if}}\n    {{#if order.fields.INSTRUCTIONS}}<div><b>Instructions:</b> {{order.fields.INSTRUCTIONS}}</div>{{/if}}\n  \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Items</h2>\n      <div style=\"width:100%; height:20px;\">\n        <div style=\"float:left; width:150px; font-weight: bold;\">QTY</div>\n        <div style=\"float:left; width:150px; font-weight: bold;\">SKU</div>\n        <div style=\"float:left; width:400px; font-weight: bold;\">Product</div>      \n      </div>\n      {{#each items}}\n      <div style=\"width:100%; min-width:700px; padding-top:5px; padding-bottom:5px;\">\n        <div style=\"float:left; width:150px;\">{{this.qty}}</div>\n        <div style=\"float:left; width:150px;\">{{this.fields.SKU}}</div>\n        <div style=\"float:left; width:400px;\">{{this.name}}</div>      \n      </div>\n      {{/each}}   \n      \n    {{#if order.notes}} \n    <h2 style=\"background:#d2d2d2; padding:5px;\">Notes</h2>\n    {{#each order.notes}}\n    <div>On {{formatDate this.ts format=\"MM/DD/YYYY HH:mm\"}}<br>{{this.note}}</div>\n    {{/each}}\n    {{/if}}\n    \n  </div>",
                    "subject" : "New Order Request"
                }
            },
            "itempromoter" : {
                "from" : "Lactalis Broker Portal",
                "body" : "<div style=\"margin:20px 0;\">{{message}}</div>\n<h2 style=\"background:#d2d2d2; padding:5px;\">Items</h2>\n{{#each items}}\n<div style=\"width:100%; min-width:700px; padding-top:5px; padding-bottom:5px; height:160px;\">\n    <div style=\"float:left; width:150px;\"><a href=\"{{this.url}}\"><img src=\"{{this.img}}\" style=\"width:100%;\"></a></div>\n    <div style=\"float:left; width:400px;\"><a href=\"{{this.url}}\">{{this.name}}</a></div>      \n</div>\n{{/each}}",
                "subject" : "Weekly Product Email"
            }
        },
        "config" : {
            "emailed_link_ttl" : 2192.0,
            "contacts" : {
                "from" : "<EMAIL>",
                "reply_to" : "<EMAIL>",
                "lactalis_warehouse" : "<EMAIL>",
                "approval_managers" : {
                    "AK" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "AL" : {
                        "name" : "Lara Freeland",
                        "email" : "<EMAIL>"
                    },
                    "AR" : {
                        "name" : "Lara Freeland",
                        "email" : "<EMAIL>"
                    },
                    "AZ" : {
                        "name" : "Larry Gelb",
                        "email" : "<EMAIL>"
                    },
                    "CA" : {
                        "name" : "Larry Gelb",
                        "email" : "<EMAIL>"
                    },
                    "CO" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "CT" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "DE" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "FL" : {
                        "name" : "John Canonico",
                        "email" : "<EMAIL>"
                    },
                    "GA" : {
                        "name" : "John Canonico",
                        "email" : "<EMAIL>"
                    },
                    "HI" : {
                        "name" : "Larry Gelb",
                        "email" : "<EMAIL>"
                    },
                    "IA" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "ID" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "IL" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "IN" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "KS" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "KY" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "LA" : {
                        "name" : "Lara Freeland",
                        "email" : "<EMAIL>"
                    },
                    "MA" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "MD" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "ME" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "MI" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "MN" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "MO" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "MS" : {
                        "name" : "Lara Freeland",
                        "email" : "<EMAIL>"
                    },
                    "MT" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "NC" : {
                        "name" : "John Canonico",
                        "email" : "<EMAIL>"
                    },
                    "ND" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "NE" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "NH" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "NJ" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "NM" : {
                        "name" : "Oli Guajardo",
                        "email" : "<EMAIL>"
                    },
                    "NV" : {
                        "name" : "Larry Gelb",
                        "email" : "<EMAIL>"
                    },
                    "NY" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "OH" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "OK" : {
                        "name" : "Oli Guajardo",
                        "email" : "<EMAIL>"
                    },
                    "OR" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "PA" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "RI" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "SC" : {
                        "name" : "John Canonico",
                        "email" : "<EMAIL>"
                    },
                    "SD" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "TN" : {
                        "name" : "Lara Freeland",
                        "email" : "<EMAIL>"
                    },
                    "TX" : {
                        "name" : "Oli Guajardo",
                        "email" : "<EMAIL>"
                    },
                    "UT" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "VA" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "VT" : {
                        "name" : "Melissa Racicot",
                        "email" : "<EMAIL>"
                    },
                    "WA" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    },
                    "WI" : {
                        "name" : "Jim Binner (Temporary, vacant)",
                        "email" : "<EMAIL>"
                    },
                    "WV" : {
                        "name" : "Keith Branham",
                        "email" : "<EMAIL>"
                    },
                    "WY" : {
                        "name" : "Shanan Pritchett",
                        "email" : "<EMAIL>"
                    }
                }
            }
        }
    },
    "account_tags" : {
        "fF01zd2W" : {
            "label" : "BMG",
            "default_home" : "/admin"
        },
        "qtK6Hdsv0l" : {
            "label" : "Admin",
            "default_home" : "/admin"
        },
        "cjNhcjen" : {
            "label" : "Brokers",
            "default_home" : "/portal"
        },
        "dsv0lqtK6H" : {
            "label" : "Lactalis 90%",
            "default_home" : "/portal"
        },
        "enjNcjhc" : {
            "label" : "Lactalis 100%",
            "default_home" : "/portal"
        }
    },
    "item_tags" : {
        "parishable" : {
            "label" : "Parishable",
            "searchable" : false
        },
        "nonparishable" : {
            "label" : "Non-Parishable",
            "searchable" : false
        },
        "aSfFCx" : {
            "label" : "CREAM CHEESE",
            "searchable" : true
        },
        "01zd2W" : {
            "label" : "Featured Items",
            "searchable" : true
        },
        "kPDprt" : {
            "label" : "Galbani Pro Team/Pizza Expo",
            "searchable" : true
        },
        "Hdsl" : {
            "label" : "Marketing Trends",
            "searchable" : true
        },
        "QQc0Ki" : {
            "label" : "Memos/Policies/Documents",
            "searchable" : true
        },
        "qtK6BG" : {
            "label" : "New Product Launch",
            "searchable" : true
        },
        "nbG5I8" : {
            "label" : "Nutritional Information",
            "searchable" : true
        },
        "oFSXtE" : {
            "label" : "Product - additional images",
            "searchable" : true
        },
        "Tm8kua" : {
            "label" : "Reports",
            "searchable" : true
        },
        "C0cMH1" : {
            "label" : "Brand > Black Diamond",
            "searchable" : true
        },
        "oMhJ4l" : {
            "label" : "Brand > Cracker Barrel",
            "searchable" : true
        },
        "Nhcjen" : {
            "label" : "Brand > Don Bernardo/Gran Capitán",
            "searchable" : true
        },
        "192R8N" : {
            "label" : "Brand > Galbani",
            "searchable" : true
        },
        "gEGbyG" : {
            "label" : "Brand > Hoffman's",
            "searchable" : true
        },
        "hh55Kw" : {
            "label" : "Brand > istara",
            "searchable" : true
        },
        "veU4UU" : {
            "label" : "Brand > Kraft",
            "searchable" : true
        },
        "K1zYa3" : {
            "label" : "Brand > Other",
            "searchable" : true
        },
        "ffWA0M" : {
            "label" : "Brand > Président",
            "searchable" : true
        },
        "o29bli" : {
            "label" : "Brand > Private Label",
            "searchable" : true
        },
        "FGKMer" : {
            "label" : "Brand > rondelé",
            "searchable" : true
        },
        "8DZ3DK" : {
            "label" : "Brand > Société",
            "searchable" : true
        },
        "foOqAP" : {
            "label" : "Brand > Valbreso",
            "searchable" : true
        },
        "q9iONh" : {
            "label" : "Product Category > Asiago",
            "searchable" : true
        },
        "D1W6pH" : {
            "label" : "Product Category > Bel Paese",
            "searchable" : true
        },
        "LdWLXt" : {
            "label" : "Product Category > Blue",
            "searchable" : true
        },
        "dcvkec" : {
            "label" : "Product Category > Brie",
            "searchable" : true
        },
        "RL4cgb" : {
            "label" : "Product Category > Butter",
            "searchable" : true
        },
        "QrcALM" : {
            "label" : "Product Category > Camembert",
            "searchable" : true
        },
        "X34TlL" : {
            "label" : "Product Category > Cheddar",
            "searchable" : true
        },
        "kBppWc" : {
            "label" : "Product Category > Emmental Swiss & Madrigal",
            "searchable" : true
        },
        "jBGEvl" : {
            "label" : "Product Category > Feta",
            "searchable" : true
        },
        "kEIr4f" : {
            "label" : "Product Category > Flavored Loaves",
            "searchable" : true
        },
        "XUfzxe" : {
            "label" : "Product Category > Fresh Mozzarella",
            "searchable" : true
        },
        "iKeVZk" : {
            "label" : "Product Category > Goat",
            "searchable" : true
        },
        "rcL6SC" : {
            "label" : "Product Category > Gorgonzola",
            "searchable" : true
        },
        "XhSZZB" : {
            "label" : "Product Category > Manchego & Other Spanish",
            "searchable" : true
        },
        "vDI7WX" : {
            "label" : "Product Category > Mascarpone",
            "searchable" : true
        },
        "BvXinh" : {
            "label" : "Product Category > Milk",
            "searchable" : true
        },
        "gBRePL" : {
            "label" : "Product Category > Mozzarella - all",
            "searchable" : true
        },
        "wlMzqO" : {
            "label" : "Product Category > Mozzarella - Premio (Blue)",
            "searchable" : true
        },
        "Nl72dl" : {
            "label" : "Product Category > Mozzarella - Red Pack",
            "searchable" : true
        },
        "lRqUr2" : {
            "label" : "Product Category > P'tit Basque",
            "searchable" : true
        },
        "4fZYPr" : {
            "label" : "Product Category > Parmesan",
            "searchable" : true
        },
        "IO0EJg" : {
            "label" : "Product Category > Provolone",
            "searchable" : true
        },
        "6udUJV" : {
            "label" : "Product Category > Ricotta",
            "searchable" : true
        },
        "9Uuebl" : {
            "label" : "Product Category > Romano",
            "searchable" : true
        },
        "bHHfGY" : {
            "label" : "Product Category > Roquefort",
            "searchable" : true
        },
        "caUQP5" : {
            "label" : "Product Category > Snack",
            "searchable" : true
        },
        "Ze6nId" : {
            "label" : "Product Category > Spreadable",
            "searchable" : true
        },
        "v6DUQd" : {
            "label" : "Product Category > Sheep's Milk",
            "searchable" : true
        },
        "Sibv6D" : {
            "label" : "Product Category > Yogurt",
            "searchable" : true
        },
        "hQVQdD" : {
            "label" : "Product Category > Whipped Cream",
            "searchable" : true
        },
        "KThQVv" : {
            "label" : "Level 1 > [BELP] BEL PAESE",
            "searchable" : true
        },
        "erewhu" : {
            "label" : "Level 1 > [BLUE] BLUE",
            "searchable" : true
        },
        "SUfrdX" : {
            "label" : "Level 1 > [BRIE] BRIE",
            "searchable" : true
        },
        "8pIlAs" : {
            "label" : "Level 1 > [BUTR] BUTTER",
            "searchable" : true
        },
        "hFMmaf" : {
            "label" : "Level 1 > [CHED] CHEDDAR",
            "searchable" : true
        },
        "8s6cAH" : {
            "label" : "Level 1 > [FETA] FETA",
            "searchable" : true
        },
        "nl5W88" : {
            "label" : "Level 1 > [FMOZ] FRESH MOZZARELLA",
            "searchable" : true
        },
        "x5zKw4" : {
            "label" : "Level 1 > [GOAT] GOAT",
            "searchable" : true
        },
        "DvHS35" : {
            "label" : "Level 1 > [GORG] GORGONZOLA",
            "searchable" : true
        },
        "5W0Ype" : {
            "label" : "Level 1 > [GRAT] GRATED",
            "searchable" : true
        },
        "NvSCSu" : {
            "label" : "Level 1 > [MASC] MASCARPONE",
            "searchable" : true
        },
        "SPe3eT" : {
            "label" : "Level 1 > [MILK] MILK",
            "searchable" : true
        },
        "ay6vmS" : {
            "label" : "Level 1 > [MISC] MISCELLANEOUS",
            "searchable" : true
        },
        "USOaB0" : {
            "label" : "Level 1 > [MOZP] MOZZARELLA PREMIUM",
            "searchable" : true
        },
        "4bR5BP" : {
            "label" : "Level 1 > [MOZZ] MOZZARELLA",
            "searchable" : true
        },
        "FZSc72" : {
            "label" : "Level 1 > [PARM] PARMESAN",
            "searchable" : true
        },
        "XtZVa4" : {
            "label" : "Level 1 > [PROV] PROVOLONE",
            "searchable" : true
        },
        "CANlqY" : {
            "label" : "Level 1 > [RIC] RICOTTA",
            "searchable" : true
        },
        "Z46ip5" : {
            "label" : "Level 1 > [RMNO] ROMANO IMPORTED",
            "searchable" : true
        },
        "hbJoYn" : {
            "label" : "Level 1 > [SHDI] SHRED ITALIAN",
            "searchable" : true
        },
        "7d40pY" : {
            "label" : "Level 1 > [SHDO] SHRED OTHER",
            "searchable" : true
        },
        "vvM8Sk" : {
            "label" : "Level 1 > [SHDP] SHRED PREMIUM",
            "searchable" : true
        },
        "L1ablr" : {
            "label" : "Level 1 > [SPEC] SPECIALTY IMPORTED",
            "searchable" : true
        },
        "4rEIIm" : {
            "label" : "Level 1 > [SPRD] SPREAD",
            "searchable" : true
        },
        "d6Q0DT" : {
            "label" : "Level 1 > [STRG] STRING",
            "searchable" : true
        },
        "QZgWkO" : {
            "label" : "Level 1 > [WHEY] WHEY",
            "searchable" : true
        },
        "nbX65o" : {
            "label" : "QuickLinks > Order Samples",
            "searchable" : true
        },
        "npsRgx" : {
            "label" : "QuickLinks > Product Catalogs",
            "searchable" : true
        },
        "CQxCou" : {
            "label" : "QuickLinks > Sysco Italian Platform",
            "searchable" : true
        },
        "ci9db0" : {
            "label" : "QuickLinks > Templates/Forms",
            "searchable" : true
        },
        "2zgmkR" : {
            "label" : "QuickLinks > Trade Marketing/Price Lists",
            "searchable" : true
        },
        "MFy65T" : {
            "label" : "Type > Announcements/Notifications",
            "searchable" : true
        },
        "K8XEiY" : {
            "label" : "Type > Category Reports & Sample Category Decks",
            "searchable" : true
        },
        "31nXXa" : {
            "label" : "Type > Category Syndicated Reports",
            "searchable" : true
        },
        "Hz2xLr" : {
            "label" : "Type > Industrial",
            "searchable" : true
        },
        "VOt9YW" : {
            "label" : "Type > Logos",
            "searchable" : true
        },
        "VAS16f" : {
            "label" : "Type > Marketing Trends/Resources",
            "searchable" : true
        },
        "6XNKFx" : {
            "label" : "Type > Non Commercial",
            "searchable" : true
        },
        "3CoF5I" : {
            "label" : "Type > Order Samples",
            "searchable" : true
        },
        "dbxOnb" : {
            "label" : "Type > POS Materials",
            "searchable" : true
        },
        "wxISmu" : {
            "label" : "Type > Powerpoint Presentations",
            "searchable" : true
        },
        "lQ3aM6" : {
            "label" : "Type > Product",
            "searchable" : true
        },
        "isdLfL" : {
            "label" : "Type > Product - Raw Shots",
            "searchable" : true
        },
        "30Q9OP" : {
            "label" : "Type > Promotional Items",
            "searchable" : true
        },
        "106Wdt" : {
            "label" : "Type > Rebates/$ Promotions",
            "searchable" : false
        },
        "ujIHPt" : {
            "label" : "Type > Recipes",
            "searchable" : true
        },
        "J85hgK" : {
            "label" : "Type > Sell Sheets",
            "searchable" : true
        },
        "a8lq1P" : {
            "label" : "Type > Spec Sheets",
            "searchable" : true
        },
        "UJkiYy" : {
            "label" : "Type > Templates/Forms",
            "searchable" : true
        },
        "NZDokp" : {
            "label" : "Type > Trade Marketing/Price Lists",
            "searchable" : true
        },
        "A5dlre" : {
            "label" : "Type > Videos",
            "searchable" : true
        }
    },
    "permissions" : {
        "items" : {
            "c" : [
                "fF01zd2W"
            ],
            "r" : true,
            "u" : [
                "fF01zd2W"
            ],
            "d" : [
                "fF01zd2W"
            ],
            "outbox" : true,
            "links" : true,
            "download" : true
        },
        "orders" : {
            "c" : [
                "fF01zd2W",
                "cjNhcjen",
                "dsv0lqtK6H",
                "enjNcjhc"
            ],
            "r" : [
                "fF01zd2W",
                "cjNhcjen",
                "dsv0lqtK6H",
                "enjNcjhc"
            ],
            "u" : [
                "fF01zd2W",
                "cjNhcjen",
                "dsv0lqtK6H",
                "enjNcjhc"
            ],
            "d" : [
                "fF01zd2W",
                "cjNhcjen",
                "dsv0lqtK6H",
                "enjNcjhc"
            ]
        },
        "adminui" : [
            "fF01zd2W"
        ],
        "org" : {
            "c" : [
                "fF01zd2W"
            ],
            "u" : [
                "fF01zd2W"
            ],
            "d" : [
                "fF01zd2W"
            ]
        }
    },
    "data_configs" : {
        "items" : {
            "meta" : {

            },
            "file_types" : {
                "base" : {
                    "label" : "File Asset"
                },
                "sellsheet" : {
                    "label" : "Sell Sheet"
                },
                "specsheet" : {
                    "label" : "Spec Sheet"
                }
            },
            "fields" : {
                "CASEDIM" : {
                    "type" : "text",
                    "label" : "Case L, W, H",
                    "searchable" : false
                },
                "CASEWEIGHT" : {
                    "type" : "text",
                    "label" : "Case Weight (Gross / Net)",
                    "searchable" : false,
                    "sortable" : false
                },
                "COA" : {
                    "type" : "text",
                    "label" : "Country of Origin",
                    "searchable" : false,
                    "sortable" : false
                },
                "CUBE" : {
                    "type" : "number",
                    "label" : "Cube",
                    "searchable" : false,
                    "sortable" : false
                },
                "DESCRIPTION" : {
                    "type" : "textarea",
                    "label" : "Description",
                    "searchable" : true
                },
                "FULLNAME" : {
                    "type" : "text",
                    "label" : "Full Name",
                    "searchable" : false,
                    "sortable" : false
                },
                "GTIN" : {
                    "type" : "text",
                    "label" : "GTIN",
                    "searchable" : true,
                    "sortable" : false
                },
                "ITEMUPC" : {
                    "type" : "text",
                    "label" : "Item UPC",
                    "searchable" : true,
                    "sortable" : false
                },
                "SKU" : {
                    "type" : "text",
                    "label" : "Resource No. (SKU)",
                    "searchable" : true,
                    "sortable" : true
                },
                "SHELFLIFE" : {
                    "type" : "text",
                    "label" : "Shelf Life (Total / At Shipping)",
                    "searchable" : false,
                    "sortable" : false
                },
                "TIExHEIGHT" : {
                    "type" : "text",
                    "label" : "Tie x High",
                    "searchable" : false,
                    "sortable" : false
                },
                "UNITSIZE" : {
                    "type" : "text",
                    "label" : "Unit Size",
                    "searchable" : false,
                    "sortable" : false
                },
                "WEIGHTMETHOD" : {
                    "type" : "text",
                    "label" : "Weight Method",
                    "searchable" : false,
                    "sortable" : false
                },
                "PRICETYPE" : {
                    "type" : "text",
                    "label" : "Price Type",
                    "sortable" : true
                }
            },
            "forms" : {
                "admin" : {
                    "fields" : [
                        {
                            "field_id" : "FULLNAME"
                        },
                        {
                            "field_id" : "SKU"
                        },
                        {
                            "field_id" : "ITEMUPC"
                        },
                        {
                            "field_id" : "GTIN"
                        },
                        {
                            "field_id" : "UNITSIZE"
                        },
                        {
                            "field_id" : "CASEDIM"
                        },
                        {
                            "field_id" : "CASEWEIGHT"
                        },
                        {
                            "field_id" : "CUBE"
                        },
                        {
                            "field_id" : "SHELFLIFE"
                        },
                        {
                            "field_id" : "TIExHEIGHT"
                        },
                        {
                            "field_id" : "WEIGHTMETHOD"
                        },
                        {
                            "field_id" : "PRICETYPE"
                        },
                        {
                            "field_id" : "COA"
                        },
                        {
                            "field_id" : "DESCRIPTION"
                        }
                    ],
                    "cta" : {
                        "label" : "Save Changes"
                    }
                }
            }
        },
        "orders" : {
            "fields" : {
                "FNAME" : {
                    "type" : "text",
                    "label" : "Recipient Name",
                    "placeholder" : "First Name (required)",
                    "required" : true,
                    "searchable" : true
                },
                "LNAME" : {
                    "type" : "text",
                    "label" : "Last Name",
                    "placeholder" : "Last Name (required)",
                    "sortable" : true,
                    "required" : true,
                    "searchable" : true
                },
                "EMAIL" : {
                    "label" : "Email",
                    "type" : "email",
                    "placeholder" : "Email (required)",
                    "sortable" : true,
                    "required" : true,
                    "searchable" : true
                },
                "STREET" : {
                    "label" : "Address",
                    "type" : "text",
                    "placeholder" : "Street Address (required)",
                    "required" : true
                },
                "STREET_2" : {
                    "label" : "Address 2",
                    "type" : "text",
                    "placeholder" : "Apartment, Suite, etc..",
                    "required" : false
                },
                "CITY" : {
                    "label" : "City",
                    "type" : "text",
                    "placeholder" : "City (required)",
                    "required" : true
                },
                "STATE" : {
                    "label" : "State",
                    "type" : "state",
                    "options" : "abbr",
                    "required" : true
                },
                "ZIP" : {
                    "label" : "Zipcode",
                    "type" : "text",
                    "placeholder" : "Zipcode (required)",
                    "required" : true
                },
                "PHONE" : {
                    "type" : "telephone",
                    "label" : "Phone"
                },
                "COMPANY" : {
                    "type" : "text",
                    "label" : "Company",
                    "sortable" : true,
                    "searchable" : true
                },
                "NEEDEDBY" : {
                    "type" : "date",
                    "label" : "Needed By"
                },
                "INSTRUCTIONS" : {
                    "type" : "textarea",
                    "placeholder" : "Instructions",
                    "style" : "height:200px;"
                },
                "TRACKING" : {
                    "type" : "text",
                    "label" : "Tracking Number",
                    "searchable" : true
                }
            },
            "forms" : {
                "portal_checkout" : {
                    "fields" : [
                        {
                            "field_id" : "FNAME"
                        },
                        {
                            "field_id" : "LNAME",
                            "labelless" : true
                        },
                        {
                            "field_id" : "EMAIL"
                        },
                        {
                            "field_id" : "COMPANY",
                            "placeholder" : "Recipients Company"
                        },
                        {
                            "field_id" : "STREET"
                        },
                        {
                            "field_id" : "STREET_2"
                        },
                        {
                            "field_id" : "CITY"
                        },
                        {
                            "field_id" : "STATE"
                        },
                        {
                            "field_id" : "ZIP"
                        },
                        {
                            "field_id" : "PHONE"
                        },
                        {
                            "type" : "paragraph",
                            "value" : "Due to COVID-19 and ongoing events, there has been an increase in shipping volumes and delivery times. Please allow 2 weeks lead time for your requested samples to be sent. This lead time may increase more during peak times or holidays. Samples may be delayed if the product is seasonal, is a special request, or is pending production. All shipping will occur on Monday through Wednesday of each week."
                        },
                        {
                            "field_id" : "NEEDEDBY"
                        },
                        {
                            "field_id" : "INSTRUCTIONS"
                        }
                    ],
                    "cta" : {
                        "label" : "Place Order"
                    }
                }
            },
            "statuses" : [
                {
                    "id" : "7yc3f",
                    "label" : "Submitted",
                    "default" : true,
                    "editable" : {
                        "items" : true,
                        "fields" : true
                    }
                },
                {
                    "id" : "c3yf7",
                    "label" : "Pending Approval",
                    "is_approval" : true,
                    "editable" : {
                        "items" : true,
                        "fields" : true
                    }
                },
                {
                    "id" : "wW9DK",
                    "label" : "Approved",
                    "editable" : {
                        "items" : false,
                        "fields" : true
                    }
                },
                {
                    "id" : "KJvHG",
                    "label" : "Processing"
                },
                {
                    "id" : "aXFdK",
                    "label" : "Complete",
                    "adjustInventory" : NumberInt(-1)
                },
                {
                    "id" : "EFDgh",
                    "label" : "Canceled",
                    "adjustInventory" : NumberInt(1)
                },
                {
                    "id" : "xYjGO",
                    "label" : "Rejected",
                    "adjustInventory" : NumberInt(1)
                }
            ],
            "events" : {
                "onCreate" : {
                    "script" : "/lactalis/newOrder.js",
                    "emailAccount" : true,
                    "emailRecipient" : false,
                    "adjustInventory" : true
                },
                "beforeUpdate" : {
                    "script" : ""
                },
                "onStatusChange" : {
                    "emailAccount" : true,
                    "emailRecipient" : true,
                    "adjustInventory" : true
                },
                "afterStatusChange" : {
                    "script" : "/lactalis/afterStatusChanged.js"
                },
                "onNote" : {
                    "emailAccount" : true
                },
                "beforeCreate" : "",
                "afterCreate" : ""
            }
        },
        "accounts" : {
            "fields" : {
                "FNAME" : {
                    "type" : "text",
                    "label" : "First Name",
                    "placeholder" : "First Name (required)",
                    "required" : true
                },
                "LNAME" : {
                    "type" : "text",
                    "label" : "Last Name",
                    "placeholder" : "Last Name (required)",
                    "required" : true
                },
                "STREET" : {
                    "label" : "Address",
                    "type" : "text",
                    "placeholder" : "Street Address (required)",
                    "required" : true
                },
                "STREET_2" : {
                    "label" : "Address 2",
                    "type" : "text",
                    "placeholder" : "Apartment, Suite, etc..",
                    "required" : false
                },
                "CITY" : {
                    "label" : "City",
                    "type" : "text",
                    "placeholder" : "City (required)",
                    "required" : true
                },
                "STATE" : {
                    "label" : "State",
                    "type" : "state",
                    "options" : "abbr",
                    "required" : true
                },
                "ZIP" : {
                    "label" : "Zipcode",
                    "type" : "text",
                    "placeholder" : "Zipcode (required)",
                    "required" : true
                },
                "PHONE" : {
                    "type" : "telephone",
                    "label" : "Phone",
                    "placeholder" : "Phone number (required)",
                    "required" : true
                },
                "COMPANY" : {
                    "type" : "text",
                    "label" : "Company",
                    "placeholder" : "Company name (required)",
                    "required" : true
                },
                "COMMENTS" : {
                    "type" : "textarea",
                    "label" : "Comments"
                }
            },
            "forms" : {
                "register" : {
                    "fields" : [
                        {
                            "label" : "Your Name",
                            "field_id" : "FNAME"
                        },
                        {
                            "field_id" : "LNAME",
                            "labelless" : true
                        },
                        {
                            "label" : "Email",
                            "type" : "email",
                            "required" : true,
                            "field_id" : "email",
                            "placeholder" : "Email (required)"
                        },
                        {
                            "field_id" : "COMPANY"
                        },
                        {
                            "field_id" : "PHONE"
                        },
                        {
                            "field_id" : "STREET"
                        },
                        {
                            "field_id" : "STREET_2",
                            "labelless" : true
                        },
                        {
                            "field_id" : "CITY"
                        },
                        {
                            "field_id" : "STATE"
                        },
                        {
                            "field_id" : "ZIP"
                        },
                        {
                            "field_id" : "COMMENTS"
                        }
                    ],
                    "cta" : {
                        "label" : "Submit Request"
                    }
                }
            }
        }
    }
}
