const mdb = require(process.cwd() + "/libs/mdb.js");
const fs = require("fs");
const response = require(process.cwd() + "/libs/response.js");
const common = require(process.cwd() + "/libs/common.js");
let shipstation = require(process.cwd()+"/libs/shipstation.js").create({
    "apiKey": process.env.SHIPSTATION_V1_API_KEY,
    "apiSecret": process.env.SHIPSTATION_V1_API_SECRET
});


module.exports={
    
    
    investigatePayload:async (req, res, next)=>{
        await mdb.client().collection("webhooks").insertOne({
            "src":"investigate",
            "headers":req.headers,
            "body":req.body,
            "created":new Date()
        });
        
        response.success(res);
    }, 

    syncRebateSubmission:async (req, res, next)=>{
        
        await mdb.client().collection("webhooks").insertOne({
            "src":"sync",
            "headers":req.headers,
            "body":req.body,
            "rebateId":req.params.rebateId,
            "created":new Date()
        });

        try{
            if(!req.params.rebateId){ response.error(res,"Missing id parameter"); return;}
            const REBATEID = mdb.objectId(req.params.rebateId.trim());                
            let rebate = await mdb.client().collection("rebates").findOne({ "_id":REBATEID });
            if(!rebate){ response.error(res,"No data"); return; }

            if(rebate.processor.syncWebhook && fs.existsSync(process.cwd()+rebate.processor.syncWebhook)){
                let processor = require(process.cwd()+rebate.processor.syncWebhook);
                if(processor && typeof processor.syncProcessRequest==="function"){
                    processor.syncProcessRequest(req, {
                        "REBATE":rebate,
                        "REBATEID":REBATEID
                    }).then(function(){
                        response.success(res);
                    }
                    ).catch(function(e){
                        console.log(e);
                        response.error(res,e);
                    });
                }else{
                    response.error(res,"No processor");
                }
            }else{
                response.error(res,"No processor");
            }


        }catch(e){
            console.log(e);
            response.error(res,e);
        }        
    },

/**********************************************************************
 * SHIP STATION WEBHOOK
 * When an order is shipped in ShipStation, it sends a webhook to this endpoint
 * The webhook payload contains the shipment details, which are used to update
 * the corresponding order in the "orders" collection in MongoDB.
 **********************************************************************/
    shipstationUpdate:async (req, res, next)=>{
        await mdb.client().collection("webhooks").insertOne({
            "src":"shipstation",
            "headers":req.headers,
            "body":req.body,
            "created":new Date()
        });

        try{
            if(req.body && req.body.resource_url){                
                let shipment = await shipstation.getShipments(common.queryStringToJSON(req.body.resource_url));
                if(shipment && shipment.length>0){
                    shipment = shipment[0];
                    if(shipment.orderNumber){
                        await mdb.client().collection("orders").updateOne({ "_id":mdb.objectId(shipment.orderNumber) },{
                            "$set":{
                                "shippment_id": shipment.shipmentId,
                                "shipping_status": shipment.shipmentStatus,
                                "tracking_number": shipment.trackingNumber,
                                "carrier": shipment.carrierCode,
                                "service": shipment.serviceCode,
                                "ship_date": shipment.createDate ? new Date(shipment.createDate) : null,
                                "status": "shipped",
                                "updatable":false
                            }
                        });                        
                    }                    
                }

            }

        }catch(e){
            console.log(e);            
        }

        response.success(res);
                
    }



}