const mdb = require(process.cwd() + "/libs/mdb.js");
const fs = require("fs");
const response = require(process.cwd() + "/libs/response.js");
const common = require(process.cwd() + "/libs/common.js");
let shipstation = require(process.cwd()+"/libs/shipstation.js").create({
    "apiKey": process.env.SHIPSTATION_V1_API_KEY,
    "apiSecret": process.env.SHIPSTATION_V1_API_SECRET
});


module.exports={
    
    
    investigatePayload:async (req, res, next)=>{
        await mdb.client().collection("webhooks").insertOne({
            "src":"investigate",
            "headers":req.headers,
            "body":req.body,
            "created":new Date()
        });
        
        response.success(res);
    }, 

    syncRebateSubmission:async (req, res, next)=>{
        
        await mdb.client().collection("webhooks").insertOne({
            "src":"sync",
            "headers":req.headers,
            "body":req.body,
            "rebateId":req.params.rebateId,
            "created":new Date()
        });

        try{
            if(!req.params.rebateId){ response.error(res,"Missing id parameter"); return;}
            if(!req.body || (typeof req.body=="object" && Object.keys(req.body).length===0)){ response.error(res,"Missing body"); return;}
            const REBATEID = mdb.objectId(req.params.rebateId.trim());                
            let rebate = await mdb.client().collection("rebates").findOne({ "_id":REBATEID });
            if(!rebate){ response.error(res,"No data"); return; }

            if(rebate.processor.syncWebhook && fs.existsSync(process.cwd()+rebate.processor.syncWebhook)){
                let processor = require(process.cwd()+rebate.processor.syncWebhook);
                if(processor && typeof processor.syncProcessRequest==="function"){
                    processor.syncProcessRequest(req, {
                        "REBATE":rebate,
                        "REBATEID":REBATEID
                    }).then(function(){
                        response.success(res);
                    }
                    ).catch(function(e){
                        console.log(e);
                        response.error(res,e);
                    });
                }else{
                    response.error(res,"No processor");
                }
            }else{
                response.error(res,"No processor");
            }


        }catch(e){
            console.log(e);
            response.error(res,e);
        }        
    },

/**********************************************************************
 * SHIP STATION WEBHOOK
 * When an order is shipped in ShipStation, it sends a webhook to this endpoint
 * The webhook payload contains the shipment details, which are used to update
 * the corresponding order in the "orders" collection in MongoDB.
 **********************************************************************/
    shipstationUpdate:async (req, res, next)=>{
        await mdb.client().collection("webhooks").insertOne({
            "src":"shipstation",
            "headers":req.headers,
            "body":req.body,
            "created":new Date()
        });
        let shipment;

        try{            
            if(req.body && req.body.resource_url){   
                let resourceURLS=[];            
                if(typeof req.body.resource_url==="string"){
                    resourceURLS.push(req.body.resource_url);
                }else{
                    resourceURLS = req.body.resource_url
                }
                
                for(let resourceURL of resourceURLS){
                    shipment = await shipstation.getShipments(common.queryStringToJSON(resourceURL));                    
                
                    if(shipment && typeof shipment.shipments!=="undefined" && shipment.shipments.length>0){
                        shipment = shipment.shipments[0];
                        if(shipment.orderKey){
                            let dataToSet={
                                "status":"aXFdK",
                                "shipping.shipped": true,
                                "shipping.status": ((shipment.shipmentStatus) ? shipment.shipmentStatus : null),
                                "shipping.tracking_number": ((shipment.trackingNumber) ? shipment.trackingNumber : null),
                                "shipping.carrier": shipment.carrierCode,
                                "shipping.service": shipment.serviceCode,
                                "shipping.ship_date": shipment.createDate ? new Date(shipment.createDate) : null,                                
                                "shipping.updatable":false,
                                "shipping.shippable":false
                            };

                            if(shipment.trackingNumber){ dataToSet["fields.TRACKING"]=shipment.trackingNumber; }

                            try{
                                await mdb.client().collection("orders").updateOne({ "_id":mdb.objectId(shipment.orderKey) },{
                                    "$set":dataToSet
                                });
                            }catch(e){
                                console.log(shipment);
                                console.log(e);
                            }

                        }                    
                    }
                }                                
            }

        }catch(e){
            console.log(shipment);
            console.log(e);  
                      
        }

        response.success(res);
                
    }



}