const mdb = require("../libs/mdb.js");
const fs = require("fs");
const response = require("../libs/response.js");


module.exports={
    
    
    syncFormInvestigate:async (req, res, next)=>{
        await mdb.client().collection("webhooks").insertOne({
            "src":"sync-investigate",
            "headers":req.headers,
            "body":req.body,
            "created":new Date()
        });
        
        response.success(res);
    }, 

    syncRebateSubmission:async (req, res, next)=>{
        
        await mdb.client().collection("webhooks").insertOne({
            "src":"sync",
            "headers":req.headers,
            "body":req.body,
            "rebateId":req.params.rebateId,
            "created":new Date()
        });

        try{
            if(!req.params.rebateId){ response.error(res,"Missing id parameter"); return;}
            const REBATEID = mdb.objectId(req.params.rebateId.trim());                
            let rebate = await mdb.client().collection("rebates").findOne({ "_id":REBATEID });
            if(!rebate){ response.error(res,"No data"); return; }

            if(rebate.processor.syncWebhook && fs.existsSync(process.cwd()+rebate.processor.syncWebhook)){
                let processor = require(process.cwd()+rebate.processor.syncWebhook);
                if(processor && typeof processor.syncProcessRequest==="function"){
                    processor.syncProcessRequest(req, {
                        "REBATE":rebate,
                        "REBATEID":REBATEID
                    }).then(function(){
                        response.success(res);
                    }
                    ).catch(function(e){
                        console.log(e);
                        response.error(res,e);
                    });
                }else{
                    response.error(res,"No processor");
                }
            }else{
                response.error(res,"No processor");
            }


        }catch(e){
            console.log(e);
            response.error(res,e);
        }        
    },



}