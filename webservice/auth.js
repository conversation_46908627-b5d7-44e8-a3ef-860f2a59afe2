const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const COLLECTION = "orgs";
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");

module.exports={
    verify:async (req, res, next)=>{
        if(req.headers["x-api-key"]){
            req.headers["x-api-key"] = req.headers["x-api-key"].trim();
            let org = await mdb.client().collection(COLLECTION).findOne({"api_key":req.headers["x-api-key"]});
            if(org){
                req.ORG = org;
                next();
            }else{
                response.fail(res, "invalid api key");
            }
        }else{
            response.fail(res, "missing api key");
        }        
    }

}