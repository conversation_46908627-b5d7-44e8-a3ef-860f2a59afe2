const mdb = require("../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require("../libs/aws.js");
const HB = require("../libs/handlebars.js");
const activityModel = require("../models/model_activity.js");
const _ = require("lodash");
const common = require("../libs/common.js");

module.exports={

/******************************************************************************
    CREATE
*******************************************************************************/
    create:function(reqBody, SESSION , org){        
        return new Promise(async (resolve, reject) => {            
            if(!reqBody.items){
                reject("We could not place your order because your cart is empty.");                
            }
        
            // Find the status to use for a new order
            let status = ((typeof reqBody.status==="string") ? _.find(org.data_configs.orders.statuses,{"id":reqBody.status}) : _.find(org.data_configs.orders.statuses,{"default":true}));
            
            // ---- Find the account that placed order ---------------------------------------
            let orderAccount = await mdb.client().collection("accounts").findOne({"_id":SESSION.account_id});

            // Make sure we cast the form fields as the correct type
            for(let f of Object.keys(reqBody.fields)){    
                switch(org.data_configs.orders.fields[f].type){
                    case "date":
                        reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
                    break;
                    case "number":
                        reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
                    break;
                    case "file":
                        for(let fileObj of reqBody.fields[f]){
                            
                        }
                         /*try{
                            let newObjectId = await objectStorage.create(Object.assign({ "meta": {
                                "rebate_id":submission.rebate_id,
                                "formflow_submissions_id":mdb.objectId(req.params.id)
                            }, 
                                "org_id":req.ORG._id,
                                "account_id":req.SESSION.account_id
                            },dataToSave[FIELDNAME]));
                
                            if(newObjectId){
                                dataToReturn[FIELDNAME].push(newObjectId);
                            }
                        }catch(e){
                            console.log(dataToSave);
                            console.log("Sahlen Proof of Purchase before save",e);    
                        }*/
                    break;
                }
            };
                    
            // Construct the data to save
            let dataToSave={
                "org_id":SESSION.org_id,
                "account_id":SESSION.account_id,            
                "created":new Date(),
                "updated":new Date(),
                "status":status.id,
                "fields":reqBody.fields,
                "items":reqBody.items
            };

            if(typeof reqBody.meta==="object"){
                dataToSave.meta = reqBody.meta;
            }

            if(typeof reqBody.approved_by==="object"){
                dataToSave.approved_by = reqBody.approved_by;
            }
            
            if(typeof reqBody.limited_to_tags!=="undefined"){
                dataToSave.limited_to_tags = reqBody.limited_to_tags;
            }

            if(typeof reqBody.warehouse==="string"){
                dataToSave.warehouse = reqBody.warehouse;
            }
        
            let orderTemplateData = Object.assign({"account":orderAccount},dataToSave);

            // BEFORE CREATE - CUSTOM EVENT -----------------------------------------
            if(typeof org.data_configs.orders.events.beforeCreate==="string" && org.data_configs.orders.events.beforeCreate.length>0){
                let beforeCreateReturn = await require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.beforeCreate)({
                "reqBody":reqBody,
                "org":org,
                "dataToSave":dataToSave,
                "orderTemplateData":orderTemplateData
                });
        
                if(beforeCreateReturn && beforeCreateReturn.dataToSave){
                    dataToSave = beforeCreateReturn.dataToSave;
                }
            }
        
            // Save new order
            let newOrder = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
                    
            if(newOrder && newOrder.insertedId){                        
                // Clear Users Cart
                await mdb.client().collection("accounts").updateOne({"_id":SESSION.account_id},{"$set":{"cart":{}}});
            
                // ********** Check the event configs ************
            
                // ADJUST INVENTORY ---------------------------
                let okToAdjustInventory = false;
                if (org.server.scripts && org.server.scripts.orders.orderInventoryController && typeof org.server.scripts.orders.orderInventoryController==="string"){
                    try{
                        let inventoryResponse = await require(process.cwd()+"/custom_processors"+org.server.scripts.orders.orderInventoryController)({
                            "current_status_id":null,
                            "new_status_id":status.id,
                            "reqBody":reqBody,
                            "order_id":newOrder.insertedId,
                            "session":SESSION,
                            "org":org
                        });

                        if(inventoryResponse && inventoryResponse.adjustInventory){
                            okToAdjustInventory = true;
                        }

                    }catch(e){
                        console.log(e);                        
                    }
                
                }else if(org.data_configs.orders.events.onCreate && org.data_configs.orders.events.onCreate.adjustInventory){      
                    okToAdjustInventory = true;
                }

                if(okToAdjustInventory){
                    for(let itemId of Object.keys(reqBody.items)){                                                
                        await mdb.client().collection("inventory").insertOne({
                            "item_id":mdb.objectId(itemId),
                            "order_id":newOrder.insertedId,
                            "account":{
                                "_id":SESSION.account_id,
                                "email":SESSION.email
                            },
                            "qty":reqBody.items[itemId].qty*-1,
                            "details":'',
                            "ts":new Date()
                        });                        
                    }
                }
            
            
                // ASSEMBLE ORDER ITEMS FOR TEMPLATES ---------------------------
                let orderItems=[];
                if(org.data_configs.orders.events.onCreate.emailAccount || org.data_configs.orders.events.onCreate.emailRecipient){
                    let orgItems = JSON.parse(JSON.stringify(await mdb.client().collection("items").find({"org_id":SESSION.org_id,"actions.order":true},{"projection":{"name":true, "image":true, "fields":true, "inventory":true, "tags":true}}).toArray()));      
                    Object.keys(reqBody.items).forEach((itemId)=>{    
                        //orderItems.push(Object.assign({"qty":Number(reqBody.items[itemId].qty)},_.find(orgItems, {"_id":itemId})))    
                        orderItems.push(Object.assign({},reqBody.items[itemId],_.find(orgItems, {"_id":itemId})))    
                    });
                    orgItems=null;
                }
            
                // ASSEMBLE ORDER OBJ FOR TEMPLATES ---------------------------
                orderTemplateData._id = newOrder.insertedId;

                // SEND EMAIL TO ACCOUNT---------------------------
                if(org.data_configs.orders.events.onCreate.emailAccount && org.server.templates.order.newToAccount){
                    aws.email({
                        "to":SESSION.email,
                        "from": `${org.server.templates.order.newToAccount.from} <${org.server.config.contacts.from}>`,
                        "subject": HB(org.server.templates.order.newToAccount.subject.trim(),{"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.newToAccount.body,{"order":orderTemplateData, "items":orderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });      
                }
            
                // SEND EMAIL TO RECIPIENT ---------------------------
                if(org.data_configs.orders.events.onCreate.emailRecipient && reqBody.fields.EMAIL && org.server.templates.order.newToRecipient){
                    aws.email({
                        "to":reqBody.fields.EMAIL.split(",").map(function(s){ return s.trim(); }),
                        "reply":SESSION.email,
                        "from": `${org.server.templates.order.newToRecipient.from} <${org.server.config.contacts.from}>`,
                        "subject": HB(org.server.templates.order.newToRecipient.subject.trim(),{"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.newToRecipient.body,{"order":orderTemplateData, "items":orderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });
                }
            
                // LOG ACTIVITY ---------------------------
                let activityDataToLog=[{
                    "org_id":SESSION.org_id,          
                    "account_id":SESSION.account_id,
                    "event":{
                        "system":"order",
                        "action":"placed"   
                    },          
                    "data":{
                        "order_id":orderTemplateData._id,
                        "items":dataToSave.items
                    }
                    }];
            
                for(let orderedItem of orderItems){
                    activityDataToLog.push({
                    "org_id":SESSION.org_id,          
                    "account_id":SESSION.account_id,
                    "event":{
                        "system":"item",
                        "action":"ordered"   
                    },          
                    "data":{
                        "item_name":orderedItem.name,
                        "item_id":mdb.objectId(orderedItem._id),
                        "qty":orderedItem.qty
                    }
                    });  

                    // Send out inventory notifications
                    let inventoryRemaining = await mdb.client().collection("inventory").aggregate([
                        {"$match":{"item_id":mdb.objectId(orderedItem._id)}},
                        {"$group":
                            {"_id":null,"total":{"$sum":"$qty"}}
                        }
                    ]).toArray();
                    

                    // Inventory Restock Alert =====================================================================================================
                    let inventoriedTags = [];                     
                    Object.keys(org.item_tags).forEach((tagId)=>{
                        if(org.item_tags[tagId].inventoried){
                            inventoriedTags.push(tagId);
                        }
                    });                   
                                    
                    if(
                        _.intersection(inventoriedTags, orderedItem.tags).length>0
                        && inventoryRemaining && inventoryRemaining.length>0 
                        && inventoryRemaining[0].total<=orderedItem.inventory.restock_level
                    ){
                        aws.email({
                            "to":org.server.config.contacts.bmg_warehouse,
                            "from": `${org.server.templates.inventoryAlert.from} <${org.server.config.contacts.from}>`,
                            "subject": HB(org.server.templates.inventoryAlert.subject.trim(),{"item":orderedItem, "inventoryTotal":inventoryRemaining[0].total}),
                            "body":HB(org.server.templates.inventoryAlert.body,{"item":orderedItem, "inventoryTotal":inventoryRemaining[0].total})
                        }).then(r=>{                
                        }).catch(e=>{
                            console.log("AWS EMAIL error",e)        
                        });  
                    }

                };
                
                activityModel.log(activityDataToLog);

                // AFTER CREATE CUSTOM EVENT -------------------------------------------
                if(typeof org.data_configs.orders.events.afterCreate==="object" && typeof org.data_configs.orders.events.afterCreate.script==="string" && org.data_configs.orders.events.afterCreate.script.length>0){
                    console.log("AFTER CREATE",org.data_configs.orders.events.afterCreate);
                    let afterCreateReturn = await require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.afterCreate.script)({
                        "reqBody":reqBody,
                        "org":org,
                        "newOrderId":newOrder.insertedId,
                        "orderTemplateData":orderTemplateData,
                        "orderItems":orderItems
                    });
                }else{
                    console.log("No AFTER CREATE custom processor found");                    
                }
            
            
                // RESPOND TO USER ---------------------------                          
                resolve(orderTemplateData);
            }else{
                reject("Failed to place your order.");
            }
        });
    },

/******************************************************************************
    UPDATE
*******************************************************************************/
    update:function(reqBody, reqParams, SESSION , org){
        return new Promise(async (resolve, reject) => {

            console.log("UPDATE ORDER", reqBody, reqParams);
            
            // Make sure we cast the form fields as the correct type
            Object.keys(reqBody.fields).forEach((f)=>{    
                switch(org.data_configs.orders.fields[f].type){
                case "date":
                    reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
                break;
                case "number":
                    reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
                break;
                }
            });

            // Construct the data to save
            let dataToSave={    
                "updated":new Date()
            };

            if(typeof reqBody.fields==="object"){
                dataToSave.fields=reqBody.fields;
            }

            if(typeof reqBody.items==="object"){
                dataToSave.items=reqBody.items;
            }

            let dataToMatch = {"_id":mdb.objectId(reqParams.orderId)};

            if(!common.hasPermissions(org, SESSION.tags,["orders.adm"])){
                dataToMatch.account_id = SESSION.account_id;
            }            

            let dataToPush={"notes":{"note":"Updated by: "+SESSION.email,"ts":new Date()}};

            let currentOrder = await mdb.client().collection(COLLECTION).findOne(dataToMatch);            
            let updatedOrder = await mdb.client().collection(COLLECTION).updateOne(dataToMatch, {"$set":dataToSave,"$push":dataToPush});           

            if(updatedOrder){

                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                //If this update has items, then update the cart and inventory
                console.log(dataToSave.items, dataToMatch.account_id)
                if(dataToSave.items){

                    // Clear Users Cart
                    await mdb.client().collection("accounts").updateOne({"_id":SESSION.account_id},{"$set":{"cart":{}}});

                    //If the ON CREATED ADJUSTED INVENTORY, THEN WE NEED TO RE-ADJUST IT
                    if(org.data_configs.orders.events.onCreate.adjustInventory){ 
                        //First we have to get the current order items so we can adjust any inventory that is being affected. 
                        if(!currentOrder){
                            reject("Failed to update your order. Code (UO100)");
                        }else{                            
                            for(let itemId of Object.keys(currentOrder.items)){                                        
                                await mdb.client().collection("inventory").insertOne({
                                    "item_id":mdb.objectId(itemId),
                                    "order_id":currentOrder._id,
                                    "account":{
                                        "_id":SESSION.account_id,
                                        "email":SESSION.email
                                    },
                                    "qty":Math.abs(currentOrder.items[itemId].qty),
                                    "details":`Auto-adjusting inventory step 1. Put item qty back into inventory.`,
                                    "ts":new Date()
                                });
                            };
                        }
                        // Second, adjust inventory for items in updated order                        
                        for(let itemId of Object.keys(dataToSave.items)){                            
                            await mdb.client().collection("inventory").insertOne({
                                    "item_id":mdb.objectId(itemId),
                                    "order_id":currentOrder._id,
                                    "account":{
                                        "_id":SESSION.account_id,
                                        "email":SESSION.email
                                    },
                                    "qty":Math.abs(dataToSave.items[itemId].qty)*-1,
                                    "details":`Auto-adjusting inventory step 2. Remove new item qty from inventory.`,
                                    "ts":new Date()
                            });
                        };
                    }
                }
                

                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                // LOG ACTIVITY
                activityModel.log([{
                    "org_id":SESSION.org_id,          
                    "account_id":SESSION.account_id,
                    "event":{
                        "system":"order",
                        "action":"upated"   
                    },          
                    "data":{
                        "order_id":mdb.objectId(reqParams.orderId)
                    }
                }]);



                resolve();            
            
            }else{
                reject("No changes made");
            }

        })
    },

/******************************************************************************
    CHANGE STATUS
*******************************************************************************/
    changeStatus:function(reqBody, reqParams, SESSION , org){
        return new Promise(async (resolve, reject) => {
            // Construct the data to save
            let dataToSave={"$set":{}};

            dataToSave["$set"]["updated"] = new Date();

            if(typeof reqBody.status==="string" && reqBody.status.length>0){
                dataToSave["$set"].status=reqBody.status;
            }else{
                reject("Missing status")
            }
            
            let currentOrder = await mdb.client().collection("orders").aggregate([
                {"$match":{"_id": mdb.objectId(reqParams.orderId), "org_id":org._id }},
                {"$limit":1},
                { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "account" } }
              ]).toArray();
          
              if(currentOrder && currentOrder.length>0){
                currentOrder = currentOrder[0];
                if(currentOrder.account && currentOrder.account.length>0){
                    currentOrder.account = currentOrder.account[0];
                }    
              }

            let currentOrderStatus = _.find(org.data_configs.orders.statuses,{"id":currentOrder.status});

            let dataToMatch={"_id":mdb.objectId(reqParams.orderId)};
            if(!common.hasPermissions(org, SESSION.tags,["orders.adm"])){
                dataToMatch.account_id = SESSION.account_id;
            }
                    
            let updatedOrder = await mdb.client().collection(COLLECTION).updateOne(dataToMatch, dataToSave);
            
            if(updatedOrder && updatedOrder.modifiedCount>0){
                let newStatus = _.find(org.data_configs.orders.statuses,{"id":reqBody.status});
                let newStatusLabel = "N/A";

                // STATUS LABEL -------------------------------------------------------------------
                if(newStatus && newStatus.label){ newStatusLabel = newStatus.label; }
                           
                /*if(
                    (typeof currentOrderStatus.adjustInventory.direction==="undefined" || typeof newStatus.adjustInventory.direction==="undefined")
                    || (currentOrderStatus.adjustInventory.direction!==newStatus.adjustInventory.direction)
                ){*/
                                        

                // ADJUST INVENTORY ---------------------------
                let okToAdjustInventory = false;
                let inventoryControllerResponse=null;
                let inventoryQtyDirection=0;

                if (org.server.scripts && org.server.scripts.orders.orderInventoryController && typeof org.server.scripts.orders.orderInventoryController==="string"){
                    try{
                        inventoryControllerResponse = await require(process.cwd()+"/custom_processors"+org.server.scripts.orders.orderInventoryController)({
                            "current_status_id":currentOrder.status,
                            "new_status_id":reqBody.status,                            
                            "org":org,
                            "order_id":currentOrder._id,
                            "session":SESSION
                        });

                        if(inventoryControllerResponse && inventoryControllerResponse.adjustInventory){
                            okToAdjustInventory = inventoryControllerResponse.adjustInventory;
                            inventoryQtyDirection=inventoryControllerResponse.direction
                        }

                    }catch(e){
                        console.log(e);                        
                    }
                }else if(newStatus.adjustInventory && typeof newStatus.adjustInventory.direction!=="undefined" && newStatus.adjustInventory.onStatusChange){
                    okToAdjustInventory = true;
                    inventoryQtyDirection=newStatus.adjustInventory.direction;
                }  

                if(okToAdjustInventory){
                    for(let itemId of Object.keys(currentOrder.items)){                        
                        await mdb.client().collection("inventory").insertOne({
                            "item_id":mdb.objectId(itemId),
                            "order_id":currentOrder._id,
                            "account":{
                                "_id":SESSION.account_id,
                                "email":SESSION.email
                            },
                            "qty":Math.abs(currentOrder.items[itemId].qty) * inventoryQtyDirection,
                            "details":`Auto-adjusting inventory for status change from ${currentOrderStatus.label} to ${newStatusLabel}.`,
                            "ts":new Date()
                        });                                                                    
                    } 
                }

                                                                               
                //}                

                // ORDER ITEMS ========================================================================================                
                let itemsInOrder=[];
                Object.keys(currentOrder.items).forEach(function(itemId){ itemsInOrder.push(mdb.objectId(itemId));  });
        
                let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemsInOrder}}).toArray();
        
                orderItems.forEach(function(orderItem, i){
                    orderItems[i].qty = currentOrder.items[orderItem._id.toString()].qty;
                    orderItems[i].unit = currentOrder.items[orderItem._id.toString()].unit;
                });

                        
                let emailAccount=true, emailRecipient=true;
                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                // CUSTOM AFTER STATUS CHANGE HANDLER
                if(typeof org.data_configs.orders.events.afterStatusChange==="object" && org.data_configs.orders.events.afterStatusChange.script && org.data_configs.orders.events.afterStatusChange.script.length>0){
                    let customResponse = await require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.afterStatusChange.script)({
                        "order":currentOrder,
                        "orderItems":orderItems,
                        "org":org,
                        "newStatus":reqBody.status,
                        "newStatusLabel":newStatusLabel,
                        "session":SESSION
                    });

                    if(customResponse && typeof customResponse.emailAccount!=="undefined"){
                        emailAccount=customResponse.emailAccount;
                    }

                    if(customResponse && typeof customResponse.emailRecipient!=="undefined"){
                        emailRecipient=customResponse.emailRecipient;
                    }
                    
                }

                // SEND EMAIL TO ACCOUNT ---------------------------------
                if(emailAccount && org.data_configs.orders.events.onStatusChange.emailAccount && org.server.templates.order.statusChangedToAccount){
                    aws.email({
                        "to":currentOrder.account.email,
                        "from": `${org.server.templates.order.statusChangedToAccount.from} <${org.server.config.contacts.from}>`,
                        "subject": HB(org.server.templates.order.statusChangedToAccount.subject.trim(),{"order":currentOrder, "newStatus":newStatusLabel}),
                        "body":HB(org.server.templates.order.statusChangedToAccount.body,{"order":currentOrder, "newStatus":newStatusLabel, "items":orderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });      
                }
                
                // SEND EMAIL TO RECIPIENT -------------------------------
                if(emailRecipient && org.data_configs.orders.events.onStatusChange.emailRecipient && currentOrder.fields.EMAIL && org.server.templates.order.statusChangedToRecipient){
                    aws.email({
                        "to":common.splitIt(currentOrder.fields.EMAIL),
                        "reply":currentOrder.account.email,
                        "from": `${org.server.templates.order.statusChangedToRecipient.from} <${org.server.config.contacts.from}>`,
                        "subject": HB(org.server.templates.order.statusChangedToRecipient.subject.trim(),{"order":currentOrder, "newStatus":newStatusLabel}),
                        "body":HB(org.server.templates.order.statusChangedToRecipient.body,{"order":currentOrder, "newStatus":newStatusLabel, "items":orderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });
                }
                
                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                // LOG ACTIVITY
                activityModel.log([{
                    "org_id":SESSION.org_id,          
                    "account_id":SESSION.account_id,
                    "event":{
                        "system":"order",
                        "action":"status_change"   
                    },          
                    "data":{
                        "order_id":mdb.objectId(reqParams.orderId),
                        "from":currentOrder.status,
                        "to":dataToSave["$set"].status
                    }
                }]);

                resolve();

            }else{                
                reject();

            }

        });
    },

/******************************************************************************
    ADD NOTE
*******************************************************************************/
    addNote:function(reqBody, reqParams, SESSION , org){
        return new Promise(async (resolve, reject) => {
            // Construct the data to save
            let dataToSave={    
                "updated":new Date()
            };
            let dataToPush={};

            if(typeof reqBody.note==="string" && reqBody.note.length>0){
                dataToPush={"notes":{"note":reqBody.note,"ts":new Date(),"by":SESSION.email}};
            }else{
                reject("Missing note");
            }

            let dataToMatch={"_id":mdb.objectId(reqParams.orderId)};
            if(!common.hasPermissions(org, SESSION.tags,["orders.adm"])){
                dataToMatch.account_id = SESSION.account_id;
            }
            

            let updatedOrder = await mdb.client().collection(COLLECTION).updateOne(dataToMatch, {"$set":dataToSave,"$push":dataToPush});


            if(updatedOrder && updatedOrder.modifiedCount>0){

                let currentOrder = await mdb.client().collection("orders").aggregate([
                    {"$match":{"_id": mdb.objectId(reqParams.orderId), "org_id":org._id }},
                    {"$limit":1},
                    {"$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "account" } }
                  ]).toArray();
              
                if(currentOrder && currentOrder.length>0){
                    currentOrder = currentOrder[0];
                    if(currentOrder.account && currentOrder.account.length>0){
                        currentOrder.account = currentOrder.account[0];
                    }    
                }
                
                let emailAccount=true, emailRecipient=true;
                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                // CUSTOM ON CHANGE HANDLER
                if(typeof org.data_configs.orders.events.onNote==="object" && org.data_configs.orders.events.onNote.script && org.data_configs.orders.events.onNote.script.length>0){
                    let customResponse = await require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.onNote.script)({
                        "order":currentOrder,
                        "org":org,
                        "note":reqBody.note
                    });

                    if(customResponse && typeof customResponse.emailAccount!=="undefined"){
                        emailAccount=customResponse.emailAccount;
                    }

                    if(emailRecipient && typeof customResponse.emailRecipient!=="undefined"){
                        emailRecipient=customResponse.emailRecipient;
                    }
                }

                // SEND EMAIL TO ACCOUNT ---------------------------------
                if(emailAccount && org.data_configs.orders.events.onNote.emailAccount && org.server.templates.order.onNoteToAccount){
                    aws.email({
                        "to":currentOrder.account.email,
                        "from": `${org.server.templates.order.onNoteToAccount.from} <${org.server.config.contacts.from}>`,
                        "subject": org.server.templates.order.onNoteToAccount.subject.trim(),
                        "body":HB(org.server.templates.order.onNoteToAccount.body, {"order":currentOrder})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });      
                }
            
                // SEND EMAIL TO RECIPIENT -------------------------------
                if(emailRecipient && org.data_configs.orders.events.onNote.emailRecipient && currentOrder.fields.EMAIL && org.server.templates.order.onNoteToRecipient){
                    aws.email({
                        "to":currentOrder.fields.EMAIL,
                        "reply":currentOrder.account.email,
                        "from": `${org.server.templates.order.onNoteToRecipient.from} <${org.server.config.contacts.from}>`,
                        "subject": org.server.templates.order.onNoteToRecipient.subject.trim(),
                        "body":HB(org.server.templates.order.onNoteToRecipient.body,{"order":currentOrder})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });
                }

                
                ////////////////////////////////////////////////////////////////////////////////////////////////////////
                // LOG ACTIVITY
                activityModel.log([{
                    "org_id":SESSION.org_id,          
                    "account_id":SESSION.account_id,
                    "event":{
                        "system":"order",
                        "action":"note"   
                    },          
                    "data":{
                        "order_id":mdb.objectId(reqParams.orderId)
                    }
                }]);

                resolve();

            }else{
                reject("On failed to update.");
            }

        });
    }

};