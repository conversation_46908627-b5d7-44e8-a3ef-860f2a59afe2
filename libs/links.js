const mdb = require("../libs/mdb.js");
const dayjs = require('dayjs');
const COLLECTION = "links";

module.exports={
  create:async function(params){
    let dataToSave={
        "org_id":params.org_id,
        "account_id":params.account_id,
        "item_id":params.item_id,
        "created":new Date()   
      };
    if(params.expire){
      dataToSave.expired = dayjs().add(params.expire, 'hour').toDate()
    }    
    let newLink = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
    if(!newLink || !newLink.insertedId){
      return false;
    }else{
      return newLink.insertedId;
    }
  },

  get:async function(id){
    let link = await mdb.client().collection(COLLECTION).findOne({_id:mdb.objectId(id), "$or":[{"expired":{"$exists":false}}, {"expired":{"$gt":new Date()}}]});
    if(!link){
      return false;
    }else{
      return link;
    }
  }
  
};