module.exports={
  success:(res, data=null) => {  
    res.status(200).json(data);
  },
  fail:(res, msg) => {
    res.status(400).send(((msg) ? msg : "bad request"));    
  },
  missing:(res, msg) => {
    res.status(404).send(((msg) ? msg : "no data"));    
  },
  error:(res, msg) => {
    res.status(500).send(((msg) ? msg : "no data"));
  },
  forbidden:(res)=>{
    res.status(403).send();
  },
  noData:(res, msg)=>{
    res.status(204).send(((msg) ? msg : ""));
  }
};