const cron = require("cron").CronJob;
const dayjs = require("dayjs");

let JOBS={
    "getzAccountDisabler":{
        "job":null,
        "schedule":"0 0 4 * * *",
        "script":"/shared/accountdisabler.js",
        "params":{
            "orgId":"64cbc6e4591c6f6f7599809b"
        },
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":false
    },
    "lactalisSyndigo":{
        "job":null,
        "schedule":"0 30 4 * * *",
        "script":"/lactalis/syndigo/runner.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":true,
        "onceable":true
    },
    "upstateSyndigo":{
        "job":null,
        "schedule":"0 15 4 * * *",
        "script":"/upstate/syndigo/runner.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":true
    },
    "lactalisRebateNudges":{
        "job":null,
        "schedule":"0 0 3 * * *",
        "script":"/lactalis/rebates/nudges/switch.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":true,
        "onceable":true
    },
    "tiggerExports":{
        "job":null,
        "schedule":"0 0 * * * *",
        "script":"/shared/triggerExport.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":false
    },
    "deleteExports":{
        "job":null,
        "schedule":"0 0 5 * * *",
        "script":"/shared/deleteExportedFile.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":true
    },
    "pdfChecker":{
        "job":null,
        "schedule":"0 5 * * * *",
        "script":"/shared/pdfChecker.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":true,
        "onceable":true
    },
    "sahlenRebateNudges30":{
        "job":null,
        "schedule":"0 0 0 30 6,9 *",
        "script":"/sahlen/rebates/quarterly.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":false
    },
    "sahlenRebateNudges31":{
        "job":null,
        "schedule":"0 0 0 31 3,12 *",
        "script":"/sahlen/rebates/quarterly.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":false,
        "onceable":false
    },
    "perrysDownload":{
        "job":null,
        "schedule":"0 45 14,17 * * 1-5",
        "script":"/perrys/ftporders/download.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":true,
        "onceable":true
    },
    "perrysUpload":{
        "job":null,
        "schedule":"0 0 23 * * 1-5",
        "script":"/perrys/ftporders/upload.js",
        "params":{},
        "running":false,
        "started":false,
        "enabled":true,
        "onceable":true
    }
};

module.exports={
    init:()=>{
        console.log("Starting Crons .... ");
        Object.keys(JOBS).forEach((jobId)=>{
            
            if(JOBS[jobId].enabled){
                JOBS[jobId].job = new cron(
                    JOBS[jobId].schedule,
                    async function() {                                           
                        if(!JOBS[jobId].running){                            
                            JOBS[jobId].running=true;
                            JOBS[jobId].startedRunning=new Date();
                            let res = await require(process.cwd()+"/crons"+JOBS[jobId].script)(JOBS[jobId].params);                            
                            JOBS[jobId].running=false;
                            JOBS[jobId].startedRunning = null;
                        }                                                                                                
                    },
                    null,
                    false
                );
            }            

        });

        module.exports.startAll();
        
        let runawayCronMonitor = new cron(
            "0 0 1 * * *",
            async function() { 
                Object.keys(JOBS).forEach((jobId)=>{
                    if(JOBS[jobId].running){
                        if(JOBS[jobId].startedRunning && dayjs(JOBS[jobId].startedRunning).isBefore(dayjs().subtract(1, 'hour'))){
                            console.log("Cron job "+jobId+" has been running for more than an hour, stopping it");
                            JOBS[jobId].job.stop();
                            JOBS[jobId].running=false;
                        }
                    }
                });                                                                                                                                                                                   
            },
            null,
            false
        );
    },

    startAll:()=>{
        Object.keys(JOBS).forEach((jobId)=>{
            module.exports.start(jobId);
        });
    },
    stopAll:()=>{
        Object.keys(JOBS).forEach((jobId)=>{
            module.exports.stop(jobId);
        });
    },
    start:(jobId)=>{
        if(JOBS[jobId] && JOBS[jobId].enabled && !JOBS[jobId].started){
            try{
                JOBS[jobId].job.start();
                console.log("  Job:",jobId);
            }catch(e){
                console.log("Error STARTING cron: "+jobId,e);
            }
            JOBS[jobId].started=true;
            return true;
        }else{
            return false;
        }

    },
    stop:(jobId)=>{
        if(JOBS[jobId] && JOBS[jobId].enabled && JOBS[jobId].started){
            try{
                JOBS[jobId].job.stop();
            }catch(e){
                console.log("Error STOPPING cron: "+jobId,e);
            }
            JOBS[jobId].started=false;
            return true;
        }else{
            return false;
        }
    },
    status:()=>{
        let output={};

        Object.keys(JOBS).forEach((jobId)=>{
            output[jobId] = {"started":JOBS[jobId].started,"running":JOBS[jobId].running};
        });

        return output;
    },
    runOnce:async (jobId, reqParams={})=>{
        console.log("Run once request",jobId)
        if(jobId && JOBS[jobId] && JOBS[jobId].onceable){
            let P={};
            if(typeof reqParams==="object" && Object.keys(reqParams).length>0){
                P=Object.assign({},JOBS[jobId].params,reqParams)
            }else{
                P=Object.assign({},JOBS[jobId].params)
            }
            console.log("Running once job:",jobId);
            return await require(process.cwd()+"/crons"+JOBS[jobId].script)(P);
        }else{
            console.log(jobId)
            return false;
        }
    }
};