const mdb = require( process.cwd()+"/libs/mdb.js");
const COLLECTION = "object_storage";

module.exports={
    create:function(dataToSave){
        return new Promise(async (resolve, reject) => {
            try{
                if(typeof dataToSave!=="object"){ reject("Missing data"); return; }
                if(typeof dataToSave.name==="undefined"){ reject("Missing name"); return; }
                if(typeof dataToSave.type==="undefined"){ reject("Missing type"); return; }
                if(typeof dataToSave.size==="undefined"){ reject("Missing size"); return; }
                if(typeof dataToSave.content==="undefined"){ reject("Missing content"); return; }

                dataToSave.created = new Date();
            
                let newObjectId = await mdb.client().collection(COLLECTION).insertOne(dataToSave);

                if(newObjectId && newObjectId.insertedId){
                    resolve(newObjectId.insertedId);
                }else{
                    reject();
                }
            }catch(e){
                reject(e);
            }            
        });        
    },
    
    get:function(P){
        return new Promise(async (resolve, reject) => {
            try{   
                if(typeof P.ids!=="object"){ P.ids=[P.ids]; }                

                let projection={};
                if(!P.includeContent){ projection={"projection":{content:0} }; }

                let records = await mdb.client().collection(COLLECTION).find({"_id":{"$in":P.ids}, "org_id":P.orgId},projection).toArray();
            
                if(records && records.length>0){
                    resolve(records);
                }else{
                    reject();
                }
            }catch(e){
                reject(e);
            }            
        });
    },

    getOne:function(P){
        return new Promise(async (resolve, reject) => {
            try{   
                if(!P.id){  reject("Missing id"); }
                if(typeof P.id==="string"){  P.id = mdb.objectId(P.id); }
                
                let projection={};
                if(!P.includeContent){ projection={"projection":{content:0} }; }
                
                resolve(await mdb.client().collection(COLLECTION).findOne({"_id":P.id, "org_id":P.orgId},projection));

            }catch(e){
                reject(e);
            }            
        });
    },

    deleteMany:function(P){
        return new Promise(async (resolve, reject) => {
            try{   
                if(typeof P.ids==="string"){  P.ids = [P.ids]; }                            
                resolve(await mdb.client().collection(COLLECTION).deleteMany({"_id":{"$in":P.ids}, "org_id":P.orgId}));
            }catch(e){
                reject(e);
            }            
        });
    }
};