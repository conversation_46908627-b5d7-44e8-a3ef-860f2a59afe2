//const chromium = require('chrome-aws-lambda');
//const playwright = require("playwright-core");
const puppeteer = require("puppeteer");

module.exports={
    generate: (html, fileName)=>{
        return new Promise(async (resolve, reject) => {
            try{
                
                // Launch a headless browser instance
                const browser = await puppeteer.launch({ 
                  headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] 
                  /*args: chromium.args,
                  defaultViewport: chromium.defaultViewport,
                  executablePath: await chromium.executablePath,
                  headless: chromium.headless,
                  ignoreHTTPSErrors: true*/
                });

                // Create a new page
                const page = await browser.newPage();
                await page.setContent(html);

                let pdfOptions={           
                    printBackground: true,
                    format: 'letter',
                    margin: {
                    top: '0in',
                    right: '0in',
                    bottom: '0in',
                    left: '0in'
                    }
                };
                if(fileName){ 
                    pdfOptions.path = `${process.cwd()}/tmp/${fileName}.pdf`; 
                    await page.pdf(pdfOptions);
                    resolve(pdfOptions.path);
                }else{                
                    resolve(await page.pdf(pdfOptions));
                }        
                
            
                // Close the browser
                await browser.close();

            }catch(e){
                reject(e);
            }            

        })
        
    }
};