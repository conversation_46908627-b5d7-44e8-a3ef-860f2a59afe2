module.exports={
  hasTag: function(thing, tags) {
    let result=false;
    
    if(!tags){ return true; }
    if(typeof tags==="string"){ tags=[tags]; }
    tags.some(function(tag){
      if(thing.indexOf(tag) >= 0){
        result=true;
        return true;
      }
    });
    return result;
  },

  hasPermissions:function(ORG, ACCOUNTTAGS, REQUIRED){
    let hasPermissions=false;
    REQUIRED.some(P=>{
      let p = P.split(".");
      let requiredPermissions = ((typeof ORG.permissions[p[0]]!=="unefined" && typeof ORG.permissions[p[0]][p[1]]!=="undefined") ? ORG.permissions[p[0]][p[1]] : false);
      if(requiredPermissions===true || module.exports.hasTag(ACCOUNTTAGS, requiredPermissions)){
        hasPermissions=true;
      }else{
        hasPermissions=false;
        return true;
      }
    });
          
    return hasPermissions;
  },

  safeFilename:function(str){
    return str.replace(/[^a-z0-9.-_]/ig,"").toLowerCase();
  },

  wait:function (to) {
    return new Promise(resolve =>
      setTimeout(() => {     
        resolve()
      }, to)
    )
  },

  splitIt:function(str, del){
    let arr=[];

    if(typeof str === "string" && str.length>0){
      if(!del){
        if(str.indexOf(",")>0){
          del = ",";
        }
        if(str.indexOf(";")>0){
          del = ";";
        }
      }
  
      arr = str.split(del);
      arr.forEach((a,i)=>{
        arr[i] = a.trim();
      });
    }

    return arr;
  },

  objsToArray:function(objs){
    let output=[];

    if(objs && objs.length>0){      
      output.push(Object.keys(objs[0])); // Header

      objs.forEach(obj=>{
        let row=[];
        for(let i=0; i<output[0].length; i++){
          if(obj[output[0][i]] && typeof obj[output[0][i]]==="object" && obj[output[0][i]].length>0){
            obj[output[0][i]] = obj[output[0][i]].join(",");
          }
          row.push(obj[output[0][i]]);
        }
        output.push(row);
      });
    }
    
    return output;

  },

  rndString: function(len, params) {
    if (!len) { len = 5; }
    var text = "", possible = "";
    if (!params) {
      params = ["letters", "uppercase", "numbers", "specials", "safespecials"];
    }

    if (params.indexOf("letters") > -1) { possible += "abcdefghijklmnopqrstuvwxyz"; }
    if (params.indexOf("uppercase") > -1) { possible += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; }
    if (params.indexOf("numbers") > -1) { possible += "0123456789"; }
    if (params.indexOf("specials") > -1) { possible += '!@#$%^&*()-_+=[]{}?'; }
    if (params.indexOf("safespecials") > -1) { possible += '-_'; }
    if (params.indexOf("exclude_confusing") > -1) { possible = possible.replace(/[o0il1]/ig, ""); }

    for (var i = 0; i < len; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  },

  extractEmails:function (text) {
    // Regular expression to match valid email addresses
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    
    // Use match to find all emails in the text
    const emails = text.match(emailRegex);
    
    // Return the array of emails or an empty array if no matches are found
    return emails || [];
  }
}