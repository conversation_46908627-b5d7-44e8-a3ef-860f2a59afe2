const { SESv2Client, SendEmailCommand } = require("@aws-sdk/client-sesv2");
const nodemailer = require("nodemailer");

const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require("@aws-sdk/client-s3");
const sesClient = new SESv2Client({ region: "us-east-1" });
const BUCKETNAME = "bmghub";

const fs = require('fs');

const s3Client = new S3Client({ 
  endpoint:process.env.DOSPACES_ENDPOINT,
  forcePathStyle: false,
  region: "us-east-1",
  credentials: {
    accessKeyId: process.env.DOSPACES_KEY,
    secretAccessKey: process.env.DOSPACES_SECRET
  } 
});

module.exports={

/***************************
  EMAIL
    - to
    - from
    - body
    - subject
****************************/
  email: async (args) => {
    return new Promise(async (resolve, reject) => {
      try {
        if (!args.to || !args.subject || !args.body) {
          return reject("Missing required parameter");
        }

        if (typeof args.to === "string") args.to = [args.to];
        if (!args.cc) args.cc = [];
        if (!args.bcc) args.bcc = [];
        if (typeof args.bcc === "string" && args.bcc.length>0) args.bcc = [args.bcc];
        if (!args.reply) args.reply = [];
        if (!args.from)
          args.from = process.env["DEFAULT_FROM_EMAIL"];

        const transporter = nodemailer.createTransport({SES: {
          sesClient,SendEmailCommand
        }});

        if(args.attachments && args.attachments.length>0){
          // Filter out attachments where the file doesn't exist
          args.attachments = args.attachments.filter(file => {
            if(!fs.existsSync(file.path)){
              console.log(`Warning: Attachment file not found, removing from email: ${file.path}`);
              return false; // Remove this attachment
            }
            return true; // Keep this attachment
          });
        }

        const mailOptions = {
          from: args.from,
          to: args.to,
          cc: args.cc,
          bcc: args.bcc,
          replyTo: args.reply,
          subject: args.subject,
          html: args.body,
          text: args.text || "This email requires HTML support.",
          attachments: args.attachments || [],
        };

        const info = await transporter.sendMail(mailOptions);
        resolve(info.messageId);
      } catch (err) {
        reject(err);
      }
    });
  },//email

/***************************
  S3 - PUT
   - key
   - body
   - encoding
   - contenttype
   - acl
****************************/
  putS3Object:async function(args){
    return new Promise((resolve, reject) => {
      let objToSend={
        Bucket: (args.bucket || BUCKETNAME),
        Key: args.key       
      };
      
      if(!args.body){
        objToSend.Body = Buffer.from('');
      }else{
        objToSend.Body =  args.body;
      }
      
      if(args.encoding){
        objToSend.ContentEncoding=args.encoding;
      }
      if(args.contentType){
        objToSend.ContentType=args.contentType;
      }
      if(args.acl){
        objToSend.ACL=args.acl;
      }
  
      console.log("TRYING:",args.key)
      try{
        const command = new PutObjectCommand(objToSend);
      
        s3Client.send(command).then((r)=>{
          //console.log("UPLOADED:",args.key)
          resolve(r);
        }).catch((e)=>{
          console.log(e);
          reject(e);
        });
      }catch(e){
        console.log("Failed to upload: ", args.key);
        console.log(e);
      }
      
    });
  },


/***************************
  S3 - GET
****************************/
getS3Object:async function(args){
  return new Promise((resolve, reject) => {
    let objToSend={
      Bucket: BUCKETNAME,
      Key: args.key
    };

    const command = new GetObjectCommand(objToSend);

    s3Client.send(command).then(async (r)=>{
      // Convert the stream to buffer for file attachments
      if(args.asBuffer && r.Body){
        const chunks = [];
        for await (const chunk of r.Body) {
          chunks.push(chunk);
        }
        resolve(Buffer.concat(chunks));
      }else{
        resolve(r);
      }
    }).catch((e)=>{
      resolve(e);
    });
  });
},

/***************************
  S3 - GET AS EMAIL ATTACHMENT
  - key: S3 object key
  - filename: Name for the attachment (optional)
  - contentType: MIME type (optional)
****************************/
getS3ObjectAsAttachment: async function(args) {
  return new Promise(async (resolve, reject) => {
    try {
      const s3Response = await this.getS3Object({
        key: args.key,
        asBuffer: true
      });

      if (!s3Response.Buffer) {
        return reject("Failed to get S3 object or convert to buffer");
      }

      // Extract filename from key if not provided
      const filename = args.filename || args.key.split('/').pop();

      // Determine content type based on file extension if not provided
      let contentType = args.contentType || s3Response.ContentType;
      if (!contentType) {
        const ext = filename.split('.').pop().toLowerCase();
        const mimeTypes = {
          'pdf': 'application/pdf',
          'csv': 'text/csv',
          'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'xls': 'application/vnd.ms-excel',
          'txt': 'text/plain',
          'png': 'image/png',
          'jpg': 'image/jpeg',
          'jpeg': 'image/jpeg'
        };
        contentType = mimeTypes[ext] || 'application/octet-stream';
      }

      const attachment = {
        filename: filename,
        content: s3Response.Buffer,
        contentType: contentType
      };

      resolve(attachment);
    } catch (error) {
      reject(error);
    }
  });
},


/***************************
  S3 - DELETE
****************************/
deleteS3Object:async function(args){
  return new Promise((resolve, reject) => {
    let objToSend={
      Bucket: BUCKETNAME,
      Key: args.key      
    };

    const command = new DeleteObjectCommand(objToSend);
    
    s3Client.send(command).then((r)=>{
      resolve(r);
    }).catch((e)=>{
      reject(e);
    });
  });
}
};