const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");
const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require("@aws-sdk/client-s3");
const sesClient = new SESClient({ region: "us-east-1" });
const s3Client = new S3Client({ 
  endpoint:process.env.DOSPACES_ENDPOINT,
  forcePathStyle: false,
  region: "us-east-1",
  credentials: {
    accessKeyId: process.env.DOSPACES_KEY, // Access key pair. You can create access key pairs using the control panel or API.
    secretAccessKey: process.env.DOSPACES_SECRET // Secret access key defined through an environment variable.
  } 
});

module.exports={

/***************************
  EMAIL
    - to
    - from
    - body
    - subject
****************************/
  email:(args)=>{
    return new Promise((resolve, reject) => {
      
      if(typeof args.to==="undefined" || typeof args.body==="undefined" || typeof args.subject==="undefined"){
        resolve("missing required parameter");
        return false;
      }

      if(args.to.length===0){
        resolve("empty TO");
        return false;
      }
          
      if(typeof args.to==="string"){  args.to = [args.to];  }
      if(typeof args.cc==="undefined"){ args.cc=[]; }
      if(typeof args.cc==="string"){  args.cc = [args.cc];  }
      if(typeof args.bcc==="undefined"){ args.bcc=[]; }
      if(typeof args.bcc==="string"){  args.cc = [args.bcc];  }
      if(typeof args.reply==="undefined"){ args.reply=[]; }
      if(typeof args.reply==="string"){  args.reply = [args.reply];  }
      if(typeof args.from==="undefined"){  args.from=process.env["DEFAULT_FROM_EMAIL"];  } 

      let tempTo=[];
      if(args.to.length>0){
        args.to.forEach((t)=>{
          if(typeof t==="string" && t.trim().length>5){
            tempTo.push(t);
          }
        })
        args.to = tempTo;
      }
            
      if(typeof args.body==="object") { try{ args.body = JSON.stringify(args.body); }catch(e){ args.body=""; } }

      let body = {
        Html: {
          Charset: "UTF-8",
          Data: args.body,
        }
      };

      if(typeof args.type!=="undefined" && args.type==="text"){
        body={
          Text: {
            Charset: "UTF-8",
            Data: args.body,
          }
        }
      }

      let sendCommand=new SendEmailCommand({
        Destination: {
          /* required */        
          BccAddresses: args.bcc,
          CcAddresses: args.cc,
          ToAddresses: args.to
        },
        Message: {
          /* required */
          Body: body,
          Subject: {
            Charset: "UTF-8",
            Data: args.subject,
          },
        },
        Source: args.from,
        ReplyToAddresses: args.reply,
      });

      sesClient.send(sendCommand).then(r=>{
        resolve(r.MessageId)
      }).catch(e=>{
        console.log(args);
        reject(e)}
      )
     
  });//promise  	
  },//email

/***************************
  S3 - PUT
   - key
   - body
   - encoding
   - contenttype
   - acl
****************************/
  putS3Object:async function(args){
    return new Promise((resolve, reject) => {
      let objToSend={
        Bucket: (args.bucket || "bmghub"),
        Key: args.key,
        Body:args.body        
      };
      if(args.encoding){
        objToSend.ContentEncoding=args.encoding;
      }
      if(args.contentType){
        objToSend.ContentType=args.contentType;
      }
      if(args.acl){
        objToSend.ACL=args.acl;
      }
  
      //console.log("TRYING:",args.key)
      try{
        const command = new PutObjectCommand(objToSend);
      
        s3Client.send(command).then((r)=>{
          //console.log("UPLOADED:",args.key)
          resolve(r);
        }).catch((e)=>{
          reject(e);
        });
      }catch(e){
        console.log("Failed to upload: ", args.key);
        console.log(e);
      }
      
    });
  },


/***************************
  S3 - GET
****************************/
getS3Object:async function(args){
  return new Promise((resolve, reject) => {
    let objToSend={
      Bucket: "bmghub",
      Key: args.key    
    };
    
    const command = new GetObjectCommand(objToSend);
    
    s3Client.send(command).then((r)=>{
      resolve(r);
    }).catch((e)=>{      
      resolve(e);
    });
  });
},


/***************************
  S3 - DELETE
****************************/
deleteS3Object:async function(args){
  return new Promise((resolve, reject) => {
    let objToSend={
      Bucket: "bmghub",
      Key: args.key      
    };

    const command = new DeleteObjectCommand(objToSend);
    
    s3Client.send(command).then((r)=>{
      resolve(r);
    }).catch((e)=>{
      reject(e);
    });
  });
}
};