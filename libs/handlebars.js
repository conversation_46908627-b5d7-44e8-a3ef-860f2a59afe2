const Handlebars = require("handlebars");
const dayjs = require("dayjs");
dayjs.extend(require('dayjs/plugin/relativeTime'));

String.prototype.toPhone = function(format) {
  
  let numbers=this.replace(/[^0-9]/ig,"");
  if(numbers.length===10){
    switch(format){
      case "formal":
      return "("+numbers.substr(0,3)+") "+numbers.substr(3,3)+"-"+numbers.substr(6);
      break;
      default:
        return numbers.substr(0,3)+"."+numbers.substr(3,3)+"."+numbers.substr(6);
      break;
    }
  }else{
    return this;
  }

  
};

Number.prototype.commaFormat = function(decimals) {
  return this.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

Handlebars.registerHelper('formatCommas', function(str, options) {  
  if(typeof str!=="undefined"){
    return str.commaFormat();
  }else{
    return "";
  }
});

Handlebars.registerHelper('formatDate', function(str, options) {  
    if (options && options.hash && options.hash.format) {    
      return dayjs(str).format(options.hash.format);
    }else{
      return dayjs(str).fromNow();
    }  
});
  
Handlebars.registerHelper('formatPhone', function(str, options) {  
    if (options && options.hash && options.hash.format) {    
      return str.toPhone(options.hash.format);
    }else{
      return str.toPhone();
    }  
});
  
  
Handlebars.registerHelper('arrayToString', function(arr, options) {
    if (!arr || arr.length === 0) { return ((options && options.hash && options.hash.empty) ? options.hash.empty : ""); }
    let join = ",";
    if (options && options.hash && options.hash.join) {
      join = options.hash.join;
    }
    arr.sort();
    return arr.join(join);
})

Handlebars.registerHelper('formatId', function(str, options) {  
    if (options && options.hash && options.hash.length) {    
      return str.slice(Number(options.hash.length)*-1)
    }else{      
      if(str){
        str = str.toString();
        return str.slice(-7);
      }else{
        return "";
      }     
    }  
});

Handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {    
    return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
})

Handlebars.registerHelper('capitalize', function(str, options) {  
    if(typeof str!=="undefined"){
      return str.charAt(0).toUpperCase() + str.slice(1);
    }else{
      return "";
    }
});

Handlebars.registerHelper('ifDateAfter', function(arg1, arg2, options) {
  if(!arg1 || !arg2){ return options.inverse(this); }
  let result = false;
  
  try{
    result = (dayjs(arg1).isAfter(dayjs(arg2)));
  }catch(e){
    console.log("Error in ifDateAfter: ", e);
  }

  return result ? options.fn(this) : options.inverse(this);
})


module.exports=function(str, data){
    return Handlebars.compile(str)(data);
}
