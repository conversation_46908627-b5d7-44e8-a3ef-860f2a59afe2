const mongo = require('mongodb');
//const mongoClient = require('mongodb').MongoClient;
let DB = null;

module.exports={
  connect:() => {
    
    const mongoClient = mongo.MongoClient;
    return new Promise((resolve, reject) => {
      console.log("trying to connect to db")
      mongoClient.connect(process.env.MONGO_CONN, { useUnifiedTopology: true, useNewUrlParser: true }).then((db) => {
        DB = db.db(process.env.MONGO_DB);
        resolve();
      }).catch((e) => {
        reject(e);
      });    
    });
  },
  client:() => {
    return DB;
  },
  objectId:(str)=>{
    return new mongo.ObjectId(str);
  }
};