const mdb = require("../libs/mdb.js");
const dayjs = require("dayjs");
const aws = require("../libs/aws.js");
const _ = require("lodash");
const common = require("../libs/common.js");
const converter = require('json-2-csv');
const ExcelJS = require('exceljs');
const Handlebars = require("handlebars");


let ORGID = null;
let ORG = null;

function safeFilename(str){
    return str.replace(/\s/ig, "_").replace(/[^a-zA-Z0-9._\/]/ig, "");
}


module.exports={
    run:async function(orgId, exportId, config){
        if(typeof orgId==="string"){
            orgId = mdb.objectId(orgId);        
        }        

        ORGID = orgId;

        ORG = await mdb.client().collection("orgs").findOne({"_id":ORGID});

        console.log("Export Running", exportId);
        let csv = await this.createCSV(orgId, config)        
        await this.saveExport(exportId,config, await this.createWorkbook(config, csv.meta, csv.data));
        console.log("Export Complete", exportId);
        
    },

/********************************************************************
    * CREATE WORKBOOK 
********************************************************************/
    createWorkbook:async function(config, meta, csvData){
        const workbook = new ExcelJS.Workbook();
        const sheetDetails = workbook.addWorksheet('Details');

        let rowIndex=2;
        sheetDetails.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
        sheetDetails.getRow(rowIndex).getCell(1).value="Report Title:";
        sheetDetails.getRow(rowIndex).getCell(2).value=config.name;

        rowIndex++;
        sheetDetails.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
        sheetDetails.getRow(rowIndex).getCell(1).value="Created By:";
        sheetDetails.getRow(rowIndex).getCell(2).value=config.created_by;

        rowIndex++;
        sheetDetails.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
        sheetDetails.getRow(rowIndex).getCell(1).value="Date Created:";
        sheetDetails.getRow(rowIndex).getCell(2).value=dayjs().format("MM-DD-YYYY hh:MMa");

        rowIndex++;
        sheetDetails.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
        sheetDetails.getRow(rowIndex).getCell(1).value="Date Range:";
        sheetDetails.getRow(rowIndex).getCell(2).value=meta.dates;

        rowIndex++;
        sheetDetails.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
        sheetDetails.getRow(rowIndex).getCell(1).value="Filters:";

        meta.filters.forEach(function(filterLabel){            
            rowIndex++;        
            sheetDetails.getRow(rowIndex).getCell(2).value=filterLabel;
        })
        
        const sheetData = workbook.addWorksheet('Data');
        sheetData.addRows(csvData);
        /*
        let filename=process.cwd()+"/tmp/"+safeFilename(config.name)+".xlsx";        
        await workbook.xlsx.writeFile(filename);    
        return filename;
        */
        return await workbook.xlsx.writeBuffer();
    },

/********************************************************************
    * CREATE CSV - Takes the config and generates the CSV data 
********************************************************************/
    createCSV:async function(orgId, config){        
        let dataToFind={ "org_id":orgId }
        let COLLECTION="";
        let DATEFIELD=((config.date_range && config.date_range.field) ? config.date_range.field : "created");
        let TAGMAPPINGFIELD="";
        let ACCOUNTTAGMAPPINGFIELD = "";
        let metaOutput={dates:"", filters:[]};
        let records=[];
        let ORG = await mdb.client().collection("orgs").findOne({_id:orgId});

        

        // === Calculate the date range to match ===================================================================
        if(config.date_range && typeof config.date_range==="object"){
            if(typeof config.date_range.rel==="string" && config.date_range.rel.length>0){
                let d={};
                switch(config.date_range.rel.trim().toUpperCase()){
                    case "TODAY":
                        dataToFind[DATEFIELD]={"$gte":dayjs(dayjs().format("YYYY/MM/DD")).toDate()};
                        metaOutput.dates=dayjs().format("MM/DD/YYYY");
                    break;
                    case "YESTERDAY":
                        dataToFind[DATEFIELD]={"$gte":dayjs(dayjs().format("YYYY/MM/DD")).subtract(1,"day").toDate()};
                        metaOutput.dates=dayjs(dayjs().format("MM/DD/YYYY")).subtract(1,"day").format("MM/DD/YYYY");
                    break;
                    case "WTD":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().day(1).format("YYYY/MM/DD")).toDate()};
                        dataToFind["$and"].push(d);
                        d={};
                        d[DATEFIELD]={"$lte":dayjs().toDate()};
                        dataToFind["$and"].push(d);
                        metaOutput.dates=dayjs().day(1).format("MM/DD/YYYY") + " - " + dayjs().format("MM/DD/YYYY");
                    break;
                    case "MTD":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().format("YYYY/MM/01")).toDate()}
                        dataToFind["$and"].push(d);
                        d={};
                        d[DATEFIELD]={"$lte":dayjs().toDate()}
                        dataToFind["$and"].push(d);
                        metaOutput.dates= dayjs().format("MM/01/YYYY") + " - " + dayjs().format("MM/DD/YYYY");
                    break;
                    case "YTD":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().format("YYYY/01/01")).toDate()}
                        dataToFind["$and"].push(d);

                        d={};
                        d[DATEFIELD]={"$lte":dayjs().toDate()}
                        dataToFind["$and"].push(d);
                        metaOutput.dates= dayjs().format("01/01/YYYY") + " - " + dayjs().format("MM/DD/YYYY");
                    break;
                    case "LASTMONTH":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().subtract(1,"month").format("YYYY/MM/01")).toDate()}
                        dataToFind["$and"].push(d);
                        d={};
                        d[DATEFIELD]={"$lt":dayjs(dayjs().format("YYYY/MM/01")).toDate()}
                        dataToFind["$and"].push(d);
                        metaOutput.dates= dayjs(dayjs(dayjs().subtract(1,"month").format("YYYY/MM/01"))).format("MM/DD/YYYY") + " - " + dayjs(dayjs().format("MM/01/YYYY")).subtract(1,"day").format("MM/DD/YYYY");
                    break;                    
                    case "7D":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().subtract(7,"day").format("YYYY/MM/DD")).toDate()}                     
                        dataToFind["$and"].push(d);
                        d={};
                        d[DATEFIELD]={"$lte":dayjs().toDate()}
                        dataToFind["$and"].push(d);
                        metaOutput.dates= dayjs().subtract(7,"day").format("MM/DD/YYYY") + " - " + dayjs().format("MM/DD/YYYY");
                    break;
                    case "30D":
                        dataToFind["$and"]=[];
                        d={};
                        d[DATEFIELD]={"$gte": dayjs(dayjs().subtract(30,"day").format("YYYY/MM/DD")).toDate()}
                        dataToFind["$and"].push(d);
                        d={};
                        d[DATEFIELD]={"$lte":dayjs().toDate()}
                        dataToFind["$and"].push(d);
                        metaOutput.dates= dayjs().subtract(30,"day").format("MM/DD/YYYY") + " - " + dayjs().format("MM/DD/YYYY");
                    break;                    
                }

            }
        }else{
            metaOutput.dates = "N/A";
        }

        // === FILTERS ==============================================================================================
        if(config.filters){

            config.filters.forEach(function(filter){
                if(!filter.post){                
                    metaOutput.filters.push(filter.label);

                    /*switch(filter.field){
                        case "tags":
                        case "active":                        
                            if(typeof filter.value==="string" || typeof filter.value==="boolean" || typeof filter.value==="number"){
                                dataToFind[filter.field]=filter.value
                            }else if(typeof filter.value==="object"){
                                dataToFind["$or"]=[];
                                let d={};
                                filter.value.forEach(function(val){
                                    d={};
                                    d[filter.field]=val;
                                    dataToFind["$or"].push(d);
                                });
                            }
                        break;                    
                    }*/
                    if(filter.value===null){
                        // Ignore
                    }else if(typeof filter.value==="object" && typeof filter.value.length!=="undefined" && filter.value.length>0){                        
                        dataToFind[filter.field]={"$in":filter.value};
                    }else if(typeof filter.value==="string" || typeof filter.value==="boolean" || typeof filter.value==="number" || typeof filter.value==="object"){
                        dataToFind[filter.field]=filter.value
                    }

                }
            })

        }
        

        // === Figure out the main collection to use ===================================================================
        switch(config.datasource){
            case "accounts":
                COLLECTION="accounts";  
                TAGMAPPINGFIELD="account_tags";              
            break;
            case "items":
                COLLECTION=null;  
                TAGMAPPINGFIELD="item_tags";
                ACCOUNTTAGMAPPINGFIELD="account_tags";  

                records = await mdb.client().collection("items").aggregate([
                    {"$match":dataToFind},
                    {
                        "$lookup": {
                            "from": "inventory",
                            "localField": "_id",
                            "foreignField": "item_id",
                            "as": "inventory_records"
                        }
                    },
                    { 
                        $addFields: {
                            "totalInventoryQty": { $sum: "$inventory_records.qty" }
                        }
                    },
                    {
                        "$project": {
                            "inventory_records": 0
                        }
                    }
                ]).toArray();

            break;
            case "orders":
                COLLECTION=null;  
                TAGMAPPINGFIELD="order_tags";
                let items = await mdb.client().collection("items").find({ "org_id":orgId }).toArray();
                let orders = await mdb.client().collection("orders").aggregate([
                    {"$lookup":{
                        "from":"accounts",
                        "localField":"account_id",
                        "foreignField":"_id",
                        "as":"account"
                    }},
                    {"$match":dataToFind}                    
                ]).toArray();

                console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                console.log(JSON.stringify(dataToFind))
                console.log("#",orders.length)
                console.log("<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
                
            
                for(let i=0; i<orders.length; i++){
                    orders[i].account=orders[i].account[0];  

                    //GET ALL THE ITEM TAGS IN THIS ORDER
                    let orderItemsTags=[];                                    
                    for(let itemId of Object.keys(orders[i].items)){
                        let itemDetails = _.find(items,{"_id":mdb.objectId(itemId)});
                        if(itemDetails.tags && itemDetails.tags.length>0){
                            itemDetails.tags.forEach(function(tag){
                                if(orderItemsTags.indexOf(tag)<0){
                                    orderItemsTags.push(tag);
                                }
                            })  
                        }                        
                    }


                    for(let itemId of Object.keys(orders[i].items)){

                        let itemDetails = _.find(items,{"_id":mdb.objectId(itemId)});

                        if(itemDetails.tags && itemDetails.tags.length>0){
                            itemDetails.tags.forEach(function(tag, i){
                                if(ORG.item_tags[tag]){
                                    itemDetails.tags[i] = ORG.item_tags[tag].label;
                                }
                            })  
                        }

                        itemDetails.qty = orders[i].items[itemId].qty;
                        itemDetails.unit = orders[i].items[itemId].unit;
                        
                        records.push(Object.assign({},orders[i],{"item":itemDetails},{"allItemTags":orderItemsTags}));
                    
                    }

                };               
            break;
            case "activity":     
                COLLECTION=null; 
                let activityItems = await mdb.client().collection("items").find({ "org_id":orgId }).toArray();
                
                records = await mdb.client().collection("activity").aggregate([
                    {"$lookup":{
                        "from":"accounts",
                        "localField":"account_id",
                        "foreignField":"_id",
                        "as":"account"
                    }},
                    {"$match":dataToFind}                    
                ]).toArray();

                for(let i=0; i<records.length; i++){
                    if(records[i].account.length>0){
                        records[i].account = records[i].account[0];
                    }                    
                    if(typeof records[i].data.item_id!=="undefined"){
                        if(typeof records[i].data.item_id!=="object"){
                            records[i].data.item_id = mdb.objectId(records[i].data.item_id);
                        }

                        let activityItem = _.find(activityItems, {"_id":records[i].data.item_id})
                        if(activityItem){
                            records[i].item=activityItem;
                            if(records[i].data.qty){
                                records[i].item.qty=records[i].data.qty;
                            }
                        }                        
                    }
                }
               
            break;

        }

        // === RUN QUERY ===========================================================================================
        if(COLLECTION){                 
               
            records = await mdb.client().collection(COLLECTION).find(dataToFind).toArray();
        }
        
        // === APPLY POST FILTERS ===============================================        
        if(config.filters){
            let postFilters = _.filter(config.filters,{"post":true});
            if(postFilters && postFilters.length>0){
                for(let postFilter of postFilters){
                    records = records.filter(function(record){
                        
                        let value = eval("record."+postFilter.field);
                            
                        if(typeof postFilter.value==="string" || typeof postFilter.value==="number"){ // FILTER IS A SINGLE VALUE
                            if(typeof value=="string" && value === postFilter.value.toString()){
                                return true;                    
                            }else if(typeof value=="number" && value === Number(postFilter.value)){
                                return true;                    
                            }else if(value && typeof value=="object" && value.length>0){
                                let RETURN=false;
                                value.some(function(v){
                                    if(v==postFilter.value){
                                        RETURN=true;
                                        return true;
                                    }
                                });
                                return RETURN;
                            }
            
                        }else if(postFilter.value && typeof postFilter.value==="object" && postFilter.value.length>0){ // FILTER IS AN ARRAY
                            let RETURN=false;
                            postFilter.value.some(function(pv){
                                if(typeof value=="string" && value === pv.toString()){
                                    RETURN=true;
                                    return true;                        
                                }else if(typeof value=="number" && value === Number(pv)){
                                    RETURN=true;
                                    return true;                        
                                }else if(value && typeof value==="object" && value.length>0){
                                    value.some(function(v){
                                        if(v==postFilter.value){
                                            RETURN=true;
                                            return true;
                                        }
                                    });
                                    if(RETURN){
                                        return true;
                                    }
                                }
                            });
                            return RETURN;
            
                        }
                        
                    })
                }

            }
        }

        console.log("Records Length > ",records.length)
                        
        if(records){
            
                // ---- Limit to selected fields --------------------------
                records=records.map(o=>{ 
                    let output={};

                    if(TAGMAPPINGFIELD && o.tags){
                        for(let i=0; i<o.tags.length; i++){
                            if(typeof ORG[TAGMAPPINGFIELD]!=="undefined" && typeof ORG[TAGMAPPINGFIELD][o.tags[i]]!=="undefined" && typeof ORG[TAGMAPPINGFIELD][o.tags[i]].label!=="undefined"){
                                o.tags[i] = ORG[TAGMAPPINGFIELD][o.tags[i]].label
                            }                            
                        }
                    }
                    
                    if(ACCOUNTTAGMAPPINGFIELD && typeof o.visible_to==="object" && o.visible_to.length>0){
                        for(let i=0; i<o.visible_to.length; i++){
                            if(typeof ORG[ACCOUNTTAGMAPPINGFIELD][o.visible_to[i]]!=="undefined" && typeof ORG[ACCOUNTTAGMAPPINGFIELD][o.visible_to[i]].label!=="undefined"){
                                o.visible_to[i] = ORG[ACCOUNTTAGMAPPINGFIELD][o.visible_to[i]].label
                            }                            
                        }
                    }

                    // ---- Get Friendly name for order status ------------
                    if(o.status){
                        let statusDetails = _.find(ORG.data_configs.orders.statuses,{"id":o.status});
                        if(typeof statusDetails==="object" && typeof statusDetails.label==="string"){
                            o.status = statusDetails.label;
                        }
                    }
                    
                    
                    if(config.fields && config.fields.length>0){
                        if(o._id){ output._id = o._id.toString(); }
                        config.fields.forEach(f=>{ 

                            if(typeof f.child==="undefined"){
                                try{
                                    output[f.header]=eval(`o.${f.id}`)    
                                }catch(e){
                                    console.log("LN424",e)
                                    console.log(f, o);
                                    console.log("==========================\n\n")
                                    output[f.header]="";
                                }
                                
                            }else{
                                f.child.forEach(function(fieldChild){
                                    try{
                                        output[f.header+" > "+fieldChild.label] = eval(`o.${f.id}.${fieldChild.id}`) ;
                                    }catch(e){
                                        output[f.header+" > "+fieldChild.label] = "";
                                    }
                                    
                                })                                
                            }
                            
                        });
                    }else{                    
                        output = {};
                    }                    
                    //console.log({output})
                    return  output;
                });

                
                // ----- Convert JSON to CSV ------------------------------
                if(!records){
                    records=[];
                }                
                return {"data":common.objsToArray(records), "meta":metaOutput};
                
            }else{
                return null;
            }

            
        

    },

/********************************************************************
    * SAVE EXPORT -- UPLOAD TO SPACES
********************************************************************/
    saveExport:async function(exportId, config, buffer){
        let filename = dayjs().format("YYYYMMDDHHmm")+"_"+safeFilename(config.name)+"_"+exportId+".xlsx";
        let exportFilename = ORGID+"/exports/"+filename;

        let dataToSave={
            "org_id":ORGID,
            "spaces_key":exportFilename,
            "exportId":exportId,
            "config":config,
            "file":filename,
            "created":new Date()
        };
        
        let uploaded = await aws.putS3Object({
            "key":exportFilename,
            "body":Buffer.from(buffer, 'base64'),
            "encoding":"base64",
            "contentType":"application/vnd.ms-excel",
            "acl":"public-read"
        });

        if(uploaded['$metadata'].httpStatusCode===200){
            let created = await mdb.client().collection("exports").insertOne(dataToSave);
            if(config.email_to){
                let emails=[];
                config.email_to.split(",").forEach(function(eml){
                    emails.push(eml.trim().toLowerCase());
                });

                if(ORG.server.templates.exporter){
                    aws.email({
                        "to":emails,
                        "from":`${ORG.server.templates.exporter.from} <${ORG.server.config.contacts.from}>`,
                        "subject": `${ORG.server.templates.exporter.subject} ${config.name}`,
                        "body":Handlebars.compile(ORG.server.templates.exporter.body)({"URL":process.env["HUBCDN_ENDPOINT"]+"/"+exportFilename, "NAME":config.name})
                      }).then(r=>{                
                      }).catch(e=>{        
                        response.error(res);
                      });
                }
                                                
            }
            
            return true;
        }else{
            return false;
        }

        

        

        


    }
};

