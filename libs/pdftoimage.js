const pdf2img = require('pdf-img-convert');
const sharp = require('sharp');

module.exports={
    /*
        ARGS
            - url 
            - size (number)
    */
    generate:async function(args){
        try{
            let pdfImage=null;
            if(args.url){
                pdfImage = await pdf2img.convert(args.url);
            }

            const { data, info } = await sharp(pdfImage[0])
                .resize(args.size,args.size ,{fit:"contain",background:{ r: 255, g: 255, b: 255 }})             
                .jpeg({ mozjpeg: true })
                .toBuffer({ resolveWithObject: true });

            
            if(data && data.buffer){
                return data.buffer;
            }else{
                return false;
            }
        }catch(e){
            console.log(e);
            return false;
        }
        
    }
};