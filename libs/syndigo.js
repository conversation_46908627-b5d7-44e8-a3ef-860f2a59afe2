let fetch  = require("node-fetch");
const dayjs = require("dayjs");
const _ = require("lodash");
const BASEURL="https://api.syndigo.com/api";
const APIUSERNAME="BMGMarketing_API";
const APISECRET="aCHFCPY6URLCB0Pj8z%2FZAoV%2BWbPbv8pTrCH6KWwkXsHbaEM5NVVcRjsrp1NyjnuhZ8cD%2F9cTpBMzQ%0AnTigkRYiA%3D%3D";
let recordCount = 400

function safeKey(key){
    return key.replace(/\s/,"").replace("/","").replace(/[^a-z0-9.-_]/ig,"");
}

module.exports = function syndigo(){
    return {
        auth: function(){
            return new Promise(async (resolve, reject) => {
                const response = await fetch(BASEURL+"/auth?username="+APIUSERNAME+"&secret="+APISECRET, {method: 'GET'});
                try{
                    const token = await response.json()
                    if(token){
                        resolve(token.Value);
                    }else{
                        reject();
                    }
                }catch(e){
                    reject(e);
                }        
            });     
        },

        getRecentChanges:function(df, brand){
            let that = this;
            return new Promise(async (resolve, reject) => {
                try{
                  that.auth().then(async(token)=>{                          
                    let dataToSend={

                        "OrderBy": "AuditInfo.LastModifiedDate",
                        "Desc": false,
                        "OnHold": false,
                        "Archived": false,
                        "Language": "en-US",
                        "DateFilters": [ 
                            { 
                                "Name": "LastModifiedDate", //We can use this date filter to search on the date when things are updated 
                                "Operator": "GreaterThan", //Operator on the below date value 
                                "Value": ((df) ? df : dayjs().subtract(1,"day").format("YYYY-MM-DD")+"T00:00:00.000Z"), //"2023-07-01T00:00:00.000Z", 
                                "IncludeMissing": true 
                            } 
                        ]
                    
                    };
                    let uatURL=`/importexport/productsearch/?skipCount=0&takeCount=${recordCount}&vocabularyId=bfbac5ec-7be7-4c58-aad7-c1dd9a18ebf2&useLens=false&locale=en-US`;
                    let prodURL=`/importexport/productsearch/?skipCount=0&takeCount=${recordCount}&vocabularyId=608ac123-9bab-4657-a654-985f4d41d354&useLens=false&locale=en-US`;
                   
                    const response = await fetch(BASEURL+prodURL, 
                        {
                            method: 'POST', 
                            body:JSON.stringify(dataToSend),
                            headers:{
                                "Content-Type":"application/json; charset=utf-8",
                                "Authorization":"EN "+token
                            }
                        });
                    try{
                        
                        const products = await response.json();
                        
                        if(products && products.ProductImportData && products.ProductImportData.ProductsToImport){
                          if(brand && brand.length>0){
                                resolve(_.filter(products.ProductImportData.ProductsToImport, function(o){             
                                    if(o.SourceParties[0].toLowerCase().indexOf(brand.toLowerCase())>=0){           
                                        return true;
                                    }
                                }))
                          }else{
                            resolve(products.ProductImportData.ProductsToImport);
                          }
                            
                        }else{
                            resolve([]);
                        }
                    }catch(e){
                        reject(e);
                    }

                  }).catch(function(e){
                      reject(e);
                  });
                }catch(e){
                  console.log(e);
                  reject();
                }
                                    
            });     
        },
        
        getAll:function(brand){
            let that = this;
            return new Promise(async (resolve, reject) => {

                that.auth().then(async(token)=>{                          
                    let dataToSend={

                        "OrderBy": "AuditInfo.LastModifiedDate",
                        "Desc": false,
                        "OnHold": false,
                        "Archived": false,
                        "Language": "en-US",
                        "DateFilters": [ 
                            { 
                                "Name": "LastModifiedDate", //We can use this date filter to search on the date when things are updated 
                                "Operator": "GreaterThan", //Operator on the below date value 
                                "Value": "2023-02-01T00:00:00.000Z", 
                                "IncludeMissing": true 
                            }  
                        ]
                    
                    };       
                
                    let hasData=true,
                        output=[],
                        skip=0,
                        count=100,
                        totalRecordsAvailable=null;

                    while(hasData){
                        let prodURL=`/importexport/productsearch/?skipCount=${skip*count}&takeCount=${count}&vocabularyId=608ac123-9bab-4657-a654-985f4d41d354&useLens=false&locale=en-US`;
                        const response = await fetch(BASEURL+prodURL, 
                            {
                                method: 'POST', 
                                body:JSON.stringify(dataToSend),
                                headers:{
                                    "Content-Type":"application/json; charset=utf-8",
                                    "Authorization":"EN "+token
                                }
                            });
                        try{
                            
                            const r = await response.json();
                            
                            if(!totalRecordsAvailable){
                                totalRecordsAvailable = ((r && r.ProducingOperationDetails && r.ProducingOperationDetails.TotalHitCount) ? r.ProducingOperationDetails.TotalHitCount : 0)
                            }

                            if(r && r.ProductImportData && r.ProductImportData.ProductsToImport){
                              output = output.concat(r.ProductImportData.ProductsToImport);
                            }                      

                           // console.log(output.length);

                            if(output.length>=totalRecordsAvailable){
                                hasData = false;
                            }                    
                            
                        }catch(e){
                            console.log(e);    
                        }

                        skip++;
                    }

                    if(brand && brand.length>0){
                      resolve(_.filter(output, function(o){             
                        if(o.SourceParties[0].toLowerCase().indexOf(brand.toLowerCase())>=0){           
                          return true;
                        }
                      }))
                    }else{
                      resolve(output);
                    }                    
                    

                }).catch(function(e){
                    reject(e);
                });                    
            });     
        },

        getByGTIN:function(p){
            let that = this;            
            return new Promise(async (resolve, reject) => {

                that.auth().then(async(token)=>{                          
                    let dataToSend={

                        "OrderBy": "AuditInfo.LastModifiedDate",
                        "Desc": false,
                        "OnHold": false,
                        "Archived": false,
                        "Language": "en-US",
                        "IdentifierAttributes": [
                            "GTIN"
                        ],
                        "Identifiers":p.gtins
                    
                    };       
                
                    let hasData=true,
                        output=[],
                        skip=0,
                        count=500,
                        totalRecordsAvailable=0;

                    while(hasData){
                        console.log("skipcount", skip, count);
                        let prodURL=`/importexport/productsearch/?skipCount=${skip*count}&takeCount=${count}&vocabularyId=608ac123-9bab-4657-a654-985f4d41d354&useLens=false&locale=en-US`;
                        const response = await fetch(BASEURL+prodURL, 
                            {
                                method: 'POST', 
                                body:JSON.stringify(dataToSend),
                                headers:{
                                    "Content-Type":"application/json; charset=utf-8",
                                    "Authorization":"EN "+token
                                }
                            });
                        try{
                            
                            const r = await response.json();
                            
                            if(r && r.ProducingOperationDetails && r.ProducingOperationDetails.TotalHitCount){
                              if(!totalRecordsAvailable){
                                totalRecordsAvailable = r.ProducingOperationDetails.TotalHitCount
                              }

                              if(r && r.ProductImportData && r.ProductImportData.ProductsToImport){
                                output = output.concat(r.ProductImportData.ProductsToImport);
                              }
                            }else{
                              console.log("NO ProducingOperationDetails",r)
                            }
                          
                            console.log("total records >=",output.length, totalRecordsAvailable);

                            if(output.length>=totalRecordsAvailable){
                                hasData = false;
                            }                    
                            
                        }catch(e){
                            console.log(e);    
                        }

                        skip++;
                    }

                    resolve(output);
                    

                }).catch(function(e){
                    reject(e);
                });                    
            });     
        },

        parse:function(data){
            let output=[];
            let rootKeys=["ImportMode", "PackageType", "ComplexValues","Values","MultiValues","ContainerValues","AssetValues","NutritionalInformationModule","AuditInfo","Archived"];
            data.forEach(item=>{
                let record={};
                rootKeys.forEach(rootKey=>{
                    if(typeof item[rootKey]==="string"){
        
                        record[safeKey(rootKey)] = item[rootKey];
        
                    }else if(typeof item[rootKey]==="boolean"){                  
                        record[safeKey(rootKey)]=item[rootKey];
                    }else if(typeof item[rootKey]==="object" && item[rootKey].length>0){
        
                        item[rootKey].forEach((itemKeyObj)=>{
                            if(!record[safeKey(rootKey)]){
                                record[safeKey(rootKey)]={};
                            }
        
                            if(itemKeyObj.Name){  // Basic Value / MultiValue -----------------
                                
                                record[safeKey(rootKey)][safeKey(itemKeyObj.Name)]=((itemKeyObj.ValuesByLocale["en-US"]) ? itemKeyObj.ValuesByLocale["en-US"] : itemKeyObj.ValuesByLocale[""])
                            
                            }else if(itemKeyObj.ParentAttribute && itemKeyObj.ValueListByLocale){ // ComplexValues ------------------------
                                
                                if(!record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)]){
                                    record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)]={};
                                }
            
                                if(itemKeyObj.ValueListByLocale[0] && itemKeyObj.ValueListByLocale[0].RowList.length>0){
                                    itemKeyObj.ValueListByLocale[0].RowList[0].forEach(rlObj=>{
                                        record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(rlObj.Name)] = rlObj.Value;                           
                                    })
                                }
            
            
                            }else if(itemKeyObj.ParentAttribute && itemKeyObj.RowList){ // ContainerValues -----------------------
        
                                if(!record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)]){
                                    record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)]={};
                                }
        
                                if(itemKeyObj.RowList.length>0){
                                    itemKeyObj.RowList.forEach(rowListObj=>{
                                        if(rowListObj.Values && rowListObj.Values.length>0){
                                            rowListObj.Values.forEach(rlObjVal=>{
                                                record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(rlObjVal.Name)] = ((rlObjVal.ValuesByLocale["en-US"]) ? rlObjVal.ValuesByLocale["en-US"] : rlObjVal.ValuesByLocale[""])
                                            });
                                        }
                                        if(rowListObj.MultiValues && rowListObj.MultiValues.length>0){
                                            rowListObj.MultiValues.forEach(rlObjVal=>{
                                                record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(rlObjVal.Name)] = ((rlObjVal.ValuesByLocale["en-US"]) ? rlObjVal.ValuesByLocale["en-US"] : rlObjVal.ValuesByLocale[""])
                                            });
                                        }
                                        
                                        if(rowListObj.ContainerValues && rowListObj.ContainerValues.length>0){
                                            rowListObj.ContainerValues.forEach(cvObj=>{
                                                if(!record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(cvObj.ParentAttribute)]){
                                                    record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(cvObj.ParentAttribute)]=[]
                                                }
                                                
                                                cvObj.RowList.forEach(cvObjRL=>{
                                                    let rowListObjectToSave={};
                                                    cvObjRL.Values.forEach(cvObjRLVal=>{
                                                        rowListObjectToSave[cvObjRLVal.Name] = ((cvObjRLVal.ValuesByLocale["en-US"]) ? cvObjRLVal.ValuesByLocale["en-US"] : cvObjRLVal.ValuesByLocale[""])      
                                                    });
                                                    record[safeKey(rootKey)][safeKey(itemKeyObj.ParentAttribute)][safeKey(cvObj.ParentAttribute)].push(rowListObjectToSave);
                                                })
                                                
                                                
                                            });                                    
                                        }
                                    });
                                }
        
                            }
                        });
        
                    }else if(typeof item[rootKey]==="object" && Object.keys(item[rootKey]).length>0){
                        //This is where I'll deal with nutritional data
                        if(!record[safeKey(rootKey)]){
                            record[safeKey(rootKey)]={};
                        }   
                        if(item[rootKey].CreatedDate){
                            Object.keys(item[rootKey]).forEach((irk)=>{
                                record[safeKey(rootKey)][safeKey(irk)]=item[rootKey][irk];
                            })
                        }                
        
                        if(item[rootKey].Values && item[rootKey].Values.length>0){
                            console.log(item[rootKey].Values)
                        } 
                        if(item[rootKey].MultiValues && item[rootKey].MultiValues.length>0){
                            console.log(item[rootKey].MultiValues)
                        }             
                        if(item[rootKey].ContainerValues && item[rootKey].ContainerValues.length>0){
                            item[rootKey].ContainerValues.forEach(cv=>{
                                
                                if(!record[safeKey(rootKey)][safeKey(cv.ParentAttribute)]){
                                    record[safeKey(rootKey)][safeKey(cv.ParentAttribute)]={};
                                } 
                                
                                cv.RowList.forEach(cvRowList=>{
                                    if(cvRowList.Values && cvRowList.Values.length>0){
                                        cvRowList.Values.forEach(cvRowListValue=>{  
                                            record[safeKey(rootKey)][safeKey(cv.ParentAttribute)][safeKey(cvRowListValue.Name)]=((cvRowListValue.ValuesByLocale["en-US"]) ? cvRowListValue.ValuesByLocale["en-US"] : cvRowListValue.ValuesByLocale[""]);                                    
                                        })                                                            
                                    }
        
                                    if(cvRowList.ContainerValues && cvRowList.ContainerValues.length>0){
        
                                        cvRowList.ContainerValues.forEach(cvRowListCV=>{
                                            if(!record[safeKey(rootKey)][safeKey(cv.ParentAttribute)][safeKey(cvRowListCV.ParentAttribute)]){
                                                record[safeKey(rootKey)][safeKey(cv.ParentAttribute)][safeKey(cvRowListCV.ParentAttribute)]=[];
                                            } 
        
                                            if(cvRowListCV.RowList && cvRowListCV.RowList.length>0){
                                                cvRowListCV.RowList.forEach(cvRowListCVRowList=>{
                                                    let RLO={};
                                                    if(cvRowListCVRowList.Values.length>0){
                                                       cvRowListCVRowList.Values.forEach(cvRowListCVRowListValues=>{
                                                        RLO[safeKey(cvRowListCVRowListValues.Name)] = ((cvRowListCVRowListValues.ValuesByLocale["en-US"]) ? cvRowListCVRowListValues.ValuesByLocale["en-US"] : cvRowListCVRowListValues.ValuesByLocale[""])
                                                       });
                                                      // record[rootKey][cv.ParentAttribute][cvRowListCV.ParentAttribute].push(RLO);                                               
                                                    }
                                                    if(cvRowListCVRowList.MultiValues.length>0){
                                                        console.log("MultiValues")
                                                    }
                                                    if(cvRowListCVRowList.ContainerValues.length>0){
                                                        cvRowListCVRowList.ContainerValues.forEach(cvRowListCVRowListCV=>{
                                                            //console.log(rootKey+" > "+cv.ParentAttribute+" > "+cvRowListCV.ParentAttribute+" > "+cvRowListCVRowListCV.ParentAttribute)
                                                            if(!RLO[safeKey(cvRowListCVRowListCV.ParentAttribute)]){
                                                                RLO[safeKey(cvRowListCVRowListCV.ParentAttribute)]={};
                                                            }
        
                                                            cvRowListCVRowListCV.RowList.forEach(cvRowListCVRowListCVRowList=>{
                                                                                                                                                                      
        
                                                                if(cvRowListCVRowListCVRowList.Values.length>0){
                                                                   
                                                                    cvRowListCVRowListCVRowList.Values.forEach(v=>{                                                                
                                                                        RLO[safeKey(cvRowListCVRowListCV.ParentAttribute)][safeKey(v.Name)]=((v.ValuesByLocale["en-US"]) ? v.ValuesByLocale["en-US"] : v.ValuesByLocale[""])                                                                
                                                                    });                                                            
         
                                                                    
                                                                }                                                        
                                                                if(cvRowListCVRowListCVRowList.ContainerValues.length>0){
                                                                   console.log("CONTAINER VALUES",cvRowListCVRowListCVRowList.ContainerValues)
                                                                }
                                                            })
                                                         //   console.log(cvRowListCVRowListCV.RowList)
                                                            
                                                        })
                                                        
                                                       
                                                    }
                                                    if(cvRowListCVRowList.AssetValues.length>0){
                                                        console.log("AssetValues")
                                                    }
        
                                                    record[safeKey(rootKey)][safeKey(cv.ParentAttribute)][safeKey(cvRowListCV.ParentAttribute)].push(RLO); 
                                                })
                                                //console.log(cvRowListCV.RowList);
                                                
        
                                            }
                                            //console.log(cvRowListCV)
                                            //console.log("---------")
                                            //process.exit();
                                        })
                                    }
        
                                    
                                })
                            })
                        }             
                        
                        
                    }
        
                    
                });

                output.push(record);
            });

            return output;
        },

        imageBase64:async function(url){
            return new Promise(async (resolve, reject) => {
                try{
                    let imageData = await fetch(url);
                    let buffer = await imageData.arrayBuffer();
                    resolve({"content":Buffer.from(buffer).toString('base64'), "contentType":imageData.headers.get('content-type')})
                }catch(e){
                    console.log(e);
                    reject({"content":"","contentType":""});
                }
            });                       
        },

        uom:function(v){
          if(!v){ return "";}
            switch(v.toUpperCase()){

                case "G21":
                    return "cup";
                break;
                case "EA":
                    return "each";
                break;
                case "OZA":
                    return "fl OZ";
                break;
                case "GLL":
                    return "gallon";
                break;
                case "LK":
                    return "link";
                break;
                case "PTL":
                    return "pint";
                break;
                case "ONZ":
                    return "oz";
                break;
                case "H87":
                    return "piece";
                break;
                case "LBR":
                    return "lb";
                break;
                case "QTL":
                    return "qrt";                
                break;
                case "G24":
                    return "tbs";
                break;
                case "G25":
                    return "tsp";
                break;
                case "DG":
                    return "dg";
                break;
                case "GRM":
                    return "g";
                break;
                case "KGM":
                    return "kg";
                break;
                case "LTR":
                    return "ltr";
                break;                
                case "MGM":
                    return "mg";
                break;
                case "MLT":
                    return "ml";
                break;
                case "E14":
                    return "cal"
                break;
                case "MC":
                    return "µg"
                break;
                case "INH":
                    return "in.";
                break;
                default:
                    return v;
                break;

            }
        },
        
        allergenCode:function(code){
            let mapping={
                "AC":"Crustaceans",
                "AE":"Eggs",
                "AF":"Fish",
                "AM":"Milk",
                "AN":"Tree Nuts",
                "AP":"Peanuts",
                "AW":"Gluten",
                "AX":"Gluten",
                "AY":"Soybeans",
                "GK":"Gluten",
                "ML":"Lactose",
                "SA":"Almonds",
                "SC":"Cashews",
                "SH":"Hazelnuts",
                "SM":"Macadamia Nuts",
                "SP":"Pecan Nuts",
                "SQ":"Queensland Nuts",
                "SR":"Brazil Nuts",
                "ST":"Pistachio",
                "SW":"Walnuts",
                "UM":"Molluscs",
                "UW":"Wheat",
                "GB":"Barley (Gluten Containing)",
                "GO":"Oats (Gluten Containing)",
                "GS":"Spelt (Gluten Containing)",
                "AS":"Sesame Seeds",
                "BM":"Mustard",
                "AA":"Amylcinnamyl Alcohol",
                "AH":"Anise Alcohol",
                "AI":"Alpha-Isomethyl Ionone",
                "AL":"Amyl Cinnamal",
                "AU":"Sulphur Dioxide",
                "BA":"Benzyl Alcohol",
                "BB":"Benzyl Benzoate",
                "BC":"Celery",
                "BE":"Butylphenyl Methylpropional",
                "BF":"Beef",
                "BI":"Benzyl Cinnamate",
                "BN":"Isoeugenol",
                "BO":"d-Limonene",
                "BP":"Linalool",
                "BQ":"Methyl heptin carbonate",
                "BS":"Benzyl Salicylate",
                "CA":"Cinnamyl Alcohol",
                "CL":"Cinnamal",
                "CM":"Chicken",
                "CN":"Citronellol",
                "CO":"Coumarin",
                "CT":"Citral",
                "EG":"Eugenol",
                "EP":"Oak Moss Extract",
                "EV":"Treemoss Extract",
                "FA":"Farnesol",
                "GE":"Geraniol",
                "GL":"Glutamate",
                "HC":"Hydroxymethylpentylcyclohexenecarboxaldehyde",
                "HX":"Hexyl Cinnamaldehyde",
                "HY":"Hydroxycitronellal",
                "MO":"3 Methyl-4-(2,6,6-trimethyl-2-cyclohexen-1-yl)-3-buten-2-on",
                "NC":"Cocoa",
                "NE":"Peas",
                "NK":"Coriander",
                "NL":"Lupines",
                "NM":"Corn",
                "NP":"Pod Fruits",
                "NR":"Rye",
                "NW":"Carrots",
                "PO":"Pork",
                "SB":"Seeds",
                "SX":"Pulses",
                "UN":"Shellfish",
                "X99":"Does not contain declaration obligatory allergens",
            };

            if(typeof mapping[code]!=="undefined"){
                return mapping[code] + " or " + mapping[code] + " Derivatives";
            }else{
                return code;
            }
        },

        country:function(criteria){
            let countries = [
                {
                  "country": "Afghanistan",
                  "abbr": "AF",
                  "abbr3": "AFG",
                  "code": 4
                },
                {
                  "country": "Aland Islands",
                  "abbr": "AX",
                  "abbr3": "ALA",
                  "code": 248
                },
                {
                  "country": "Albania",
                  "abbr": "AL",
                  "abbr3": "ALB",
                  "code": 8
                },
                {
                  "country": "Algeria",
                  "abbr": "DZ",
                  "abbr3": "DZA",
                  "code": 12
                },
                {
                  "country": "American Samoa",
                  "abbr": "AS",
                  "abbr3": "ASM",
                  "code": 16
                },
                {
                  "country": "Andorra",
                  "abbr": "AD",
                  "abbr3": "AND",
                  "code": 20
                },
                {
                  "country": "Angola",
                  "abbr": "AO",
                  "abbr3": "AGO",
                  "code": 24
                },
                {
                  "country": "Anguilla",
                  "abbr": "AI",
                  "abbr3": "AIA",
                  "code": 660
                },
                {
                  "country": "Antarctica",
                  "abbr": "AQ",
                  "abbr3": "ATA",
                  "code": 10
                },
                {
                  "country": "Antigua and Barbuda",
                  "abbr": "AG",
                  "abbr3": "ATG",
                  "code": 28
                },
                {
                  "country": "Argentina",
                  "abbr": "AR",
                  "abbr3": "ARG",
                  "code": 32
                },
                {
                  "country": "Armenia",
                  "abbr": "AM",
                  "abbr3": "ARM",
                  "code": 51
                },
                {
                  "country": "Aruba",
                  "abbr": "AW",
                  "abbr3": "ABW",
                  "code": 533
                },
                {
                  "country": "Australia",
                  "abbr": "AU",
                  "abbr3": "AUS",
                  "code": 36
                },
                {
                  "country": "Austria",
                  "abbr": "AT",
                  "abbr3": "AUT",
                  "code": 40
                },
                {
                  "country": "Azerbaijan",
                  "abbr": "AZ",
                  "abbr3": "AZE",
                  "code": 31
                },
                {
                  "country": "Bahamas",
                  "abbr": "BS",
                  "abbr3": "BHS",
                  "code": 44
                },
                {
                  "country": "Bahrain",
                  "abbr": "BH",
                  "abbr3": "BHR",
                  "code": 48
                },
                {
                  "country": "Bangladesh",
                  "abbr": "BD",
                  "abbr3": "BGD",
                  "code": 50
                },
                {
                  "country": "Barbados",
                  "abbr": "BB",
                  "abbr3": "BRB",
                  "code": 52
                },
                {
                  "country": "Belarus",
                  "abbr": "BY",
                  "abbr3": "BLR",
                  "code": 112
                },
                {
                  "country": "Belgium",
                  "abbr": "BE",
                  "abbr3": "BEL",
                  "code": 56
                },
                {
                  "country": "Belize",
                  "abbr": "BZ",
                  "abbr3": "BLZ",
                  "code": 84
                },
                {
                  "country": "Benin",
                  "abbr": "BJ",
                  "abbr3": "BEN",
                  "code": 204
                },
                {
                  "country": "Bermuda",
                  "abbr": "BM",
                  "abbr3": "BMU",
                  "code": 60
                },
                {
                  "country": "Bhutan",
                  "abbr": "BT",
                  "abbr3": "BTN",
                  "code": 64
                },
                {
                  "country": "Bolivia",
                  "abbr": "BO",
                  "abbr3": "BOL",
                  "code": 68
                },
                {
                  "country": "Bosnia and Herzegovina",
                  "abbr": "BA",
                  "abbr3": "BIH",
                  "code": 70
                },
                {
                  "country": "Botswana",
                  "abbr": "BW",
                  "abbr3": "BWA",
                  "code": 72
                },
                {
                  "country": "Bouvet Island",
                  "abbr": "BV",
                  "abbr3": "BVT",
                  "code": 74
                },
                {
                  "country": "Brazil",
                  "abbr": "BR",
                  "abbr3": "BRA",
                  "code": 76
                },
                {
                  "country": "British Virgin Islands",
                  "abbr": "VG",
                  "abbr3": "VGB",
                  "code": 92
                },
                {
                  "country": "British Indian Ocean Territory",
                  "abbr": "IO",
                  "abbr3": "IOT",
                  "code": 86
                },
                {
                  "country": "Brunei Darussalam",
                  "abbr": "BN",
                  "abbr3": "BRN",
                  "code": 96
                },
                {
                  "country": "Bulgaria",
                  "abbr": "BG",
                  "abbr3": "BGR",
                  "code": 100
                },
                {
                  "country": "Burkina Faso",
                  "abbr": "BF",
                  "abbr3": "BFA",
                  "code": 854
                },
                {
                  "country": "Burundi",
                  "abbr": "BI",
                  "abbr3": "BDI",
                  "code": 108
                },
                {
                  "country": "Cambodia",
                  "abbr": "KH",
                  "abbr3": "KHM",
                  "code": 116
                },
                {
                  "country": "Cameroon",
                  "abbr": "CM",
                  "abbr3": "CMR",
                  "code": 120
                },
                {
                  "country": "Canada",
                  "abbr": "CA",
                  "abbr3": "CAN",
                  "code": 124
                },
                {
                  "country": "Cape Verde",
                  "abbr": "CV",
                  "abbr3": "CPV",
                  "code": 132
                },
                {
                  "country": "Cayman Islands",
                  "abbr": "KY",
                  "abbr3": "CYM",
                  "code": 136
                },
                {
                  "country": "Central African Republic",
                  "abbr": "CF",
                  "abbr3": "CAF",
                  "code": 140
                },
                {
                  "country": "Chad",
                  "abbr": "TD",
                  "abbr3": "TCD",
                  "code": 148
                },
                {
                  "country": "Chile",
                  "abbr": "CL",
                  "abbr3": "CHL",
                  "code": 152
                },
                {
                  "country": "China",
                  "abbr": "CN",
                  "abbr3": "CHN",
                  "code": 156
                },
                {
                  "country": "Hong Kong, SAR China",
                  "abbr": "HK",
                  "abbr3": "HKG",
                  "code": 344
                },
                {
                  "country": "Macao, SAR China",
                  "abbr": "MO",
                  "abbr3": "MAC",
                  "code": 446
                },
                {
                  "country": "Christmas Island",
                  "abbr": "CX",
                  "abbr3": "CXR",
                  "code": 162
                },
                {
                  "country": "Cocos (Keeling) Islands",
                  "abbr": "CC",
                  "abbr3": "CCK",
                  "code": 166
                },
                {
                  "country": "Colombia",
                  "abbr": "CO",
                  "abbr3": "COL",
                  "code": 170
                },
                {
                  "country": "Comoros",
                  "abbr": "KM",
                  "abbr3": "COM",
                  "code": 174
                },
                {
                  "country": "Congo (Brazzaville)",
                  "abbr": "CG",
                  "abbr3": "COG",
                  "code": 178
                },
                {
                  "country": "Congo, (Kinshasa)",
                  "abbr": "CD",
                  "abbr3": "COD",
                  "code": 180
                },
                {
                  "country": "Cook Islands",
                  "abbr": "CK",
                  "abbr3": "COK",
                  "code": 184
                },
                {
                  "country": "Costa Rica",
                  "abbr": "CR",
                  "abbr3": "CRI",
                  "code": 188
                },
                {
                  "country": "Côte d'Ivoire",
                  "abbr": "CI",
                  "abbr3": "CIV",
                  "code": 384
                },
                {
                  "country": "Croatia",
                  "abbr": "HR",
                  "abbr3": "HRV",
                  "code": 191
                },
                {
                  "country": "Cuba",
                  "abbr": "CU",
                  "abbr3": "CUB",
                  "code": 192
                },
                {
                  "country": "Cyprus",
                  "abbr": "CY",
                  "abbr3": "CYP",
                  "code": 196
                },
                {
                  "country": "Czech Republic",
                  "abbr": "CZ",
                  "abbr3": "CZE",
                  "code": 203
                },
                {
                  "country": "Denmark",
                  "abbr": "DK",
                  "abbr3": "DNK",
                  "code": 208
                },
                {
                  "country": "Djibouti",
                  "abbr": "DJ",
                  "abbr3": "DJI",
                  "code": 262
                },
                {
                  "country": "Dominica",
                  "abbr": "DM",
                  "abbr3": "DMA",
                  "code": 212
                },
                {
                  "country": "Dominican Republic",
                  "abbr": "DO",
                  "abbr3": "DOM",
                  "code": 214
                },
                {
                  "country": "Ecuador",
                  "abbr": "EC",
                  "abbr3": "ECU",
                  "code": 218
                },
                {
                  "country": "Egypt",
                  "abbr": "EG",
                  "abbr3": "EGY",
                  "code": 818
                },
                {
                  "country": "El Salvador",
                  "abbr": "SV",
                  "abbr3": "SLV",
                  "code": 222
                },
                {
                  "country": "Equatorial Guinea",
                  "abbr": "GQ",
                  "abbr3": "GNQ",
                  "code": 226
                },
                {
                  "country": "Eritrea",
                  "abbr": "ER",
                  "abbr3": "ERI",
                  "code": 232
                },
                {
                  "country": "Estonia",
                  "abbr": "EE",
                  "abbr3": "EST",
                  "code": 233
                },
                {
                  "country": "Ethiopia",
                  "abbr": "ET",
                  "abbr3": "ETH",
                  "code": 231
                },
                {
                  "country": "Falkland Islands (Malvinas)",
                  "abbr": "FK",
                  "abbr3": "FLK",
                  "code": 238
                },
                {
                  "country": "Faroe Islands",
                  "abbr": "FO",
                  "abbr3": "FRO",
                  "code": 234
                },
                {
                  "country": "Fiji",
                  "abbr": "FJ",
                  "abbr3": "FJI",
                  "code": 242
                },
                {
                  "country": "Finland",
                  "abbr": "FI",
                  "abbr3": "FIN",
                  "code": 246
                },
                {
                  "country": "France",
                  "abbr": "FR",
                  "abbr3": "FRA",
                  "code": 250
                },
                {
                  "country": "French Guiana",
                  "abbr": "GF",
                  "abbr3": "GUF",
                  "code": 254
                },
                {
                  "country": "French Polynesia",
                  "abbr": "PF",
                  "abbr3": "PYF",
                  "code": 258
                },
                {
                  "country": "French Southern Territories",
                  "abbr": "TF",
                  "abbr3": "ATF",
                  "code": 260
                },
                {
                  "country": "Gabon",
                  "abbr": "GA",
                  "abbr3": "GAB",
                  "code": 266
                },
                {
                  "country": "Gambia",
                  "abbr": "GM",
                  "abbr3": "GMB",
                  "code": 270
                },
                {
                  "country": "Georgia",
                  "abbr": "GE",
                  "abbr3": "GEO",
                  "code": 268
                },
                {
                  "country": "Germany",
                  "abbr": "DE",
                  "abbr3": "DEU",
                  "code": 276
                },
                {
                  "country": "Ghana",
                  "abbr": "GH",
                  "abbr3": "GHA",
                  "code": 288
                },
                {
                  "country": "Gibraltar",
                  "abbr": "GI",
                  "abbr3": "GIB",
                  "code": 292
                },
                {
                  "country": "Greece",
                  "abbr": "GR",
                  "abbr3": "GRC",
                  "code": 300
                },
                {
                  "country": "Greenland",
                  "abbr": "GL",
                  "abbr3": "GRL",
                  "code": 304
                },
                {
                  "country": "Grenada",
                  "abbr": "GD",
                  "abbr3": "GRD",
                  "code": 308
                },
                {
                  "country": "Guadeloupe",
                  "abbr": "GP",
                  "abbr3": "GLP",
                  "code": 312
                },
                {
                  "country": "Guam",
                  "abbr": "GU",
                  "abbr3": "GUM",
                  "code": 316
                },
                {
                  "country": "Guatemala",
                  "abbr": "GT",
                  "abbr3": "GTM",
                  "code": 320
                },
                {
                  "country": "Guernsey",
                  "abbr": "GG",
                  "abbr3": "GGY",
                  "code": 831
                },
                {
                  "country": "Guinea",
                  "abbr": "GN",
                  "abbr3": "GIN",
                  "code": 324
                },
                {
                  "country": "Guinea-Bissau",
                  "abbr": "GW",
                  "abbr3": "GNB",
                  "code": 624
                },
                {
                  "country": "Guyana",
                  "abbr": "GY",
                  "abbr3": "GUY",
                  "code": 328
                },
                {
                  "country": "Haiti",
                  "abbr": "HT",
                  "abbr3": "HTI",
                  "code": 332
                },
                {
                  "country": "Heard and Mcdonald Islands",
                  "abbr": "HM",
                  "abbr3": "HMD",
                  "code": 334
                },
                {
                  "country": "Holy See (Vatican City State)",
                  "abbr": "VA",
                  "abbr3": "VAT",
                  "code": 336
                },
                {
                  "country": "Honduras",
                  "abbr": "HN",
                  "abbr3": "HND",
                  "code": 340
                },
                {
                  "country": "Hungary",
                  "abbr": "HU",
                  "abbr3": "HUN",
                  "code": 348
                },
                {
                  "country": "Iceland",
                  "abbr": "IS",
                  "abbr3": "ISL",
                  "code": 352
                },
                {
                  "country": "India",
                  "abbr": "IN",
                  "abbr3": "IND",
                  "code": 356
                },
                {
                  "country": "Indonesia",
                  "abbr": "ID",
                  "abbr3": "IDN",
                  "code": 360
                },
                {
                  "country": "Iran, Islamic Republic of",
                  "abbr": "IR",
                  "abbr3": "IRN",
                  "code": 364
                },
                {
                  "country": "Iraq",
                  "abbr": "IQ",
                  "abbr3": "IRQ",
                  "code": 368
                },
                {
                  "country": "Ireland",
                  "abbr": "IE",
                  "abbr3": "IRL",
                  "code": 372
                },
                {
                  "country": "Isle of Man",
                  "abbr": "IM",
                  "abbr3": "IMN",
                  "code": 833
                },
                {
                  "country": "Israel",
                  "abbr": "IL",
                  "abbr3": "ISR",
                  "code": 376
                },
                {
                  "country": "Italy",
                  "abbr": "IT",
                  "abbr3": "ITA",
                  "code": 380
                },
                {
                  "country": "Jamaica",
                  "abbr": "JM",
                  "abbr3": "JAM",
                  "code": 388
                },
                {
                  "country": "Japan",
                  "abbr": "JP",
                  "abbr3": "JPN",
                  "code": 392
                },
                {
                  "country": "Jersey",
                  "abbr": "JE",
                  "abbr3": "JEY",
                  "code": 832
                },
                {
                  "country": "Jordan",
                  "abbr": "JO",
                  "abbr3": "JOR",
                  "code": 400
                },
                {
                  "country": "Kazakhstan",
                  "abbr": "KZ",
                  "abbr3": "KAZ",
                  "code": 398
                },
                {
                  "country": "Kenya",
                  "abbr": "KE",
                  "abbr3": "KEN",
                  "code": 404
                },
                {
                  "country": "Kiribati",
                  "abbr": "KI",
                  "abbr3": "KIR",
                  "code": 296
                },
                {
                  "country": "Korea (North)",
                  "abbr": "KP",
                  "abbr3": "PRK",
                  "code": 408
                },
                {
                  "country": "Korea (South)",
                  "abbr": "KR",
                  "abbr3": "KOR",
                  "code": 410
                },
                {
                  "country": "Kuwait",
                  "abbr": "KW",
                  "abbr3": "KWT",
                  "code": 414
                },
                {
                  "country": "Kyrgyzstan",
                  "abbr": "KG",
                  "abbr3": "KGZ",
                  "code": 417
                },
                {
                  "country": "Lao PDR",
                  "abbr": "LA",
                  "abbr3": "LAO",
                  "code": 418
                },
                {
                  "country": "Latvia",
                  "abbr": "LV",
                  "abbr3": "LVA",
                  "code": 428
                },
                {
                  "country": "Lebanon",
                  "abbr": "LB",
                  "abbr3": "LBN",
                  "code": 422
                },
                {
                  "country": "Lesotho",
                  "abbr": "LS",
                  "abbr3": "LSO",
                  "code": 426
                },
                {
                  "country": "Liberia",
                  "abbr": "LR",
                  "abbr3": "LBR",
                  "code": 430
                },
                {
                  "country": "Libya",
                  "abbr": "LY",
                  "abbr3": "LBY",
                  "code": 434
                },
                {
                  "country": "Liechtenstein",
                  "abbr": "LI",
                  "abbr3": "LIE",
                  "code": 438
                },
                {
                  "country": "Lithuania",
                  "abbr": "LT",
                  "abbr3": "LTU",
                  "code": 440
                },
                {
                  "country": "Luxembourg",
                  "abbr": "LU",
                  "abbr3": "LUX",
                  "code": 442
                },
                {
                  "country": "Macedonia, Republic of",
                  "abbr": "MK",
                  "abbr3": "MKD",
                  "code": 807
                },
                {
                  "country": "Madagascar",
                  "abbr": "MG",
                  "abbr3": "MDG",
                  "code": 450
                },
                {
                  "country": "Malawi",
                  "abbr": "MW",
                  "abbr3": "MWI",
                  "code": 454
                },
                {
                  "country": "Malaysia",
                  "abbr": "MY",
                  "abbr3": "MYS",
                  "code": 458
                },
                {
                  "country": "Maldives",
                  "abbr": "MV",
                  "abbr3": "MDV",
                  "code": 462
                },
                {
                  "country": "Mali",
                  "abbr": "ML",
                  "abbr3": "MLI",
                  "code": 466
                },
                {
                  "country": "Malta",
                  "abbr": "MT",
                  "abbr3": "MLT",
                  "code": 470
                },
                {
                  "country": "Marshall Islands",
                  "abbr": "MH",
                  "abbr3": "MHL",
                  "code": 584
                },
                {
                  "country": "Martinique",
                  "abbr": "MQ",
                  "abbr3": "MTQ",
                  "code": 474
                },
                {
                  "country": "Mauritania",
                  "abbr": "MR",
                  "abbr3": "MRT",
                  "code": 478
                },
                {
                  "country": "Mauritius",
                  "abbr": "MU",
                  "abbr3": "MUS",
                  "code": 480
                },
                {
                  "country": "Mayotte",
                  "abbr": "YT",
                  "abbr3": "MYT",
                  "code": 175
                },
                {
                  "country": "Mexico",
                  "abbr": "MX",
                  "abbr3": "MEX",
                  "code": 484
                },
                {
                  "country": "Micronesia, Federated States of",
                  "abbr": "FM",
                  "abbr3": "FSM",
                  "code": 583
                },
                {
                  "country": "Moldova",
                  "abbr": "MD",
                  "abbr3": "MDA",
                  "code": 498
                },
                {
                  "country": "Monaco",
                  "abbr": "MC",
                  "abbr3": "MCO",
                  "code": 492
                },
                {
                  "country": "Mongolia",
                  "abbr": "MN",
                  "abbr3": "MNG",
                  "code": 496
                },
                {
                  "country": "Montenegro",
                  "abbr": "ME",
                  "abbr3": "MNE",
                  "code": 499
                },
                {
                  "country": "Montserrat",
                  "abbr": "MS",
                  "abbr3": "MSR",
                  "code": 500
                },
                {
                  "country": "Morocco",
                  "abbr": "MA",
                  "abbr3": "MAR",
                  "code": 504
                },
                {
                  "country": "Mozambique",
                  "abbr": "MZ",
                  "abbr3": "MOZ",
                  "code": 508
                },
                {
                  "country": "Myanmar",
                  "abbr": "MM",
                  "abbr3": "MMR",
                  "code": 104
                },
                {
                  "country": "Namibia",
                  "abbr": "NA",
                  "abbr3": "NAM",
                  "code": 516
                },
                {
                  "country": "Nauru",
                  "abbr": "NR",
                  "abbr3": "NRU",
                  "code": 520
                },
                {
                  "country": "Nepal",
                  "abbr": "NP",
                  "abbr3": "NPL",
                  "code": 524
                },
                {
                  "country": "Netherlands",
                  "abbr": "NL",
                  "abbr3": "NLD",
                  "code": 528
                },
                {
                  "country": "Netherlands Antilles",
                  "abbr": "AN",
                  "abbr3": "ANT",
                  "code": 530
                },
                {
                  "country": "New Caledonia",
                  "abbr": "NC",
                  "abbr3": "NCL",
                  "code": 540
                },
                {
                  "country": "New Zealand",
                  "abbr": "NZ",
                  "abbr3": "NZL",
                  "code": 554
                },
                {
                  "country": "Nicaragua",
                  "abbr": "NI",
                  "abbr3": "NIC",
                  "code": 558
                },
                {
                  "country": "Niger",
                  "abbr": "NE",
                  "abbr3": "NER",
                  "code": 562
                },
                {
                  "country": "Nigeria",
                  "abbr": "NG",
                  "abbr3": "NGA",
                  "code": 566
                },
                {
                  "country": "Niue",
                  "abbr": "NU",
                  "abbr3": "NIU",
                  "code": 570
                },
                {
                  "country": "Norfolk Island",
                  "abbr": "NF",
                  "abbr3": "NFK",
                  "code": 574
                },
                {
                  "country": "Northern Mariana Islands",
                  "abbr": "MP",
                  "abbr3": "MNP",
                  "code": 580
                },
                {
                  "country": "Norway",
                  "abbr": "NO",
                  "abbr3": "NOR",
                  "code": 578
                },
                {
                  "country": "Oman",
                  "abbr": "OM",
                  "abbr3": "OMN",
                  "code": 512
                },
                {
                  "country": "Pakistan",
                  "abbr": "PK",
                  "abbr3": "PAK",
                  "code": 586
                },
                {
                  "country": "Palau",
                  "abbr": "PW",
                  "abbr3": "PLW",
                  "code": 585
                },
                {
                  "country": "Palestinian Territory",
                  "abbr": "PS",
                  "abbr3": "PSE",
                  "code": 275
                },
                {
                  "country": "Panama",
                  "abbr": "PA",
                  "abbr3": "PAN",
                  "code": 591
                },
                {
                  "country": "Papua New Guinea",
                  "abbr": "PG",
                  "abbr3": "PNG",
                  "code": 598
                },
                {
                  "country": "Paraguay",
                  "abbr": "PY",
                  "abbr3": "PRY",
                  "code": 600
                },
                {
                  "country": "Peru",
                  "abbr": "PE",
                  "abbr3": "PER",
                  "code": 604
                },
                {
                  "country": "Philippines",
                  "abbr": "PH",
                  "abbr3": "PHL",
                  "code": 608
                },
                {
                  "country": "Pitcairn",
                  "abbr": "PN",
                  "abbr3": "PCN",
                  "code": 612
                },
                {
                  "country": "Poland",
                  "abbr": "PL",
                  "abbr3": "POL",
                  "code": 616
                },
                {
                  "country": "Portugal",
                  "abbr": "PT",
                  "abbr3": "PRT",
                  "code": 620
                },
                {
                  "country": "Puerto Rico",
                  "abbr": "PR",
                  "abbr3": "PRI",
                  "code": 630
                },
                {
                  "country": "Qatar",
                  "abbr": "QA",
                  "abbr3": "QAT",
                  "code": 634
                },
                {
                  "country": "Réunion",
                  "abbr": "RE",
                  "abbr3": "REU",
                  "code": 638
                },
                {
                  "country": "Romania",
                  "abbr": "RO",
                  "abbr3": "ROU",
                  "code": 642
                },
                {
                  "country": "Russian Federation",
                  "abbr": "RU",
                  "abbr3": "RUS",
                  "code": 643
                },
                {
                  "country": "Rwanda",
                  "abbr": "RW",
                  "abbr3": "RWA",
                  "code": 646
                },
                {
                  "country": "Saint-Barthélemy",
                  "abbr": "BL",
                  "abbr3": "BLM",
                  "code": 652
                },
                {
                  "country": "Saint Helena",
                  "abbr": "SH",
                  "abbr3": "SHN",
                  "code": 654
                },
                {
                  "country": "Saint Kitts and Nevis",
                  "abbr": "KN",
                  "abbr3": "KNA",
                  "code": 659
                },
                {
                  "country": "Saint Lucia",
                  "abbr": "LC",
                  "abbr3": "LCA",
                  "code": 662
                },
                {
                  "country": "Saint-Martin (French part)",
                  "abbr": "MF",
                  "abbr3": "MAF",
                  "code": 663
                },
                {
                  "country": "Saint Pierre and Miquelon",
                  "abbr": "PM",
                  "abbr3": "SPM",
                  "code": 666
                },
                {
                  "country": "Saint Vincent and Grenadines",
                  "abbr": "VC",
                  "abbr3": "VCT",
                  "code": 670
                },
                {
                  "country": "Samoa",
                  "abbr": "WS",
                  "abbr3": "WSM",
                  "code": 882
                },
                {
                  "country": "San Marino",
                  "abbr": "SM",
                  "abbr3": "SMR",
                  "code": 674
                },
                {
                  "country": "Sao Tome and Principe",
                  "abbr": "ST",
                  "abbr3": "STP",
                  "code": 678
                },
                {
                  "country": "Saudi Arabia",
                  "abbr": "SA",
                  "abbr3": "SAU",
                  "code": 682
                },
                {
                  "country": "Senegal",
                  "abbr": "SN",
                  "abbr3": "SEN",
                  "code": 686
                },
                {
                  "country": "Serbia",
                  "abbr": "RS",
                  "abbr3": "SRB",
                  "code": 688
                },
                {
                  "country": "Seychelles",
                  "abbr": "SC",
                  "abbr3": "SYC",
                  "code": 690
                },
                {
                  "country": "Sierra Leone",
                  "abbr": "SL",
                  "abbr3": "SLE",
                  "code": 694
                },
                {
                  "country": "Singapore",
                  "abbr": "SG",
                  "abbr3": "SGP",
                  "code": 702
                },
                {
                  "country": "Slovakia",
                  "abbr": "SK",
                  "abbr3": "SVK",
                  "code": 703
                },
                {
                  "country": "Slovenia",
                  "abbr": "SI",
                  "abbr3": "SVN",
                  "code": 705
                },
                {
                  "country": "Solomon Islands",
                  "abbr": "SB",
                  "abbr3": "SLB",
                  "code": 90
                },
                {
                  "country": "Somalia",
                  "abbr": "SO",
                  "abbr3": "SOM",
                  "code": 706
                },
                {
                  "country": "South Africa",
                  "abbr": "ZA",
                  "abbr3": "ZAF",
                  "code": 710
                },
                {
                  "country": "South Georgia and the South Sandwich Islands",
                  "abbr": "GS",
                  "abbr3": "SGS",
                  "code": 239
                },
                {
                  "country": "South Sudan",
                  "abbr": "SS",
                  "abbr3": "SSD",
                  "code": 728
                },
                {
                  "country": "Spain",
                  "abbr": "ES",
                  "abbr3": "ESP",
                  "code": 724
                },
                {
                  "country": "Sri Lanka",
                  "abbr": "LK",
                  "abbr3": "LKA",
                  "code": 144
                },
                {
                  "country": "Sudan",
                  "abbr": "SD",
                  "abbr3": "SDN",
                  "code": 736
                },
                {
                  "country": "Suriname",
                  "abbr": "SR",
                  "abbr3": "SUR",
                  "code": 740
                },
                {
                  "country": "Svalbard and Jan Mayen Islands",
                  "abbr": "SJ",
                  "abbr3": "SJM",
                  "code": 744
                },
                {
                  "country": "Swaziland",
                  "abbr": "SZ",
                  "abbr3": "SWZ",
                  "code": 748
                },
                {
                  "country": "Sweden",
                  "abbr": "SE",
                  "abbr3": "SWE",
                  "code": 752
                },
                {
                  "country": "Switzerland",
                  "abbr": "CH",
                  "abbr3": "CHE",
                  "code": 756
                },
                {
                  "country": "Syrian Arab Republic (Syria)",
                  "abbr": "SY",
                  "abbr3": "SYR",
                  "code": 760
                },
                {
                  "country": "Taiwan, Republic of China",
                  "abbr": "TW",
                  "abbr3": "TWN",
                  "code": 158
                },
                {
                  "country": "Tajikistan",
                  "abbr": "TJ",
                  "abbr3": "TJK",
                  "code": 762
                },
                {
                  "country": "Tanzania, United Republic of",
                  "abbr": "TZ",
                  "abbr3": "TZA",
                  "code": 834
                },
                {
                  "country": "Thailand",
                  "abbr": "TH",
                  "abbr3": "THA",
                  "code": 764
                },
                {
                  "country": "Timor-Leste",
                  "abbr": "TL",
                  "abbr3": "TLS",
                  "code": 626
                },
                {
                  "country": "Togo",
                  "abbr": "TG",
                  "abbr3": "TGO",
                  "code": 768
                },
                {
                  "country": "Tokelau",
                  "abbr": "TK",
                  "abbr3": "TKL",
                  "code": 772
                },
                {
                  "country": "Tonga",
                  "abbr": "TO",
                  "abbr3": "TON",
                  "code": 776
                },
                {
                  "country": "Trinidad and Tobago",
                  "abbr": "TT",
                  "abbr3": "TTO",
                  "code": 780
                },
                {
                  "country": "Tunisia",
                  "abbr": "TN",
                  "abbr3": "TUN",
                  "code": 788
                },
                {
                  "country": "Turkey",
                  "abbr": "TR",
                  "abbr3": "TUR",
                  "code": 792
                },
                {
                  "country": "Turkmenistan",
                  "abbr": "TM",
                  "abbr3": "TKM",
                  "code": 795
                },
                {
                  "country": "Turks and Caicos Islands",
                  "abbr": "TC",
                  "abbr3": "TCA",
                  "code": 796
                },
                {
                  "country": "Tuvalu",
                  "abbr": "TV",
                  "abbr3": "TUV",
                  "code": 798
                },
                {
                  "country": "Uganda",
                  "abbr": "UG",
                  "abbr3": "UGA",
                  "code": 800
                },
                {
                  "country": "Ukraine",
                  "abbr": "UA",
                  "abbr3": "UKR",
                  "code": 804
                },
                {
                  "country": "United Arab Emirates",
                  "abbr": "AE",
                  "abbr3": "ARE",
                  "code": 784
                },
                {
                  "country": "United Kingdom",
                  "abbr": "GB",
                  "abbr3": "GBR",
                  "code": 826
                },
                {
                  "country": "United States of America",
                  "abbr": "US",
                  "abbr3": "USA",
                  "code": 840
                },
                {
                  "country": "US Minor Outlying Islands",
                  "abbr": "UM",
                  "abbr3": "UMI",
                  "code": 581
                },
                {
                  "country": "Uruguay",
                  "abbr": "UY",
                  "abbr3": "URY",
                  "code": 858
                },
                {
                  "country": "Uzbekistan",
                  "abbr": "UZ",
                  "abbr3": "UZB",
                  "code": 860
                },
                {
                  "country": "Vanuatu",
                  "abbr": "VU",
                  "abbr3": "VUT",
                  "code": 548
                },
                {
                  "country": "Venezuela (Bolivarian Republic)",
                  "abbr": "VE",
                  "abbr3": "VEN",
                  "code": 862
                },
                {
                  "country": "Viet Nam",
                  "abbr": "VN",
                  "abbr3": "VNM",
                  "code": 704
                },
                {
                  "country": "Virgin Islands, US",
                  "abbr": "VI",
                  "abbr3": "VIR",
                  "code": 850
                },
                {
                  "country": "Wallis and Futuna Islands",
                  "abbr": "WF",
                  "abbr3": "WLF",
                  "code": 876
                },
                {
                  "country": "Western Sahara",
                  "abbr": "EH",
                  "abbr3": "ESH",
                  "code": 732
                },
                {
                  "country": "Yemen",
                  "abbr": "YE",
                  "abbr3": "YEM",
                  "code": 887
                },
                {
                  "country": "Zambia",
                  "abbr": "ZM",
                  "abbr3": "ZMB",
                  "code": 894
                },
                {
                  "country": "Zimbabwe",
                  "abbr": "ZW",
                  "abbr3": "ZWE",
                  "code": 716
                }
            ];

            return _.find(countries,criteria)
        },

        fullImageSrc:function(src){
          if(!src || src.indexOf("http")>=0){
            return src;
          }else{
            return "https://assets.edgenet.com/"+src+"?originalFileName=true"
          }
        }
}}

