let shipstation = require(process.cwd()+"/libs/shipstation.js").create({
    "apiKey": process.env.SHIPSTATION_V1_API_KEY,
    "apiSecret": process.env.SHIPSTATION_V1_API_SECRET
});

/**********************************************
 * P={
    * order,
    * items,
    * session,
    * org
 * }
 * 
 * first_name
 * last_name
 * company
 * street1
 * street2
 * city
 * state
 * postalCode
 * country
 * phone
 * notes
 ***********************************************/
module.exports = {

    // Create order function (your existing logic)
    createUpdate: async function(P) {
        try {
            let shipTo = {};
            Object.keys(P.org.data_configs.orders.fields).forEach(fKey => {
                if(P.org.data_configs.orders.fields[fKey].shipping) {
                    let shippingField = P.org.data_configs.orders.fields[fKey].shipping.field.split(".");
                    if(shippingField[0] === "shipTo") {
                        shipTo[shippingField[1]] = fKey;
                    }
                }
            });

            let orderToSend = {
                "orderNumber": P.order._id.toString(),
                "orderKey": P.order._id.toString(),
                "orderDate": new Date(),
                "orderStatus": "awaiting_shipment",
                "shipTo": {
                    "name": ((P.order.fields[shipTo.name]) ? P.order.fields[shipTo.name] : (P.order.fields[shipTo.first_name] || '') + ' ' + (P.order.fields[shipTo.last_name] || '')).trim(),
                    "company": P.order.fields[shipTo.company] || '',
                    "street1": P.order.fields[shipTo.street1] || '',
                    "street2": P.order.fields[shipTo.street2] || '',
                    "city": P.order.fields[shipTo.city] || '',
                    "state": P.order.fields[shipTo.state] || '',
                    "postalCode": P.order.fields[shipTo.postalCode] || '',
                    "country": P.order.fields[shipTo.country] || 'US',
                    "phone": P.order.fields[shipTo.phone] || '',
                },
                "billTo": {
                    "name": "Fulfillment Manager",
                    "company": P.org.name,
                    "street1": "530 Fillmore Avenue",
                    "street2": "",
                    "city": "Tonawanda",
                    "state": "NY",
                    "postalCode": '14150',
                    "country": 'US',
                    "phone": '************'
                },
                "items": []                
            };            
        
            if(P.order.org_tag) {
                orderToSend.tagIds = [P.order.org_tag];
            }

            if(P.order.fields[shipTo.notes]) {
                orderToSend.internalNotes = P.order.fields[shipTo.notes];
            }            

            P.items.forEach(function(item) { 
                orderToSend.items.push({
                    "lineItemKey": item._id.toString(),
                    "name": item.name || 'N/A',
                    "quantity": item.qty
                });
            });
            
            let response = await shipstation.createOrder(orderToSend);
            
            if(response && response.orderId) {
                return {"success": true, "id": response.orderId, "status": response.orderStatus};
            } else {
                return {"success": false, "error": "No response from ShipStation"};
            }           

        } catch(err) {
            console.log("Error in ShipStation createUpdate:", err);
            return {"success": false, "error": err.toString()};
        }
    },

    // Delete order function
    delete: async function(orderId) {
        try {
            let response = await shipstation.deleteOrder(orderId);

            if(response) {
                return {"success": true, "message": "Order deleted successfully"};
            } else {
                return {"success": false, "error": "Failed to delete order"};
            }

        } catch(err) {
            return {"success": false, "error": err.toString()};
        }
    },

    // Get order function
    get: async function(orderId) {
        try {
            let response = await shipstation.getOrder(orderId);

            if(response) {
                return {"success": true, "order": response};
            } else {
                return {"success": false, "error": "Order not found"};
            }

        } catch(err) {
            return {"success": false, "error": err.toString()};
        }
    }

};


                        
                        