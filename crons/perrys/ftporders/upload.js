const Client = require('ssh2-sftp-client');
const sftp = new Client();
const fs = require('fs');
const dayjs = require('dayjs');
const csv = require("fast-csv");
const mdb = require(process.cwd()+"/libs/mdb.js");
const aws = require(process.cwd()+"/libs/aws.js");

const ORGID = mdb.objectId("68e02920e84a00d98da3f19f");

/*******************************************************  
 * Step 1:
 * Query the DB to get orders marked as "shipped" but not "meta.uploaded"
 * 
 * Step 2:
 * Get the items associated with this order
 * 
 * Step 3:
 * Assemble the record to match the CSV output
 * 
 * Step 4: 
 * Write the CSV to a file
 * 
 * Step 5: 
 * Upload the file to SFTP
 * 
 * Step 6:
 * Update the order record to mark status as "complete" and "meta.uploaded" with timestamp
 * 
 * OrderNo,
 * OrderDatetime,
 * Name,
 * Addr1,
 * Addr2,
 * City,
 * State,
 * ZipCode,
 * OrderStatus,
 * ItemNumber,
 * ItemDescription,
 * OrderCaseQty,
 * OrderEachQty,
 * PickedCase,
 * PickedEach,
 * ShipDate,
 * Shipper,
 * Tracking
 *******************************************************/

function friendlyCarrierMapping(str){
    switch(str.toLowerCase()){
        case "ups_walleted":
            return "UPS";
        default:
            return str;
    }
}

module.exports=async (PARAMS)=>{
    let dataToFind={
        "org_id":ORGID,
        "shipping.shipped":true,
        "meta.uploaded":{"$exists":false}
    }

    let emailContent={ "orderCount":0 };

    let orderIdsToUpdate=[];
    
    let orders = await mdb.client().collection("orders").find(dataToFind).toArray();

    console.log("ORDERS:",orders.length);

    if(orders && orders.length>0){ 
        
        emailContent.orderCount=orders.length;
        
        let csvData=[];
        for(let order of orders){
            orderIdsToUpdate.push(order._id);

            let itemIds = Object.keys(order.items).map(i=>mdb.objectId(i));
            
            let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemIds}}).toArray();            

            for(let item of orderItems){
                csvData.push({
                    "OrderNo":order.fields.CLIENTOID,
                    "OrderDatetime":order.created,
                    "Name":(order.fields.COMPANY).trim(),
                    "Addr1":order.fields.STREET,
                    "Addr2":order.fields.STREET_2,
                    "City":order.fields.CITY,
                    "State":order.fields.STATE,
                    "ZipCode":order.fields.ZIP,
                    "OrderStatus":1,
                    "ItemNumber":item.fields.ITEMNUM,
                    "ItemDescription":item.name,
                    "OrderCaseQty":0,
                    "OrderEachQty":order.items[item._id.toString()].qty,
                    "PickedCase":0,
                    "PickedEach":order.items[item._id.toString()].qty,
                    "ShipDate":order.shipping.ship_date ? order.shipping.ship_date : new Date(),
                    "Shipper":friendlyCarrierMapping(order.shipping.carrier) || "",
                    "Tracking":`${order.shipping.tracking_number || '""'}`
                });
            }
        }
            
            let remoteFileName = `BMGExport_${dayjs().format("MM_DD_YYYY_HHmmss")}.csv`;
            let localFilePath = process.cwd()+`/tmp/${remoteFileName}`;            
            let remotePath = `./Receive/${remoteFileName}`;

            console.log("WRITING CSV");

            await csv.write(csvData, { headers: true, quoteColumns: {"Tracking":true} }).pipe(fs.createWriteStream(localFilePath));

            
            await sftp.connect({
                host: 'edi.perrysicecream.com',
                port: 22,
                username: process.env["PERRYS_FTP_USER"],
                password: process.env["PERRYS_FTP_PASS"]                
            });

            await sftp.put(localFilePath, remotePath);
            await sftp.end();
                        
            await mdb.client().collection("orders").updateMany({ "_id":{ "$in":orderIdsToUpdate } },{
                "$set":{
                    "meta.uploaded":new Date(),
                    "status":"aXFdK"
                }
            });
            
            console.log("Sending Email");
            
            await aws.email({
                to: "<EMAIL>",
                subject: `${emailContent.orderCount} Shipped Orders Sent To Perrys`,
                body: "Results file attached",
                attachments: [{
                    "filename":remoteFileName,
                    "path":localFilePath
                }]
            });

            console.log("Done");
            
            if(!process.env.ISDEV){
                fs.unlinkSync(localFilePath);
            }    
        
    }

    

};