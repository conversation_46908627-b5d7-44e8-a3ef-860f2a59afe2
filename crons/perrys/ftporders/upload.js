const Client = require('ssh2-sftp-client');
const sftp = new Client();
const fs = require('fs');
const dayjs = require('dayjs');
const csv = require("fast-csv");
const mdb = require(process.cwd()+"/libs/mdb.js");

const ORGID = mdb.objectId("68bf16a594f57ff549ef5bee");

/*******************************************************
 * Step 1:
 * Query the DB to get orders marked as "complete" but not "meta.uploaded"
 * 
 * Step 2:
 * Get the items associated with this order
 * 
 * Step 3:
 * Assemble the record to match the CSV output
 * 
 * Step 4: 
 * Write the CSV to a file
 * 
 * Step 5: 
 * Upload the file to SFTP
 * 
 * Step 6:
 * Update the order record to mark "meta.uploaded" with timestamp
 *******************************************************/
module.exports=async (PARAMS)=>{

};