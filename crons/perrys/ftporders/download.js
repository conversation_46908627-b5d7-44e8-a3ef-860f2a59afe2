/*
    1. Connect to SFTP and download all files to TMP folder
    2. Upload CSVs to S3
    3. Parse S3 files:
        a. Insert or get ID of order items
        b. Insert or get ID of operators    
        c. Insert or get ID of orders
        d. Create Shipstation orders
*/

const Client = require('ssh2-sftp-client');
const sftp = new Client();
const fs = require('fs');
const dayjs = require('dayjs');
const csv = require("fast-csv");
const mdb = require(process.cwd()+"/libs/mdb.js");
const orderProcessor = require(process.cwd()+"/libs/orders.js");

const ORGID = mdb.objectId("68e02920e84a00d98da3f19f");

const SESSION = {
  "org_id": ORGID,
  "account_id": mdb.objectId("68e02a21e84a00d98da3f1a8"),
  "email": "<EMAIL>",
  "tags": [ "bmg","admin", "dev" ],
  "created": new Date("2025-09-29T16:43:53.451Z"),
}

const ORG = mdb.client().collection("orgs").findOne({"_id":ORGID});

module.exports=async (PARAMS)=>{
    
    sftp.connect({
        host: "upload.medstatix.com",//'edi.perrysicecream.com',
        port: '22',
        username: "nj-capital-health-ranking", //process.env["PERRYS_FTP_USER"],
        password: "jM&3q0KxQ*nkljT0", //process.env["PERRYS_FTP_PASS"]
    }).then(() => {
        return sftp.list('/test');
    }).then(async data => {
        console.log(data, 'the data info');
        let filesToProcess = [];
        for(let file of data){
            filesToProcess.push(file.name);
            await sftp.get('/test/'+file.name, process.cwd()+'/tmp/'+file.name);
        }        
        processFiles(filesToProcess);
    }).catch(err => {
        console.log(err, 'catch error');
    });


    async function processFile(file){
        return new Promise(async (resolve, reject)=>{
            let records = [];
            fs.createReadStream(`${process.cwd()}/tmp/${file}`)
                .pipe(csv.parse({ headers: true }))
                .on('error', error => {
                    console.error(error);
                    reject(error);
                })
                .on('data', row => records.push(row))
                .on('end', rowCount => { 
                    resolve(records);
                });
        });
    }

    async function processFiles(filesToProcess) {

        for(let file of filesToProcess){
            let records = await processFile(file);
            let order={};            

            for(let record of records){
                record["OrderNo"] = record["OrderNo"].trim();
                if(record["OrderNo"].length>0){

                   if(typeof order[record["OrderNo"]]==="undefined"){
                        order[record["OrderNo"]]={                            
                            "status":"submitted",
                            "fields":{
                                "FNAME":"",
                                "LNAME":"",
                                "EMAIL":"",
                                "COMPANY":record["Name"].trim(),
                                "STREET":record["Addr1"].trim(),
                                "STREET_2":record["Addr2"].trim(),
                                "CITY":record["City"].trim(),
                                "STATE":record["State"].trim(),
                                "ZIP":record["ZipCode"].toString().trim(),
                                "PHONE":"",
                                "CLIENTOID":record["OrderNo"]
                            },
                            "items":{},
                            "notes":[],
                            "meta":{
                                "ftp":file,
                                "perrys":true
                            },
                            "created":dayjs(record["OrderDatetime"]).toDate(),
                            "updated":new Date()
                        };
                    }

                    // Check if item exists, if not create it
                    let hubItem = await mdb.client().collection("items").findOne({"fields.ITEMNUM":record["ItemNumber"]});
                    let hubItemId=null;

                    if(!hubItem){
                        let newItemId = mdb.objectId();
                        let newItem = {
                            "_id":newItemId,
                            "org_id":ORGID,
                            "name":record["ItemDescription"].trim(),
                            "image":"",
                            "actions":{
                                "link":true,
                                "download":false,
                                "email":false,
                                "order":true
                            },
                            "fields":{
                                "ITEMNUM":record["ItemNumber"].toString().trim()
                            },
                            "tags":[],
                            "inventory":{
                                "restock_level":0,
                                "oos_action":"-"
                            },
                            "status":1,
                            "meta":{
                                "ftp":file                                
                            },
                            "files":{
                                "base":{}
                            },
                            "visible_to":[],
                            "created":new Date(),
                            "updated":new Date()
                        };
                        //await mdb.client().collection("items").insertOne(newItem);
                        hubItemId = newItemId;
                    }else{
                        hubItemId = hubItem._id;
                    }

                    if(Number(record["OrderCaseQty"])>0){
                        order[record["OrderNo"]].items[hubItemId.toString()]={
                            "qty":Number(record["OrderCaseQty"]),
                            "unit":"case"
                        };
                    }else if(Number(record["OrderEachQty"])>0){
                        order[record["OrderNo"]].items[hubItemId.toString()]={
                            "qty":Number(record["OrderEachQty"]),
                            "unit":"each"
                        };
                    }


                    let orderKeys = Object.keys(order);
                    for(let ok of orderKeys){                        
                        //await orderProcessor.create(order[ok], SESSION, ORG);
                    }
                    

                    
                    
                }
            }

            
            console.log(JSON.stringify(order));

        }

    }

   // processFiles(["BMGExport_06_30_2025_123000.csv"])

}