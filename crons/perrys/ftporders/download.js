/*
    1. Connect to SFTP and download all files to TMP folder
    2. Upload CSVs to S3
    3. Parse S3 files:
        a. Insert or get ID of order items
        b. Insert or get ID of operators    
        c. Insert or get ID of orders
        d. Create Shipstation orders
*/

const Client = require('ssh2-sftp-client');
const sftp = new Client();
const fs = require('fs');
const dayjs = require('dayjs');
const csv = require("fast-csv");
const mdb = require(process.cwd()+"/libs/mdb.js");

const ORGID = mdb.objectId("68bf16a594f57ff549ef5bee");


module.exports=async (PARAMS)=>{

/*
    sftp.connect({
        host: 'edi.perrysicecream.com',
        port: '22',
        username: process.env["PERRYS_FTP_USER"],
        password: process.env["PERRYS_FTP_PASS"]
    }).then(() => {
        return sftp.list('/');
    }).then(data => {
        console.log(data, 'the data info');
        //sftp.get('/path/to/remote/file', '/path/to/local/file');
        //Get the files from FTP and move them to tmp
        //Then upload to S3
        //Run processFiles()
    }).catch(err => {
        console.log(err, 'catch error');
    });
*/

    async function processFile(file){
        return new Promise(async (resolve, reject)=>{
            let records = [];
            fs.createReadStream(`${process.cwd()}/tmp/${file}`)
                .pipe(csv.parse({ headers: true }))
                .on('error', error => {
                    console.error(error);
                    reject(error);
                })
                .on('data', row => records.push(row))
                .on('end', rowCount => { 
                    resolve(records);
                });
        });
    }

    async function processFiles(filesToProcess) {

        for(let file of filesToProcess){
            let records = await processFile(file);
            let order={};            

            for(let record of records){
                record["OrderNo"] = record["OrderNo"].trim();
                if(record["OrderNo"].length>0){

                   if(typeof order[record["OrderNo"]]==="undefined"){
                        order[record["OrderNo"]]={
                            "org_id":ORGID,
                            "account_id":mdb.objectId("68bf172394f57ff549ef5bf1"),
                            "status":"submitted",
                            "fields":{
                                "FNAME":"",
                                "LNAME":"",
                                "EMAIL":"",
                                "COMPANY":record["Name"].trim(),
                                "STREET":record["Addr1"].trim(),
                                "STREET_2":record["Addr2"].trim(),
                                "CITY":record["City"].trim(),
                                "STATE":record["State"].trim(),
                                "ZIP":record["ZipCode"].toString().trim(),
                                "PHONE":"",
                                "CLIENTOID":record["OrderNo"]
                            },
                            "items":{},
                            "notes":[],
                            "meta":{
                                "ftp":file,
                                "uploaded":false
                            },
                            "created":dayjs(record["OrderDatetime"]).toDate(),
                            "updated":new Date()
                        };
                    }

                    // Check if item exists, if not create it
                    let hubItem = await mdb.client().collection("items").findOne({"fields.ITEMNUM":record["ItemNumber"]});
                    let hubItemId=null;

                    if(!hubItem){
                        let newItemId = mdb.objectId();
                        let newItem = {
                            "_id":newItemId,
                            "org_id":ORGID,
                            "name":record["ItemDescription"].trim(),
                            "image":"",
                            "actions":{
                                "link":true,
                                "download":false,
                                "email":false,
                                "order":true
                            },
                            "fields":{
                                "ITEMNUM":record["ItemNumber"].toString().trim()
                            },
                            "tags":[],
                            "inventory":{
                                "restock_level":0,
                                "oos_action":"-"
                            },
                            "status":1,
                            "meta":{
                                "ftp":file                                
                            },
                            "files":{
                                "base":{}
                            },
                            "visible_to":[],
                            "created":new Date(),
                            "updated":new Date()
                        };
                        await mdb.client().collection("items").insertOne(newItem);
                        hubItemId = newItem;
                    }else{
                        hubItemId = hubItem._id;
                    }

                    if(Number(record["OrderCaseQty"])>0){
                        order[record["OrderNo"]].items[hubItemId]={
                            "qty":Number(record["OrderCaseQty"]),
                            "unit":"case"
                        };
                    }else if(Number(record["OrderEachQty"])>0){
                        order[record["OrderNo"]].items[hubItemId]={
                            "qty":Number(record["OrderEachQty"]),
                            "unit":"each"
                        };
                    }


                    
                    
                }
            }

            console.log(order);

        }

    }

    processFiles(["BMGExport_06_30_2025_123000.csv"])

}