/*
    1. Connect to SFTP and download all files to TMP folder
    2. Upload CSVs to S3
    3. Parse S3 files:
        a. Insert or get ID of order items
        b. Insert or get ID of operators    
        c. Insert or get ID of orders
        d. Create Shipstation orders
*/

const Client = require('ssh2-sftp-client');
const sftp = new Client();
const fs = require('fs');
const dayjs = require('dayjs');
const csv = require("fast-csv");
const { isArray } = require('lodash');
const mdb = require(process.cwd()+"/libs/mdb.js");
const aws = require(process.cwd()+"/libs/aws.js");
const orderProcessor = require(process.cwd()+"/libs/orders.js");

const ORGID = mdb.objectId("68e02920e84a00d98da3f19f");

const SESSION = {
  "org_id": ORGID,
  "account_id": mdb.objectId("68e02a21e84a00d98da3f1a8"),
  "email": "<EMAIL>",
  "tags": [ "bmg","admin", "dev" ],
  "created": new Date("2025-09-29T16:43:53.451Z"),
}

let emailContent={
    "orderCount":0,
    "pdfFiles":[]
};

module.exports=async (PARAMS)=>{
    const ORG = await mdb.client().collection("orgs").findOne({"_id":ORGID});
    
    /*sftp.connect({
        host: 'edi.perrysicecream.com',
        port: '22',
        username: process.env["PERRYS_FTP_USER"],
        password: process.env["PERRYS_FTP_PASS"] 
    }).then(() => {
        return sftp.list('./Send');
    }).then(async data => {
        //console.log(data, 'the data info');
        let filesToProcess = [];
        for(let file of data){
            filesToProcess.push(file.name);            
            await sftp.get('./Send/'+file.name, process.cwd()+'/tmp/'+file.name);
        }        
        await processFiles(filesToProcess);
        await sftp.end();
        console.log("SFTP Disconnected");
    }).catch(err => {
        console.log(err, 'catch error');
    });*/

    //=== Call our FTP Relay ======================================================
    /*let ftpResponse = await fetch(process.env["PDF_SERVER_URL"]+'/sftp/download', {
        method: 'post',
        body: JSON.stringify({
            "ftp_host":"edi.perrysicecream.com",
            "ftp_user":process.env["PERRYS_FTP_USER"],
            "ftp_pass":process.env["PERRYS_FTP_PASS"],
            "local_path":"PERRYS/",
            "remote_path":"./Send/"
        }),
        headers: {'Content-Type': 'application/json'}
    });*/

    let ftpResponse=["BMGExport_12_02_2025_123000.csv", "BMGExport_12_02_2025_123000_1085922.pdf"];

    if(ftpResponse && isArray(ftpResponse)){
        for(let file of ftpResponse){
            let r = await aws.getS3Object({
                "key":`ftp/PERRYS/${file}`
            })
            console.log(r);
            //await processFile(file);
        }
    }

    


    async function processFile(file){
        return new Promise(async (resolve, reject)=>{
            let records = [];
            fs.createReadStream(`${process.cwd()}/tmp/${file}`)
                .pipe(csv.parse({ headers: true }))
                .on('error', error => {
                    console.error(error);
                    reject(error);
                })
                .on('data', row => records.push(row))
                .on('end', rowCount => { 
                    resolve(records);
                });
        });
    }

    async function processFiles(filesToProcess) {

        for(let file of filesToProcess){
            if(file.indexOf(".pdf")>0){ 
                
                emailContent.pdfFiles.push({
                    "filename":file,
                    "path":`${process.cwd()}/tmp/${file}`
                });

            }else{
                let records = await processFile(file);
                let order={};            

                for(let record of records){
                    record["OrderNo"] = record["OrderNo"].trim();
                    if(record["OrderNo"].length>0){

                    if(typeof order[record["OrderNo"]]==="undefined"){
                            order[record["OrderNo"]]={                            
                                "status":"submitted",
                                "fields":{
                                    "FNAME":"",
                                    "LNAME":"",
                                    "EMAIL":"",
                                    "COMPANY":record["Name"].trim(),
                                    "STREET":record["Addr1"].trim(),
                                    "STREET_2":record["Addr2"].trim(),
                                    "CITY":record["City"].trim(),
                                    "STATE":record["State"].trim(),
                                    "ZIP":record["ZipCode"].toString().trim(),
                                    "PHONE":"",
                                    "CLIENTOID":record["OrderNo"]
                                },
                                "items":{},
                                "notes":[],
                                "meta":{
                                    "ftp":file,
                                    "perrys":true
                                },
                                "org_tag":"PERRYS",
                                "created":dayjs(record["OrderDatetime"]).toDate(),
                                "updated":new Date()
                            };
                        }

                        // Check if item exists, if not create it
                        let hubItem = await mdb.client().collection("items").findOne({"fields.WAREHOUSEID":record["ItemNumber"]});
                        let hubItemId=null;

                        if(!hubItem){
                            let newItemId = mdb.objectId();
                            let newItem = {
                                "_id":newItemId,
                                "org_id":ORGID,
                                "name":record["ItemDescription"].trim(),
                                "image":"",
                                "actions":{
                                    "link":true,
                                    "download":false,
                                    "email":false,
                                    "order":true
                                },
                                "fields":{
                                    "WAREHOUSEID":record["ItemNumber"].toString().trim()
                                },
                                "tags":[],
                                "inventory":{
                                    "restock_level":0,
                                    "oos_action":"-"
                                },
                                "status":1,
                                "meta":{
                                    "ftp":file                                
                                },
                                "files":{
                                    "base":{}
                                },
                                "visible_to":[],
                                "created":new Date(),
                                "updated":new Date()
                            };
                            await mdb.client().collection("items").insertOne(newItem);
                            hubItemId = newItemId;
                        }else{
                            hubItemId = hubItem._id;
                        }

                        if(Number(record["OrderCaseQty"])>0){
                            order[record["OrderNo"]].items[hubItemId.toString()]={
                                "qty":Number(record["OrderCaseQty"]),
                                "unit":"case"
                            };
                        }else if(Number(record["OrderEachQty"])>0){
                            order[record["OrderNo"]].items[hubItemId.toString()]={
                                "qty":Number(record["OrderEachQty"]),
                                "unit":"each"
                            };
                        }                        
                                                        
                    }                    
                }            
                    
                emailContent.orderCount+=Object.keys(order).length;

                for(let ok of Object.keys(order)){
                    await orderProcessor.create(order[ok], SESSION, ORG, true);
                }
            }            

        }

        aws.email({
            to: "<EMAIL>",
            subject: `${emailContent.orderCount} Perrys Orders Imported`,
            body: "Order PDF(s) attached",
            attachments: emailContent.pdfFiles
        });

        console.log("Done");


    }

   // processFiles(["BMGExport_12_03_2025_003000.csv", "BMGExport_12_02_2025_123000.csv", "BMGExport_12_02_2025_123000_1085922.pdf"]);

}