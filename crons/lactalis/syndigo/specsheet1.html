<html>
    <head>
        <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
        <style>
            html{
                padding:0px;                
            }
            body{
                padding:20px 20px 20px 5px;
                border:1px solid #000;
            }
            p{
                margin-top:0px;
                font-size:0.9rem;
            }
            h1{
                font-size: 1.2rem;
                font-weight:bold;
                color:#666;
            }
            .productDescription{
                font-size:0.70rem;
                color:#666;
            }
            #logoHolder{
                text-align: center;                
            }
            .sectionHead{
                background:#a3a3a3;
                font-weight: bold;
                color:#fff;
                padding:3px 5px;
                font-size:13px;
            }
            table.caseSpecs{
                width:100%;
                font-size:11px;
                margin-bottom: 15px;
            }
            table.caseSpecs td{
                border:1px solid rgb(211, 210, 210);
                width:25%;
            }
            table.caseSpecs td.label{
                border:1px solid #333;
                font-weight: bold;
            }            
            .smText{
                font-size:9px;
            }
            .bigBottomBorder{
                border-bottom: 10px solid #000;
            }
            .lessBigBottomBorder{
                border-bottom: 6px solid #000;
            }
            .details{
                font-size:12px;
                margin:0 0 10px 5px;
            }
            .bb{
                border-bottom:1px solid #000;
            }
            .indent{
                margin-left:10px;
            }
            .bullet{
                margin-left:10px;
                margin-right:20px;
            }

            .flex{
                display:flex;
            }

            @media print {
                .noBreak {                
                    break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="w3-container">
            <div class="w3-row">
                <div class="w3-col s3 m3 logoHolder"><img src="{{item.logo}}" style="width:85%; max-height:200px;"></div>
                <div class="w3-col s6 m6">
                    <div style="margin:0 auto; width:90%;"><h1>{{item.title}}</h1></div>
                </div>
                <div class="w3-col s3 m3 __tc"><img src="{{item.image}}" style="width:85%; max-height:200px;"></div>
            </div>
            <div class="w3-row">
                <div class="w3-col">
                    <div>{{{item.description}}}</div>
                    <p class="productDescription">Brand: {{item.brand}}</p>
                </div>
            </div>
        </div>


        <div>
            <div id="panel" style="width:45%; float:left; margin:0 10px 10px 0; background:#fff;">
                <div style="font-size:18px; font-weight:bold; margin-top:-3px;">Nutrition Facts</div>
                <div class="smText">Serving Size: 1 oz <!--{{item.servingSize}}--></div>
                <div class="smText bigBottomBorder">Servings Per Container: {{item.perContainer}}</div>
                <div class="bb" style="font-weight: bold; font-size:11px;">Amount Per Serving</div>
                <div class="w3-row lessBigBottomBorder" style="padding-bottom:5px;">
                    <div class="w3-col s4"><b>Calories</b> {{item.nutrition.calories.value}}</div>
                    <div class="w3-col s8 w3-right-align" style="padding-right:5px;"><b>Calories from Fat</b> {{item.nutrition.caloriesFromFat.value}}</div>
                </div>
                <div class="w3-right-align bb" style="font-weight:bold; margin-top:15px;">% Daily Value*</div>
                <div class="w3-row bb">
                    <div class="w3-col s6"><b>Total Fat</b> {{item.nutrition.totalFat.value}}</div>
                    <div class="w3-col s6 w3-right-align">{{item.nutrition.totalFat.percent}}</div>
                </div>
                <div class="w3-row bb">
                    <div class="w3-col s6"><div class="indent">Saturated Fat {{item.nutrition.satFat.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.satFat.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Trans Fat {{item.nutrition.transFat.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.transFat.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Cholesterol</b> {{item.nutrition.cholesterol.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.cholesterol.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Sodium</b> {{item.nutrition.sodium.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.sodium.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Total Carbohydrate</b> {{item.nutrition.carbs.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.carbs.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Dietary Fiber {{item.nutrition.fiber.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.fiber.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Sugars {{item.nutrition.sugar.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.sugar.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Protein</b> {{item.nutrition.protein.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.protein.percent}}</div>
                        </div>
                        
                        <div class="bigBottomBorder"></div>
            </div>
            
                    <div class="sectionHead flex">Ingredients</div>
                    <div class="details flex"><p>{{{item.ingredients}}}</p></div>
                    
                    <div class="sectionHead flex">Case Specifications</div>  
                    <div class="flex">
                        <table class="caseSpecs">
                            <tr>
                                <td class="label">GTIN</td>
                                <td>{{item.case.gtin}}</td>
				                <td class="label">Case L,W,H</td>
                                <td>{{item.case.caseDim}}</td>                                                               
                            </tr>
                            <tr>
                               <td class="label">Unit Size</td>
                                <td>{{item.case.unitSize}}</td>
                                <td class="label">Cube</td>
                                <td>{{item.case.cube}}</td>                                                              
                            </tr>
                            <tr>
                                <td class="label">Shelf Life (Total / At Shipping)</td>
                                <td>{{item.case.shelfLife}}</td>
                                <td class="label">Tie x High</td>
                                <td>{{item.case.tieHigh}}</td>                                                               
                            </tr>
                            <tr>
                                <td class="label">Case Gross Weight</td>
                                <td>{{item.case.caseGrossWeight}}</td>
                                <td class="label">Kosher Status</td>
                                <td>{{item.case.kosher}}</td>                                 
                            </tr>
                            <tr>
                                <td class="label">Case Net Weight</td>
                                <td>{{item.case.caseNetWeight}}</td> 
                                <td></td>
                                <td></td>                                                             
                            </tr>
                    </table>   
                </div>

            <div class="noBreak">
                {{#if item.prep}}
                        <div class="sectionHead flex">Preparation and Cooking</div>
                        <div class="details flex">{{{item.prep}}}</div>                    
                {{/if}}    
            </div>
            <div class="noBreak">
                {{#if item.storage}}
                <div class="sectionHead flex">Packaging and Storage</div>  
                <div class="details flex">{{{item.storage}}}</div>
                {{/if}}  
            </div>
            <div class="noBreak">
                <div class="sectionHead flex">Allergens</div>  
                <div class="details">{{{item.allergens}}}</div>
            </div>  
            
            <div style="width:100%; margin-top:25px; font-size:9px; color:#666; text-align: center;">Lactalis Culinary | 2376 South Park Avenue, Buffalo NY 14220 | www.lactalisculinary.com | SKU Updated {{item.updated}}, Printed {{today}}</div>
            
        </div>
    </body>
</html>
    