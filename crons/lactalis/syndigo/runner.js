const fs = require('fs');
const dayjs = require("dayjs");
const syndigo =  require(process.cwd()+"/libs/syndigo.js");
const mdb = require(process.cwd()+"/libs/mdb.js");
const sharp = require('sharp');
const _ = require('lodash');
const fetch = require("node-fetch");
const HB = require(process.cwd()+"/libs/handlebars.js");
const aws = require(process.cwd()+"/libs/aws.js");
const ITEMS = "items";
const ORGID = mdb.objectId("6425eef367c2b869c563d7be");
const WH = 400;

function safeFilename(key){
    return key.replace(/\s/,"_").replace(/[^a-z0-9.-_]/ig,"").replace(/\//g, "_").toLowerCase();
}

function brandLogo(v){
    let output="https://bmghub.nyc3.digitaloceanspaces.com/6425eef367c2b869c563d7be/images/thumbs/";            
            
    if(v.toUpperCase().indexOf("DIAMOND")>-1){
        output+="officialblackdiamondlogo.jpg"
    }else if(v.toUpperCase().indexOf("BARREL")>-1){
        output+="crackerbarrel_logo.png"
    }else if(v.toUpperCase().indexOf("BERNARDO")>-1){
        output+="officialdonbernardologo.png"
    }else if(v.toUpperCase().indexOf("GALBANI")>-1){
        output+="officialgalbaniculinarylogo.png"
    }else if(v.toUpperCase().indexOf("HOFFMAN")>-1){
        output+="officialhoffmanslogo.png"
    }else if(v.toUpperCase().indexOf("ISTARA")>-1){
        output+="officialistaralogo.png"
    }else if(v.toUpperCase().indexOf("KRAFT")>-1){
        output+="kraftnaturalcheese.png"
    }else if(v.toUpperCase().indexOf("FRESCA")>-1){
        output+="officialmozzarellafrescalogo.jpg"
    }else if(v.toUpperCase().indexOf("PARMALAT")>-1){
        output+="parmalatlogo.png"
    }else if(v.toUpperCase().indexOf("PRESIDENT")>-1){
        output+="presidentlogoblacktag.jpg"
    }else if(v.toUpperCase().indexOf("SIGGI")>-1){
        output+="officialsiggislogo.jpg"
    }else if(v.toUpperCase().indexOf("SOCIETE")>-1){
        output+="officialsocietelogo.png"
    }else if(v.toUpperCase().indexOf("STONYFIELD")>-1){
        output+="officialstonyfieldlogo.jpg"
    }else if(v.toUpperCase().indexOf("VALBRESO")>-1){
        output+="officialvalbresologo.png"
    }else{
        output+="lusa-logo-400-white.png"  //"lactalisculinarylogo300dpi1.png"
    }

    return output;
               
}

function formatSyndigoDataForPDF(item){              
    let output={
        "title":item.Values.AdditionalIdentifier +" - "+ item.Values.AdditionalDescription,
        "image":((item.AssetValues && item.AssetValues.MainProductImage) ? this.fullImageSrc(item.AssetValues.MainProductImage.SourceUrl) : ""),
        "logo":((item.Values.MFGBrandName) ? item.Values.MFGBrandName : ""),
        "description":"", //set below.
        "brand":item.Values.MFGBrandName,
        "ingredients":item.Values.ingredientStatement,
        "servingSize_0":((item.NutritionalInformationModule && item.NutritionalInformationModule.Panels && item.NutritionalInformationModule.Panels.ServingSizeDescription) ? item.NutritionalInformationModule.Panels.ServingSizeDescription : "N/A"),
        "servingSize":((item.Values.GTIN==="10810140180015") ? "23 g" : "1 oz"),
        "perContainer": item.Values.numberOfServingsPerPackage,  //((item.NutritionalInformationModule && item.NutritionalInformationModule.Panels && item.NutritionalInformationModule.Panels.NumberOfServings) ? item.NutritionalInformationModule.Panels.NumberOfServings : "N/A"),
        "case":{
            "gtin":item.Values.GTIN,
            "upc":item.Values.UPC,
            "unitSize":item.Values.quantityOfInnerPack+" / "+ item.Values.individualUnitMin + " " + syndigo().uom(item.Values.individualUnitMinUOM),
            "shelfLife":item.Values.minimumTradeItemLifespanFromTimeOfProduction + " Days / " + item.Values.minimumTradeItemLifespanFromTimeOfArrival + " Days",
            "caseGrossWeight":item.Values.PackageWeight + " " + syndigo().uom(item.Values.PackageWeightUOM),
            "caseNetWeight":item.Values.netWeight+ " " + syndigo().uom(item.Values.netWeightUOM),
            "caseDim":item.Values.PackageDepth+" IN, "+item.Values.PackageWidth + " IN, "+item.Values.PackageHeight + " IN",
            "cube":item.Values.inBoxCubeDimension+" CF",
            "tieHigh":item.Values.quantityOfTradeItemsPerPalletLayerNONPalletGTIN+" x " + item.Values.quantityOfLayersPerPalletNONPalletGTIN,
            "kosher":"NA"                    
        },
        "prep":"", //set below
            "servings":"", //set below
            "storage":"", //set below
            "allergens":"",
            "nutrition":{},
            "updated":((item.AuditInfo && item.AuditInfo.LastModifiedDate) ? dayjs(item.AuditInfo.LastModifiedDate).format("MM.DD.YYYY") : dayjs().subtract(1,"day").format("MM.DD.YYYY") )
        };

        if(item.MultiValues){
            if(item.MultiValues.tradeItemMarketingMessage && item.MultiValues.tradeItemMarketingMessage.length>0){
                item.MultiValues.tradeItemMarketingMessage.forEach(str=>{
                    output.description += "<p>"+str+"</p>";
                });
            }
            if(item.MultiValues.consumerStorageInstructions && item.MultiValues.consumerStorageInstructions.length>0){
                item.MultiValues.consumerStorageInstructions.forEach(str=>{
                    output.storage += "<p>"+str+"</p>";
                });
            }        
        }
        
        if(item.ContainerValues){
            if(item.ContainerValues["DietTypeInformation"] && item.ContainerValues["DietTypeInformation"].dietTypeCode && item.ContainerValues["DietTypeInformation"].dietTypeCode.toUpperCase()==="KOSHER"){                  
                output.case.kosher = "KOSHER";
            }

            if(item.ContainerValues["PreparationServingInformation"]){
                output.prep = "<p>"+item.ContainerValues["PreparationServingInformation"].preparationInstructions+"</p>";

                if(typeof item.ContainerValues["PreparationServingInformation"].servingSuggestion!=="undefined" && item.ContainerValues["PreparationServingInformation"].servingSuggestion.length>0){
                    item.ContainerValues["PreparationServingInformation"].servingSuggestion.forEach(str=>{
                        output.servings += "<p>"+str+"</p>";
                    });
                }                    
            }

            if(item.ContainerValues["AllergenRelatedInformation"]){
                let contains=[], freeFrom=[];
                    
                if(typeof item.ContainerValues["AllergenRelatedInformation"].Allergens!=="undefined" && item.ContainerValues["AllergenRelatedInformation"].Allergens.length>0){
                    item.ContainerValues["AllergenRelatedInformation"].Allergens.forEach(allergen=>{
                        if(allergen.allergenLevelOfContainment==="CONTAINS"){
                            contains.push(allergen.allergenTypeCode);
                        }else{
                            freeFrom.push(allergen.allergenTypeCode);
                        }
                      });
                }
                                        
                if(contains.length>0){
                    output.allergens+="<p>CONTAINS:<br>"+contains.map(function(v){ return syndigo().allergenCode(v)}).join(", ")+"</p>";
                }

                if(freeFrom.length>0){
                    output.allergens+="<p>FREE FROM:<br>"+freeFrom.map(function(v){ return syndigo().allergenCode(v)}).join(", ");+"</p>";
                }
                    
            }
        }

        if(item.NutritionalInformationModule && item.NutritionalInformationModule.Panels && typeof item.NutritionalInformationModule.Panels.NutrientDetails==="object" && item.NutritionalInformationModule.Panels.NutrientDetails.length>0){
            item.NutritionalInformationModule.Panels.NutrientDetails.forEach(function(N){                    
                    let fieldName = null;
                    switch(N.NutrientTypeCode.toUpperCase()){
                        case "ENER-":
                        case "ENERC":
                            fieldName="calories";
                        break;
                        case "ENERPF":
                            fieldName="caloriesFromFat";
                        break;
                        case "FAT":
                        case "FATNLEA":
                            fieldName="totalFat";
                        break;
                        case "FASAT":
                            fieldName="satFat";
                        break;
                        case "FATRN":
                            fieldName="transFat";
                        break;
                        case "CHOL-":
                        case "CHOLC":
                            fieldName="cholesterol";
                        break;
                        case "NA":
                        case "NACL":
                            fieldName="sodium";
                        break;
                        case "CHO-":
                        case "CHOCDF":
                            fieldName="carbs";
                        break;
                        case "FIBTSW":
                        case "FIB-":
                        case "FIFIBTG":
                            fieldName="fiber";
                        break;
                        case "SUGAR-":
                        case "SUGAR":
                            fieldName="sugar";
                        break;
                        case "SUGAD":
                            fieldName="addedSugar";
                        break;
                        case "PRO-":
                        case "PROA":
                            fieldName="protein";
                        break;
                        case "VITA-":
                        case "VITAA":
                        case "VITA":
                            fieldName="vitA";
                        break;
                        case "VITC-":
                        case "VITC":
                            fieldName="vitC";
                        break;
                        case "VITD-":                        
                            fieldName="vitD";
                        break;
                        case "CA":
                            fieldName="calcium";
                        break;
                        case "FE":
                        case "HAEM":
                            fieldName="iron";
                        break;
                        case "K":
                            fieldName="potassium";
                        break;
                        case "ASH":
                            fieldName="ash";
                        break;
                        case "BIOT":
                            fieldName="biotin";
                        break;
                        case "CAFFN":
                            fieldName="caffeine";
                        break;
                    }
                   
                    if(fieldName){           
                        let percentValue="0";
                        
                        if(N.DailyValueIntakePercent && 
                            ((typeof N.DailyValueIntakePercent==="number" && N.DailyValueIntakePercent>0)
                            || (typeof N.DailyValueIntakePercent==="string" && N.DailyValueIntakePercent!=="0"))
                        ){
                            percentValue=N.DailyValueIntakePercent+"%";
                        }
                                  
                        output.nutrition[fieldName] = {
                            "percent":percentValue,
                            "value":((N.QuantityContained) ? N.QuantityContained.Value+" "+syndigo().uom(N.QuantityContained.Code) : 0)
                        }
                    }
                    
            });
        }
                    
    return output;
    
}

module.exports=async (PARAMS)=>{
        let hubGTINs = [];
        let syndigoGTINs = [];
        let hubItemsToRemove=[],
            hubItemsUpdated=[],
            hubItemsCreated=[];  

        // ========= Get a list of Product items in HUB2.0 ================================================
        let hubItems = await mdb.client().collection(ITEMS).find({"org_id":ORGID, "tags":"lQ3aM6","archived":{"$exists":false}}).toArray(); 
        for(let hubItem of hubItems){
            if(hubItem.fields.GTIN && hubItem.fields.GTIN.trim().length>0){
                hubGTINs.push(hubItem.fields.GTIN.trim());
            }            
        }
        
        // ========= Get a list of Syndigo items that match HUB GTINS ======================================
        for(let syndigoItem of syndigo().parse(await syndigo().getByGTIN({"gtins":hubGTINs}))){
            syndigoGTINs.push(syndigoItem.Values.GTIN);
        }

        // ========= Compare the two lists and find HubItems that do not match ======================================
        for(let hubItem of hubItems){
            if(syndigoGTINs.indexOf(hubItem.fields.GTIN.trim())<0){                              

               /*0000000000000000000000000000000000
                Commenting out the auto-archive.  Let Laura manually do that for now until Lactalis data is better.

                // Archive Item from HUB
                await mdb.client().collection(ITEMS).updateOne({
                    "org_id":ORGID,
                    "_id":hubItem._id
                },{
                    "$set":{
                        "archived":new Date()
                    }
                });
                0000000000000000000000000000000000*/
               
                hubItemsToRemove.push({"name":hubItem.name, "id":hubItem._id.toString(), "gtin":hubItem.fields.GTIN})
            }
        }



        // ====== Find recent item changes in Syndigo ====================================================
        
        let syndigoRecentRAW=null;
        let syndigoItems=[];
        try{
            if(PARAMS && PARAMS.gtins && PARAMS.gtins.length>0){
                console.log("Processing by GTIN",PARAMS.gtins);
                let GTINS = PARAMS.gtins.trim().split(",");
                if(GTINS.length>0){                    
                    syndigoRecentRAW = await syndigo().getByGTIN({"gtins":GTINS});
                }                   
            }else{
                console.log("ALL RECENT")
                syndigoRecentRAW = await syndigo().getRecentChanges(((PARAMS && PARAMS.df) ? PARAMS.df : null), "lactalis"); //"2023-07-01T00:00:00.000Z"            
            }                   
           // syndigoRecentRAW = await syndigo().getAll();
            syndigoItems = syndigo().parse(syndigoRecentRAW);
        }catch(e){
            console.log("Syndigo Recent Error",e);
        }


        
        if(syndigoItems){                        
            console.log("Total:", syndigoItems.length);
            let sii=0;
            for(let syndigoItem of syndigoItems){
                sii++;
                try{                
                
                    let hubItem = _.find(hubItems,function(o){
                        return ((o && o.fields && o.fields.GTIN && o.fields.GTIN===syndigoItem.Values.GTIN) ? true : false);
                    });                       
                    
                    let COA="";
                    if(syndigoItem.ContainerValues && syndigoItem.ContainerValues.PlaceofProductActivity && syndigoItem.ContainerValues.PlaceofProductActivity.CountryofOrigin && syndigoItem.ContainerValues.PlaceofProductActivity.CountryofOrigin.length>0 && syndigoItem.ContainerValues.PlaceofProductActivity.CountryofOrigin[0].CountryofActivity){
                        
                        let coa = syndigo().country({"code":Number(syndigoItem.ContainerValues.PlaceofProductActivity.CountryofOrigin[0].CountryofActivity)});
                        if(coa && typeof coa === "object"){                        
                            COA = coa.abbr;
                        }
                    }

                    let dataToSave={
                        "fields.FULLNAME":syndigoItem.Values.FunctionalName,
                        "fields.SKU":syndigoItem.Values.AdditionalIdentifier,
                        "fields.ITEMUPC":syndigoItem.Values.UPC,
                        "fields.UNITSIZE":syndigoItem.Values.quantityOfInnerPack+" / "+ syndigoItem.Values.individualUnitMin + " " + syndigo().uom(syndigoItem.Values.individualUnitMinUOM),
                        "fields.CASEDIM":syndigoItem.Values.PackageDepth+", "+ syndigoItem.Values.PackageWidth + ", " + syndigoItem.Values.PackageHeight,
                        "fields.CASEWEIGHT":syndigoItem.Values.PackageWeight+" / " + syndigoItem.Values.netWeight,
                        
                        "fields.CUBE":syndigoItem.Values.inBoxCubeDimension+ " " + syndigo().uom(syndigoItem.Values.inBoxCubeDimensionUOM),
                        "fields.WEIGHTMETHOD":"",
                        "fields.PRICETYPE":"",
                        "fields.COA":COA,
                        "fields.DESCRIPTION":((syndigoItem.MultiValues && syndigoItem.MultiValues.tradeItemMarketingMessage) ? syndigoItem.MultiValues.tradeItemMarketingMessage.join(" ") : ""),
                        
                        "fields.SHELFLIFE":syndigoItem.Values.minimumTradeItemLifespanFromTimeOfProduction+" / " + syndigoItem.Values.minimumTradeItemLifespanFromTimeOfArrival,
                        "fields.TIExHEIGHT":syndigoItem.Values.quantityOfTradeItemsPerPalletLayerNONPalletGTIN+" x " + syndigoItem.Values.quantityOfLayersPerPalletNONPalletGTIN,
                        "updated":new Date(),
                        "meta.syndigo":new Date()
                    };

                    //==== PDF =====================================================================================
                  
                    let pdfFileName=safeFilename(syndigoItem.Values.ShortDescription + ((syndigoItem.Values.GTIN) ? "_"+syndigoItem.Values.GTIN : ""))+".pdf";
                    dataToSave.files={};
                    dataToSave.files.specsheet={
                        "name" : pdfFileName,
                        "size" : -1
                    };
                    
                   // fs.writeFileSync(process.cwd()+"/templates/"+safeFilename(syndigoItem.Values.ShortDescription + ((syndigoItem.Values.GTIN) ? "_"+syndigoItem.Values.GTIN : ""))+".html",HB(fs.readFileSync(process.cwd() +"/crons/lactalis/syndigo/specsheet.html").toString(),{"item":formatSyndigoDataForPDF(syndigoItem), "today":dayjs().format("MM.DD.YYYY")}))
                    
                    /* VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV
                    await fetch(process.env["PDF_SERVER_URL"]+'/generate', {
                        method: 'post',
                        body: JSON.stringify({
                            "key":";w1wY?Tf9[!Xw0a*z%u9pEEtVp,2tA",
                            "content":HB(fs.readFileSync(process.cwd() +"/crons/lactalis/syndigo/specsheet.html").toString(),{"item":formatSyndigoDataForPDF(syndigoItem), "today":dayjs().format("MM.DD.YYYY")}),
                            "output":"upload",
                            "filename":ORGID.toString()+"/files/"+pdfFileName
                        }),
                        headers: {'Content-Type': 'application/json'}
                    });^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ */
                    
                    

                    /*
                    await aws.putS3Object({
                        "key":ORGID.toString()+"/files_dev/"+pdfFileName,
                        "body":await PDF.generate(
                            HB(fs.readFileSync(process.cwd() +"/crons/lactalis/syndigo/specsheet.html").toString(),{"item":formatSyndigoDataForPDF(syndigoItem), "today":dayjs().format("MM.DD.YYYY")})                        
                        ),
                        "encoding":"base64",
                        "contentType":"application/pdf",
                        "acl":"public-read"
                    });
                    */  

                    async function processPDF(pdfFileName, syndigoItem, productImage){
                        console.log("GENERATE PDF");
                        
                        let formattedItem = formatSyndigoDataForPDF(syndigoItem);
                            formattedItem.logo = brandLogo(formattedItem.logo);
                            if(!formattedItem.image){ formattedItem.image = formattedItem.logo; }

                        let pdfResponse = await fetch(process.env["PDF_SERVER_URL"]+'/generate', {
                            method: 'post',
                            body: JSON.stringify({
                                "key":";w1wY?Tf9[!Xw0a*z%u9pEEtVp,2tA",
                                "content":HB(fs.readFileSync(process.cwd() +"/crons/lactalis/syndigo/specsheet.html").toString(),{"item":Object.assign({},productImage, formattedItem), "today":dayjs().format("MM.DD.YYYY")}),
                                "output":"upload",
                                "filename":ORGID.toString()+"/files/"+pdfFileName
                            }),
                            headers: {'Content-Type': 'application/json'}
                        });
                        console.log(pdfResponse.status);

                    }

                    
                    //==== IMAGES ===================================================================================
                    if(syndigoItem.AssetValues && syndigoItem.AssetValues.MainProductImage){
                        dataToSave.image = safeFilename(syndigoItem.AssetValues.MainProductImage.Name);
    
                        /*0000000000000000000000000000000000*/
                        let imageBase64 = await syndigo().imageBase64(syndigoItem.AssetValues.MainProductImage.Url);
    
                        // ===== Create and upload thumb =====================================================
                        if(syndigoItem.AssetValues.MainProductImage.Format==="png"){                                                      
                            sharp(Buffer.from(imageBase64.content, 'base64'))
                                .flatten({ background: { r: 255, g: 255, b: 255 } })
                                .resize(WH,WH,{fit:"contain",background:{ r: 255, g: 255, b: 255 }})                //
                                .png({compressionLevel:7,quality:80})
                                .toBuffer(async (err, data, info) => {  
                                if(err){
                                    console.log(err);                                    
                                }else{
                                    await aws.putS3Object({
                                        "key":ORGID.toString()+"/images/thumbs/"+dataToSave.image, //syndigoItem.AssetValues.MainProductImage.Name,
                                        "body":data,
                                        "encoding":"base64",
                                        "contentType":((syndigoItem.AssetValues && syndigoItem.AssetValues.MainProductImage && syndigoItem.AssetValues.MainProductImage.Format==="png") ? "image/png" : "image/jpg"),
                                        "acl":"public-read"
                                    });    
                                    
                                    processPDF(pdfFileName, syndigoItem, {"local_product_image":"https://bmghub.nyc3.digitaloceanspaces.com/"+ORGID.toString()+"/images/thumbs/"+dataToSave.image})
                                }
                            });
                        }else{
                            sharp(Buffer.from(imageBase64.content, 'base64'))                                
                                .resize(WH,WH,{fit:"contain",background:{ r: 255, g: 255, b: 255 }})                //                                
                                .toBuffer(async (err, data, info) => {  
                                if(err){
                                    console.log(err);                                    
                                }else{
                                    await aws.putS3Object({
                                        "key":ORGID.toString()+"/images/thumbs/"+dataToSave.image, //syndigoItem.AssetValues.MainProductImage.Name,
                                        "body":data,
                                        "encoding":"base64",
                                        "contentType":((syndigoItem.AssetValues && syndigoItem.AssetValues.MainProductImage && syndigoItem.AssetValues.MainProductImage.Format==="png") ? "image/png" : "image/jpg"),
                                        "acl":"public-read"
                                    });

                                    processPDF(pdfFileName, syndigoItem, {"local_product_image":"https://bmghub.nyc3.digitaloceanspaces.com/"+ORGID.toString()+"/images/thumbs/"+dataToSave.image})
                                }
                            });
                        }
    
                        // ===== UPLOAD THE IMAGE TO CLOUD ==================================================
                        await aws.putS3Object({
                            "key":ORGID.toString()+"/images/"+dataToSave.image, //syndigoItem.AssetValues.MainProductImage.Name,
                            "body":Buffer.from(imageBase64.content, 'base64'),
                            "encoding":"base64",
                            "contentType":((syndigoItem.AssetValues && syndigoItem.AssetValues.MainProductImage && syndigoItem.AssetValues.MainProductImage.Format==="png") ? "image/png" : "image/jpg"),
                            "acl":"public-read"
                        });

                        console.log("Uploaded: ",dataToSave.image);
                        /*0000000000000000000000000000000000*/
                        
                    }






                        
                    if(hubItem){

                        let updated = await mdb.client().collection(ITEMS).updateOne({
                            "org_id":ORGID,
                            "_id":hubItem._id
                        },{
                            "$set":dataToSave
                        });
    
                        if(updated && updated.modifiedCount){
                            hubItemsUpdated.push({"id":hubItem._id,"name":hubItem.name});
                        }                                

                    }else{ // Create a new item in the db                 
    
                        let created = await mdb.client().collection(ITEMS).insertOne({
                            "org_id":ORGID,
                            "name": ((syndigoItem.Values.AdditionalDescription && syndigoItem.Values.AdditionalDescription.length>0) ? syndigoItem.Values.AdditionalDescription : syndigoItem.Values.FunctionalName),
                            "image":((dataToSave.image) ? dataToSave.image : ""),
                            "actions":{
                                "link":false,
                                "download":false,
                                "email":false,
                                "order":false
                            },
                            "fields":{
                                "FULLNAME":syndigoItem.Values.FunctionalName,
                                "SKU":syndigoItem.Values.AdditionalIdentifier,
                                "ITEMUPC":syndigoItem.Values.UPC,
                                "GTIN":syndigoItem.Values.GTIN,
                                "UNITSIZE":syndigoItem.Values.quantityOfInnerPack+" / "+ syndigoItem.Values.individualUnitMin + " " + syndigo().uom(syndigoItem.Values.individualUnitMinUOM),
                                "CASEDIM":syndigoItem.Values.PackageDepth+", "+ syndigoItem.Values.PackageWidth + ", " + syndigoItem.Values.PackageHeight,
                                "CASEWEIGHT":syndigoItem.Values.PackageWeight+" / " + syndigoItem.Values.netWeight,
                                
                                "CUBE":syndigoItem.Values.inBoxCubeDimension+ " " + syndigo().uom(syndigoItem.Values.inBoxCubeDimensionUOM),
                                "WEIGHTMETHOD":"",
                                "PRICETYPE":"",
                                "COA":COA,
                                "DESCRIPTION":syndigoItem.MultiValues.tradeItemMarketingMessage.join(" "),

                                "SHELFLIFE":syndigoItem.Values.minimumTradeItemLifespanFromTimeOfProduction+" / " + syndigoItem.Values.minimumTradeItemLifespanFromTimeOfArrival,
                                "TIExHEIGHT":syndigoItem.Values.quantityOfTradeItemsPerPalletLayerNONPalletGTIN+" x " + syndigoItem.Values.quantityOfLayersPerPalletNONPalletGTIN,
    
                            },
                            "tags":[],
                            "inventory":{
                              "in_stock" : Number(0),        
                              "on_order" : Number(0),
                              "restock_level" : Number(0),
                              "oos_action" : ""
                            },
                            "status" : Number(0),
                            "files":{},
                            "meta":{
                                "syndigo":new Date()
                            },
                            "created":new Date(),
                            "updated":new Date()
                        });
    
                        if(created && created.insertedId){
                            hubItemsCreated.push({"id":created.insertedId, "name":syndigoItem.Values.FunctionalName});
                        }
                    }
                    
                    /*await mdb.client().collection("syndigo").insertOne({
                        "scope":"recent",
                        "created":new Date(),                        
                        "parsed":syndigoItem
                    });*/
                    
                }catch(e){
                    console.log("TRY Error: ",syndigoItem.Values.GTIN)
                    console.log(e);
                }

                console.log("# "+sii);
            }
        }
            


            /////////////////////////////////////////////

            //Send mail with IDs of affected items
            let body=`{{#if updates}}
            <h2>Items Updated</h2>
            {{#each updates}}
            <a href="https://lactalisculinarystp.bmghub.us/admin/item/{{this.id}}">{{this.name}}</a><br>
            {{/each}}
            {{/if}}
            
            {{#if creates}}
            <h2>Items Created</h2>
            {{#each creates}}
            <a href="https://lactalisculinarystp.bmghub.us/admin/item/{{this.id}}">{{this.name}}</a><br>
            {{/each}}
            {{/if}}
            
            {{#if removes}}
            <h2>Items To Remove</h2>
            {{#each removes}}
            <a href="https://lactalisculinarystp.bmghub.us/admin/item/{{this.id}}">{{this.name}}</a>&nbsp;&nbsp;({{this.gtin}})<br>
            {{/each}}
            {{/if}}`;


            aws.email({
                "to":["<EMAIL>","<EMAIL>"], // ,"<EMAIL>"
                "from": "BMGHub - Syndigo <<EMAIL>>",
                "subject": "Syndigo Importer - Updated:"+hubItemsUpdated.length+" Created:"+hubItemsCreated.length+" Removed:"+hubItemsToRemove.length,
                "body":HB(body,{"updates":hubItemsUpdated, "creates":hubItemsCreated, "removes":hubItemsToRemove})
            }).then(r=>{   
                console.log("Lactalis <> Syndigo Email Sent")             
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });
            
            
            /////////////////////////////////////////////

        

        
    
};