const dayjs = require("dayjs");
const mdb = require(process.cwd()+"/libs/mdb.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const aws = require(process.cwd()+"/libs/aws.js");

let _get =async function(matchCriteria){    
    /*
     { "$lookup": { "from": "formflows", "localField": "formflows", "foreignField": "_id", "as": "formflows" } },
     { "$lookup": { "from": "formflow_submissions", "localField": "formflow_submissions", "foreignField": "_id", "as": "formflow_submissions" } },
    */
    let agg=[
        { "$match":matchCriteria},                
        { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
        { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
        { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } },
      ];
      
    let submissions = await mdb.client().collection("rebate_submissions").aggregate(agg).toArray();
    
    for(let x=0; x<submissions.length; x++){        
        submissions[x].rebate = submissions[x].rebate[0];
        submissions[x].operator = submissions[x].operator[0];
        submissions[x].broker = submissions[x].broker[0];
        delete submissions[x].broker.cart;
        delete submissions[x].broker.favorites;
        delete submissions[x].broker.outbox;   
        delete submissions[x].logs;     
        delete submissions[x].notes;     
    }

    return submissions;
}

module.exports=async (PARAMS)=>{

    const org = await mdb.client().collection("orgs").findOne({"_id":mdb.objectId("6425eef367c2b869c563d7be")},{"projection":{"server":1}});

    //Query all ACTIVE rebate submissions
    let submissions = await _get({"org_id":mdb.objectId("6425eef367c2b869c563d7be"),"closed":{"$ne":true}});    
    
    for(let submission of submissions){                   
        if( // ---- NUDGE ------
            (
                !submission.checklist.operatorRegistrationComplete 
                || !submission.checklist.proofOfPurchaseComplete
                || !submission.checklist.bankingInformationComplete 
                || !submission.checklist.w9InformationComplete  
                || !submission.checklist.brokerReviewComplete 
            )
            && 
            (dayjs().diff(dayjs(submission.created),"day") > 30)
            &&
            (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 30)
        ){
            
            let to=[];
            to.push(submission.broker.email);
            //to = to.concat(submission.collaborators);
            nudge("nudgeDataCollection", to, submission);

        }else if ( // ---- NUDGE  ------
            submission.status==="Pending Sales/Broker Review" 
            && (!submission.status_updated || dayjs(submission.status_updated).diff(dayjs(),"day") > 5)
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 5)
        ){
            let to=[submission.broker.email];                    
            nudge("nudgeBrokerReview", to, submission);


        }else if ( // ---- NUDGE  ------
            submission.status==="Pending Finance Review" 
            && (!submission.status_updated || dayjs(submission.status_updated).diff(dayjs(),"day") > 14)
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 14)
        ){
            let to=org.server.config.contacts.rebateTeams.finance;                    
            nudge("nudgeFinance", to, submission);


        }else if ( // ---- NUDGE  ------
            submission.status==="Pending Go Simple Contact Creation" 
            && (!submission.status_updated || dayjs(submission.status_updated).diff(dayjs(),"day") > 5)
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 5)
        ){
            let to=[];
            to.push(submission.broker.email);                
            nudge("nudgePendingGoSimple", to, submission);

        }else if ( // ---- NUDGE  ------
            submission.status==="Pending Proof of Purchase" 
            && submission.checklist.operatorRegistrationComplete
            && !submission.dataCollectionComplete 
            && dayjs(submission.created).diff(dayjs(),"day") > 90
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 30)
        ){
            let to=[];
            to.push(submission.broker.email);                    
            nudge("nudgeProofOfPurchase", to, submission);
        
        
        }else if (
            submission.status==="Pending Marketing Review" 
            && (!submission.status_updated || dayjs(submission.status_updated).diff(dayjs(),"day") > 5)
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 5)
        ){
            let to=[];
            to.push(org.server.config.contacts.rebateTeams.marketingGalbani);                    
            nudge("nudgePendingMarketing", to, submission);
        
        
        }else if (
            submission.status==="Payable" 
            && (!submission.status_updated || dayjs(submission.status_updated).diff(dayjs(),"day") > 8)
            && (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 8)
        ){
            let to=[];
            to.push(org.server.config.contacts.rebateTeams.finance);                    
            nudge("nudgePayable", to, submission);
        }


    }


/*********************************************************************
 * NUDGE - SEND EMAILS
 ********************************************************************/

    async function nudge(templateId, to, submission){
        try{

            let dataToSave={
                "$set":{"nudged":new Date()},
                "$push":{"logs":{
                    "d":new Date(),
                    "e":"SYSTEM",
                    "m":`Nudge Sent: <code>${templateId}</code>`
                }}
            };
    
           await mdb.client().collection("rebate_submissions").updateOne({"_id":submission._id},  dataToSave);

        }catch(e){
            console.log("ERROR SAVING NUDGE");
            console.log(e);
        }
    
        let template = org.server.templates.rebate[templateId];

        let templateData = {
            "REBATENAME":submission.rebate.name,
            "OPERATORNAME":((submission.operator && submission.operator.name) ? submission.operator.name : "(Operator name not set)"),
            "BROKERNAME":submission.broker.fields.FNAME + " " + submission.broker.fields.LNAME,
            "BROKEREMAIL":submission.broker.email,
            "STATUS":submission.status,
            "SUBMISSIONID":submission._id.toString()
        };

        if(template){            
            aws.email({
                "to":((process.env.ISDEV) ? "<EMAIL>"  : to),                         
                "from": template.from+" <<EMAIL>>",
                "subject": HB(template.subject,templateData),
                "body":HB(template.body,templateData)
            }).then(r=>{   
                console.log("Lactalis Rebate Nudge Sent")             
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });            
        }else{
            console.log("Error - Missing Template", templateId);
        }        
    }


}