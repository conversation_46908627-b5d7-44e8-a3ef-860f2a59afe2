<html>
    <head>
        <title></title>
        <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
        <style>
            html{
                padding:0px;                
            }
            body{
                padding:20px 20px 20px 5px;
                border:1px solid #000;
            }
            p{
                margin-top:0px;
                font-size:0.75rem;
            }
            h1{
                font-size: 1.2rem;
                font-weight:bold;
                color:#666;
            }
            .productDescription{
                font-size:0.70rem;
                color:#666;
            }
            #logoHolder{
                text-align: center;                
            }
            .sectionHead{
                background:#a3a3a3;
                font-weight: bold;
                color:#fff;
                padding:3px 5px;
                font-size:13px;
            }
            table.caseSpecs{
                width:100%;
                font-size:11px;
                margin-bottom: 15px;
            }
            table.caseSpecs td{
                border:1px solid rgb(211, 210, 210);
                width:25%;
            }
            table.caseSpecs td.label{
                border:1px solid #333;
                font-weight: bold;
            }            
            .smText{
                font-size:9px;
            }
            .bigBottomBorder{
                border-bottom: 10px solid #000;
            }
            .lessBigBottomBorder{
                border-bottom: 6px solid #000;
            }
            .details{
                font-size:0.75rem;
                margin:0 0 10px 5px;
            }
            .bb{
                border-bottom:1px solid #000;
            }
            .indent{
                margin-left:10px;
            }
            .bullet{
                margin-left:10px;
                margin-right:20px;
            }
            @media print {
                .noBreak {                
                    break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div style="margin-left:5px;">
            <div class="w3-row">
                <div class="w3-col s3 m3 logoHolder"><img src="{{item.logo}}" style="width:85%; max-height:200px;"></div>
                <div class="w3-col s6 m6">
                    <div style="margin:0 auto; width:100%;">
                        <h1 style="margin-top:0px;">{{item.title}}</h1>
                        <div style="font-size:0.5rem;">{{{item.description}}}</div>
                        <p class="productDescription">Brand: {{item.brand}}</p>
                    </div>
                </div>
                <div class="w3-col s3 m3 w3-right-align"><img src="{{item.local_product_image}}" style="width:85%; max-height:200px;"></div>
            </div>            
        </div>

        <div style="margin-left:5px;">
            <div class="w3-row">                
                <div class="w3-col" style="width:37%;">
                    <div style="border:1px solid #000; padding:3px; margin-right:7px; font-size:0.75rem;">
                        <div style="font-size:18px; font-weight:bold; margin-top:-3px;">Nutrition Facts</div>

                        <div class="w3-row bigBottomBorder">
                            <div class="w3-col">{{item.perContainer}} servings per container</div>
                            <div class="w3-col s6"><b>Serving Size:</b></div>
                            <div class="w3-col s6 w3-right-align">{{item.servingSize}}</div>
                        </div>

                        <div class="bb" style="font-weight: bold; font-size:11px;">Amount Per Serving</div>
                        <div class="w3-row lessBigBottomBorder" style="padding-bottom:5px;">
                            <div class="w3-col s4"><b>Calories</b> {{item.nutrition.calories.value}}</div>
                            <div class="w3-col s8 w3-right-align" style="padding-right:5px;"><b>Calories from Fat</b> {{item.nutrition.caloriesFromFat.value}}</div>
                        </div>
                        <div class="w3-right-align bb" style="font-weight:bold; margin-top:15px;">% Daily Value*</div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Total Fat</b> {{item.nutrition.totalFat.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.totalFat.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Saturated Fat {{item.nutrition.satFat.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.satFat.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Trans Fat {{item.nutrition.transFat.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.transFat.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Cholesterol</b> {{item.nutrition.cholesterol.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.cholesterol.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Sodium</b> {{item.nutrition.sodium.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.sodium.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Total Carbohydrate</b> {{item.nutrition.carbs.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.carbs.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Dietary Fiber {{item.nutrition.fiber.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.fiber.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Total Sugars {{item.nutrition.sugar.value}}</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.sugar.percent}}</div>
                        </div>
                         <div class="w3-row bb">
                            <div class="w3-col s6"><div class="indent">Includes {{item.nutrition.addedSugar.value}} Added Sugars</div></div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.addedSugar.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6"><b>Protein</b> {{item.nutrition.protein.value}}</div>
                            <div class="w3-col s6 w3-right-align">{{item.nutrition.protein.percent}}</div>
                        </div>
                        
                        <div class="bigBottomBorder"></div>
                        
                        <div class="bb">&nbsp;</div>

                        <div class="w3-row bb">
                            <div class="w3-col s6">Vitamin D {{item.nutrition.vitD.value}} {{item.nutrition.vitD.percent}}</div>
                            <div class="w3-col s6 w3-right-align">Calcium {{item.nutrition.calcium.value}} {{item.nutrition.calcium.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6">Iron {{item.nutrition.iron.value}} {{item.nutrition.iron.percent}}</div>
                            <div class="w3-col s6 w3-right-align">Potassium {{item.nutrition.potassium.value}} {{item.nutrition.potassium.percent}}</div>
                        </div>
                        <div class="w3-row bb">
                            <div class="w3-col s6">Vitamin C {{item.nutrition.vitC.value}} {{item.nutrition.vitC.percent}}</div>
                            <div class="w3-col s6 w3-right-align">Vitamin A {{item.nutrition.vitA.value}} {{item.nutrition.vitA.percent}}</div>
                        </div>
                         <div class="w3-row bb">
                            <div class="w3-col">
                                * The % Daily Value (DV) tells you how much a nutrient in a serving of food contributes to a daily diet. 2,000 calories a day is used for general nutrition advice.
                            </div>
                         </div>                                                                   
                    </div>                    
                </div>
                
            
                <div class="w3-col" style="width:62.9%;">
                    <div class="sectionHead">Ingredients</div>
                    <div class="details"><p>{{{item.ingredients}}}</p></div>

                    <div class="sectionHead">Each Specifications</div>  
                        <table class="caseSpecs">
                            <tr>
                                <td class="label">GTIN</td>
                                <td>{{item.each.gtin}}</td>
				                <td class="label">Gross Weight</td>
                                <td>{{item.each.grossWeight}}</td>                                                               
                            </tr>
                            <tr>
                               <td class="label">UPC</td>
                                <td>{{item.each.upc}}</td>
                                <td class="label">Net Weight</td>
                                <td>{{item.each.netWeight}}</td>                                                              
                            </tr>
                            <tr>
                                <td class="label">Pack Size</td>
                                <td>{{item.each.packSize}}</td>
                                <td class="label">L, W, H</td>
                                <td>{{item.each.packageDim}}</td>                                                               
                            </tr>
                            <tr>
                                <td class="label">Shelf Life</td>
                                <td>{{item.each.shelfLife}}</td>
                                <td class="label">Cube</td>
                                <td>{{item.each.cube}}</td>                                 
                            </tr>
                            <tr>
                                <td class="label">Tie x High</td>
                                <td>{{item.case.caseNetWeight}}</td> 
                                <td class="label">&nbsp;</td>
                                <td></td>                                                             
                            </tr>
                        </table>   
                        {{#if item.prep}}
                        <div class="sectionHead">Preparation and Cooking</div>
                        <div class="details">{{{item.prep}}}</div>                    
                        {{/if}}  
                        
                        <div class="noBreak">
                            {{#if item.servings}}
                            <div class="sectionHead">Serving Suggestions</div>  
                            <div class="details">{{{item.servings}}}</div>
                            {{/if}}                        
                    </div>
                    <div class="noBreak">
                            {{#if item.featuresBenefits}}
                            <div class="sectionHead">Product Features and Benefits</div>  
                            <div class="details">{{{item.featuresBenefits}}}</div>
                            {{/if}}                        
                    </div>
                    <div class="noBreak">
                            {{#if item.packagingStorage}}
                            <div class="sectionHead">Packaging and Storage</div>  
                            <div class="details">{{{item.packagingStorage}}}</div>
                            {{/if}}                        
                    </div> 



                </div>
            </div>
            
            <div class="w3-row" style="margin-top:10px;">
                <div class="w3-col">
                                      
                </div>
            </div>
        </div>
        <!--<div style="width:100%; margin-top:25px; font-size:9px; color:#666; text-align: center;">Upstate Niagara Cooperative, Inc. | Foodservice Division, Lancaster NY 14086 | https://uncdairy.com/ | SKU Updated {{item.updated}}, Printed {{today}}</div>-->
    </body>
</html>
<!-- position: absolute; bottom:5px;-->