const dayjs = require("dayjs");
const mdb = require(process.cwd()+"/libs/mdb.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const aws = require(process.cwd()+"/libs/aws.js");


let _get =async function(matchCriteria){    
    
    let agg=[
        { "$match":matchCriteria},                
        { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
        { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
        { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } },
      ];
      
    let submissions = await mdb.client().collection("rebate_submissions").aggregate(agg).toArray();
    
    for(let x=0; x<submissions.length; x++){        
        submissions[x].rebate = submissions[x].rebate[0];
        submissions[x].operator = submissions[x].operator[0];
        submissions[x].broker = submissions[x].broker[0];
        delete submissions[x].broker.cart;
        delete submissions[x].broker.favorites;
        delete submissions[x].broker.outbox;   
        delete submissions[x].logs;     
        delete submissions[x].notes;     
    }

    return submissions;
}

let nudge = async function(templateId, to, submission){
    await mdb.client().collection("rebate_submissions").updateOne({"_id":submission._id},  {"$set":{"nudged":new Date()}});

     let template = org.server.templates.rebate[templateId];

     let templateData = {
         "REBATENAME":submission.rebate.name,
         "OPERATORNAME":((submission.operator && submission.operator.name) ? submission.operator.name : "(Operator name not set)"),
         "BROKERNAME":submission.broker.fields.FNAME + " " + submission.broker.fields.LNAME,
         "BROKEREMAIL":submission.broker.email,
         "STATUS":submission.status,
         "SUBMISSIONID":submission._id.toString()
     };

     if(template){            
         aws.email({
             "to":((process.env.ISDEV) ? "<EMAIL>"  : to),                         
             "from": template.from+" <<EMAIL>>",
             "subject": HB(template.subject,templateData),
             "body":HB(template.body,templateData)
         }).then(r=>{   
             console.log("Sahlen Rebate Nudge Sent")             
         }).catch(e=>{
             console.log("AWS EMAIL error",e)        
         });
        
     }else{
         console.log("Error - Missing Template", templateId);
     }        
 }

module.exports=async function(PARAMS){
    
    const org = await mdb.client().collection("orgs").findOne({"_id":mdb.objectId("64ae1321e802d121cafedc15")},{"projection":{"server":1}});
    let submissions = await _get({"org_id":mdb.objectId("64ae1321e802d121cafedc15"), "closed":{"$ne":true}}); 

    for(let submission of submissions){

        if( // ---- NUDGE ------
            (
                submission.checklist.operatorRegistrationComplete 
                && submission.checklist.operatorIncentiveApproved
                && (
                        !submission.checklist.proofOfPurchaseComplete 
                        || !submission.checklist.proofOfPromotionsComplete  
                    )
            )            
            &&
            (!submission.nudged || dayjs(submission.nudged).diff(dayjs(),"day") >= 30)
        ){

            let to=[];
            to.push(submission.broker.email);
            if(submission.collaborators && submission.collaborators.length>0){
                to = to.concat(submission.collaborators);
            }

            submission.status==="Pending Reporting";
            
            nudge("nudgeUploadPOPs", to, submission);            

        }
    }
}
