const dayjs = require("dayjs");
const mdb = require("../../libs/mdb.js");

module.exports=async function(P){     
    try{
        let orgs = await mdb.client().collection("orgs").find({"exports":{"$exists":true}}).toArray(); 
    
        for(let org of orgs){
            for(let exportKey of Object.keys(org.exports)){
                if(org.exports[exportKey].schedule){
                    let DAYJS = dayjs().utc().local();
                    let now={
                        "M":Number(DAYJS.format("M")),
                        "dom":Number(DAYJS.format("D")),
                        "dow":Number(DAYJS.format("d")),
                        "H":Number(DAYJS.format("H"))
                    }
                    if(
                        (org.exports[exportKey].schedule.cron.M==="" || org.exports[exportKey].schedule.cron.M==="*" || (org.exports[exportKey].schedule.cron.M.indexOf(now.M)>=0))
                        &&
                        (org.exports[exportKey].schedule.cron.dom==="" || org.exports[exportKey].schedule.cron.dom==="*" || (org.exports[exportKey].schedule.cron.dom.indexOf(now.dom)>=0))
                        &&
                        (org.exports[exportKey].schedule.cron.dow==="" || org.exports[exportKey].schedule.cron.dow==="*" || (org.exports[exportKey].schedule.cron.dow.indexOf(now.dow)>=0))
                        &&
                        (org.exports[exportKey].schedule.cron.H==="" || org.exports[exportKey].schedule.cron.H==="*" || (org.exports[exportKey].schedule.cron.H.indexOf(now.H)>=0))                    
                    ){

                        console.log("Running Scheduled Export", exportKey);
                                
                        exportLib.run(org._id, exportKey, Object.assign({                        
                            "email_to":org.exports[exportKey].schedule.email_to,
                        },org.exports[exportKey]));
                        

                    }
                }
            }
        }
    }catch(e){
        console.log("tiggerExport.js Error");
        console.log(e);
    }

};