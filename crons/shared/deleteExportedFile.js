const dayjs = require("dayjs");
const mdb = require("../../libs/mdb.js");
const aws = require("../../libs/aws.js");

module.exports=async function(P){  
    console.log("RUN deleteExportedFile")   
    try{
        //Find exports that are older than 7 days. 
        let exportsToDelete = await mdb.client().collection("exports").find({"created":{"$lt":dayjs().subtract(7,"day").toDate()}}).toArray(); 
    
        for(let exportToDelete of exportsToDelete){

            // --- Remove File from SPACES ------------
            let deleted = await aws.deleteS3Object({
                "key":exportToDelete.spaces_key               
            });            
            // --- Remove record from db ------------
            if(deleted['$metadata'].httpStatusCode<400){                
                await mdb.client().collection("exports").deleteOne({"_id":exportToDelete._id});
                console.log("Deleted Export:",exportToDelete.spaces_key, new Date())
            }                        
        }
    }catch(e){
        console.log("deleteExportedFile.js Error");
        console.log(e);
    }

};