let fetch  = require("node-fetch");
const aws = require(process.cwd()+"/libs/aws.js");


module.exports=async function(P){

    function sendAlert(){
        aws.email({
            "to":["<EMAIL>"], // ,"l<PERSON><PERSON><PERSON><EMAIL>"
            "from": "BMGHub - PDF Checker <<EMAIL>>",
            "subject": "PDF Generator Is Down",
            "body":"PDF Generator Is Down"
        }).then(r=>{   
            console.log("PDFChecker Alert Sent")             
        }).catch(e=>{
            console.log("AWS EMAIL error",e)        
        });
    }

    try{
        const response = await fetch(process.env["PDF_SERVER_URL"]+'/check', 
            {
                method: 'GET', 
                body:null,
                headers:{
                    "Content-Type":"application/json; charset=utf-8"
                }
            });
            let upResponse = await response.text();
        
        if(response.status!==200 || upResponse!=="up"){
            sendAlert();
        }else{
            //console.log("PDF UP");
        }
    }catch{
        sendAlert();
    }
    

}

