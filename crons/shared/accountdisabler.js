const dayjs = require("dayjs");
const mdb = require("../../libs/mdb.js");
const aws = require("../../libs/aws.js");
const _ = require('lodash');
const HB = require("../../libs/handlebars.js");
const common = require("../../libs/common.js");
let ORGID = mdb.objectId("64cbc6e4591c6f6f7599809b");



module.exports=async function(P){
        if(!P || !P.orgId){
            return false;
        }

        ORGID = mdb.objectId(P.orgId);

        let org = await mdb.client().collection("orgs").findOne({"_id":ORGID});        
        if(!org || !org.server.config.account_autodisabler){ return false; }

        for(let disabler of org.server.config.account_autodisabler){

            // ------ ACCOUNTS TO WARN ----------------
            let warnAccountsToFind={
                "org_id":ORGID, 
                "warn":{"$exists":false},
                "$and":[
                    {
                        "last_activity":{
                        "$lte":dayjs().subtract(disabler.warn.days, "day").toDate()
                        }
                    },
                    {
                        "last_activity":{
                        "$gt":dayjs().subtract(disabler.disable.days, "day").toDate()
                        }
                    }
                ]            
            };

            if(disabler.tags && disabler.tags.length>0){ warnAccountsToFind.tags = disabler.tags; }

            let existingWarnAccounts = await mdb.client().collection("accounts").find({
                "org_id":ORGID, 
                "warn":{"$exists":true}
            }).toArray();

            let warnAccounts = await mdb.client().collection("accounts").find(warnAccountsToFind).toArray();
            await mdb.client().collection("accounts").updateMany(warnAccountsToFind,{"$set":{"warn":new Date()}});                  
                       
            if(warnAccounts && warnAccounts.length>0 && typeof org.server.templates[disabler.warn.template]==="object"){
                let template = org.server.templates[disabler.warn.template];
                for(let account of warnAccounts){
                    await aws.email({
                        "to": ((process.env["ISDEV"]) ? "<EMAIL>" : account.email ),
                        "from": `${template.from} <${org.server.config.contacts.from}>`,
                        "reply": org.server.config.contacts.reply_to,
                        "subject": HB(template.subject,{"account":account, "org":org}),
                        "body":HB(template.body,{"account":account, "org":org})
                    });
                    await common.wait(100); //Throttle the send as we are limited to 14 req/sec or every 72ms
                }  
            }


            // ------ ACCOUNTS TO DISABLE ----------------
            let disableAccountsToFind={
                "org_id":ORGID, 
                "last_activity":{"$lte":dayjs().subtract(disabler.disable.days, "day").toDate()}
            };

            if(disabler.tags && disabler.tags.length>0){ disableAccountsToFind.tags = disabler.tags; }
            await mdb.client().collection("accounts").updateMany(disableAccountsToFind,{"$set":{"active":false, "disabled":new Date()}});
            let disableAccounts = await mdb.client().collection("accounts").find(disableAccountsToFind).toArray();
            
        
            if(disableAccounts && disableAccounts.length>0 && typeof org.server.templates[disabler.disable.template]==="object"){
                let template = org.server.templates[disabler.disable.template];
                for(let account of disableAccounts){
                    await aws.email({
                        "to": ((process.env["ISDEV"]) ? "<EMAIL>" : account.email ),
                        "from": `${template.from} <${org.server.config.contacts.from}>`,
                        "reply": org.server.config.contacts.reply_to,
                        "subject": HB(template.subject,{"account":account, "org":org}),
                        "body":HB(template.body,{"account":account, "org":org})
                    });
                    await common.wait(100); //Throttle the send as we are limited to 14 req/sec or every 72ms
                }  
            }


            // ------ SEND ADMIN REPORT ----------------
            if(disabler.report && disabler.report.to && disabler.report.to.length>0 && typeof org.server.templates[disabler.report.template]==="object"){
                let template = org.server.templates[disabler.report.template];
                await aws.email({
                    "to": ((process.env["ISDEV"]) ? "<EMAIL>" : disabler.report.to ),
                    "from": `${template.from} <${org.server.config.contacts.from}>`,
                    "reply": org.server.config.contacts.reply_to,
                    "subject": HB(template.subject,{"warnsNew":warnAccounts.length, "warnExisting":existingWarnAccounts.length, "disables":disableAccounts.length}),
                    "body":HB(template.body,{"newWarnAccounts":warnAccounts, "existingWarnAccounts":existingWarnAccounts, "disableAccounts":disableAccounts})
                });
                console.log("REPORT")
            }else{
                console.log("No report")        
            }
            

        }

    };