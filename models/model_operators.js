const mdb = require("../libs/mdb.js");
const COLLECTION = "operators";

module.exports={
    create:async (dataToSave)=>{
        let newRecord = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
        
        if(newRecord && newRecord.insertedId){
            return newRecord.insertedId
        }else{
            return null;
        }
    },

    update:async (id, dataToSet)=>{        
        let updated = await mdb.client().collection(COLLECTION).updateOne({"_id":id},{"$set":dataToSet});
        
        if(updated && updated.modifiedCount>0){
            return true;
        }else{
            return false;
        }
    }
}