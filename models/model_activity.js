const mdb = require("../libs/mdb.js");
const COLLECTION = "activity";

module.exports={
  log:async (dataToSave)=>{
    if(!dataToSave.length){
      dataToSave = [dataToSave];
    }  

    dataToSave.forEach((d,i)=>{
      dataToSave[i].timestamp = new Date();
    })
  
    let newRecord = await mdb.client().collection(COLLECTION).insertMany(dataToSave);
    
    if(newRecord && newRecord.insertedId){
      return newRecord.insertedId
    }else{
      return null;
    }
  }
}
