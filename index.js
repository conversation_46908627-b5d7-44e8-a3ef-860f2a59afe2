require('dotenv').config()

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const dayjs = require('dayjs');
const multer = require("multer");
const upload = multer({ dest: '_uploads/' })

const mdb = require("./libs/mdb.js");
const response = require("./libs/response.js");
const cron = require("./libs/cron.js");

const orgs = require("./endpoints/orgs.js");
const auth = require("./endpoints/auth.js");
const account = require("./endpoints/account.js");
const items = require("./endpoints/items.js");
const links = require("./endpoints/links.js");
const orders = require("./endpoints/orders.js");
const activity = require("./endpoints/activity.js");
const communications = require("./endpoints/communications.js");
const charts = require("./endpoints/charts.js");
const wsAuth = require("./webservice/auth.js");
const wsWebhook = require("./webservice/webhook.js");
const formFlows = require(process.cwd()+"/endpoints/formflows.js");
const formFlowSubmission = require("./endpoints/formflowsubmissions.js");
const rebateSubmission = require("./endpoints/rebatesubmissions.js");
const rebates = require("./endpoints/rebates.js");
const contest = require("./endpoints/contest.js");
const inventory = require("./endpoints/inventory.js");
const objectStorage = require(process.cwd()+"/endpoints/objectstorage.js");
const feeds = require(process.cwd()+"/endpoints/feeds.js");

const app = express();

app.use(cors());
app.use(express.json({"limit":"150mb"}));
app.use('/static', express.static('static'));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

//app.use("*",activity.request); // log requests coming in for debugging

app.post("/auth/requestotp", auth.requestOTP);
app.post("/auth/verifyotp", auth.verifyOTP);
app.post("/auth/checkemail", auth.checkEmail);
app.post("/auth/register", auth.register);
app.post("/auth/acknowledged/:typeId/:accountId", auth.acknowledged);
app.post("/auth/autologin", auth.useLoginLink);
app.post("/auth/collaboratorotp", auth.collaboratorOTP);

app.use("/account*",account.verifyUser);
app.use("/items*",account.verifyUser);
app.use("/item*",account.loadIfUser);
app.use("/inventory*",account.verifyUser);
app.use("/inventory*",account.loadIfUser);
app.use("/link*",account.verifyUser);
app.use("/order*",account.verifyUser);
app.use("/activity*",account.verifyUser);
app.use("/reporting*",account.verifyUser);
app.use("/log*",account.loadIfUser);
app.use("/formflowsubmission*",account.loadIfUser);
app.use("/rebate*",account.loadIfUser);
app.use("/objectstorage*",account.loadIfUser);

app.use("/communications*",account.verifyUser);

app.use("/ws/*", wsAuth.verify);
app.use("/webhook/sample", wsWebhook.investigatePayload);
app.use("/webhook/sync/rebates/:rebateId", wsWebhook.syncRebateSubmission);
app.use("/webhook/shipstation", wsWebhook.shipstationUpdate);

app.use("/feeds/*", feeds.getOrg);

/***********************************************
  ORG
***********************************************/
app.get("/org/:url", orgs.getByURL);

app.put("/org/template*",(req,res,next)=>{req.REQUIREDPERMS=["org.u"]; account.verifyUser(req,res,next)});
app.put("/org/template/ui",orgs.saveUITemplate);
app.put("/org/template/server",orgs.saveServerTemplate);

app.put("/org/itemtag*",(req,res,next)=>{req.REQUIREDPERMS=["items.u"]; account.verifyUser(req,res,next)});
app.put("/org/itemtag/:tagId",orgs.saveItemTag);

app.delete("/org/itemtag*",(req,res,next)=>{req.REQUIREDPERMS=["items.u"]; account.verifyUser(req,res,next)});
app.delete("/org/itemtag/:tagId",orgs.deleteItemTag);

app.use("/org/export*",(req,res,next)=>{req.REQUIREDPERMS=["org.reports"]; account.verifyUser(req,res,next)});
app.post("/org/export/run/:exportId",orgs.runExport);
app.post("/org/export",orgs.createExport);
app.put("/org/export/:exportId",orgs.updateExport);
app.delete("/org/export/:exportId",orgs.deleteExport);

app.use("/org/import*",(req,res,next)=>{req.REQUIREDPERMS=["org.u"]; account.verifyUser(req,res,next)});
app.post("/org/import/:dataSource",orgs.runImport);

app.post("/alert", activity.alert);

/***********************************************
  ACCOUNT
***********************************************/
//------ FAVORITE ---------
app.post("/account/addfavorite", account.addFavorite);
app.delete("/account/removefavorite/:itemId", account.deleteFavorite);

//------ OUTBOX ---------
app.post("/account/addmailitem", account.addMailItem);
app.delete("/account/removemailitem/", account.removeMailItem);
app.post("/account/sendoutbox",account.sendOutbox);

//------ CART ---------
app.post("/account/cartitem", account.addCartItem);
app.post("/account/cart", account.addCart);
app.delete("/account/cartitem/:itemId", account.removeCartItem);
app.delete("/account/cartitems", account.clearCart);
app.put("/account/cartitem", account.updateCartItem);
app.get("/account/emails", account.getEmails);
app.get("/account", account.get);

//-------- WELCOME -----------
app.post("/account/welcome", account.sendWelcomeEmail);

/***********************************************
  ACCOUNTS - Admin
***********************************************/
app.get("/accounts",(req,res,next)=>{req.REQUIREDPERMS=["accounts.r"]; account.verifyUser(req,res,next)});
app.get("/accounts",account.getAll);

// Create a login link for user
app.post("/accounts/loginlink",(req,res,next)=>{req.REQUIREDPERMS=["accounts.adm"]; account.verifyUser(req,res,next)});
app.post("/accounts/loginlink",auth.createLoginLink);

// Approve / Deny User Account Request
app.post("/accounts/approver",(req,res,next)=>{req.REQUIREDPERMS=["accounts.u"]; account.verifyUser(req,res,next)});
app.post("/accounts/approver",account.approver);

// Get account session
app.get("/accounts/sessions/:accountId",account.getUserSessions);

app.delete("/accounts*",(req,res,next)=>{req.REQUIREDPERMS=["accounts.d"]; account.verifyUser(req,res,next)});
app.delete("/accounts/sessions/:accountId",account.deleteUserSessions);

app.get("/accounts/:accountId",(req,res,next)=>{req.REQUIREDPERMS=["accounts.r"]; account.verifyUser(req,res,next)});
app.get("/accounts/:accountId",account.getOne);

app.put("/accounts/:accountId",(req,res,next)=>{req.REQUIREDPERMS=["accounts.u"]; account.verifyUser(req,res,next)});
app.put("/accounts/:accountId",account.saveOne);

app.post("/accounts",(req,res,next)=>{req.REQUIREDPERMS=["accounts.c"]; account.verifyUser(req,res,next)});
app.post("/accounts/",account.create);

//app.delete("/accounts/:accountId",(req,res,next)=>{req.REQUIREDPERMS=["accounts.d"]; account.verifyUser(req,res,next)});

/***********************************************
  SESSION
***********************************************/
app.get("/session", auth.getSession);
app.delete("/session", auth.deleteSession);

/***********************************************
  ITEMS
***********************************************/
app.get("/items", items.getAll);

app.post("/items",(req,res,next)=>{req.REQUIREDPERMS=["items.c"]; account.verifyUser(req,res,next)}); //perm
app.post("/items", items.add);
app.post("/items/generateimage", items.generateImage);
app.post("/items/copy/:itemId", items.copy);

app.get("/items/nonactive", items.getNonactive);
app.get("/items/archived", items.getArchived);
app.get("/items/:context", items.getAll);
app.get("/item/:itemId", items.get);

app.delete("/items/file/:itemId/:fileId",(req,res,next)=>{req.REQUIREDPERMS=["items.u"]; account.verifyUser(req,res,next)}); //perm
app.delete("/items/file/:itemId/:fileId", items.deleteFile);

app.put("/items/*",(req,res,next)=>{req.REQUIREDPERMS=["items.u"]; account.verifyUser(req,res,next)}); //perm
app.put("/items/archive/:itemId", items.archive);
app.delete("/items/archive/:itemId", items.unarchive);
app.post("/items/tagsort/:tagId", items.tagSort);
app.put("/items/:itemId/basic", items.saveBasic);
app.put("/items/:itemId/files", items.saveFiles);
app.put("/items/:itemId/tags", items.saveTags);
app.put("/items/:itemId/permissions", items.savePermissions);
app.put("/items/:itemId/fields", items.saveFields);
app.put("/items/:itemId/inventory", items.saveInventory);
app.post("/items/:itemId/inventory", inventory.adjust);
app.put("/items/:itemId/linkeditems", items.saveLinkedItems);
app.put("/items/:itemId/variants", items.saveVariants);


/***********************************************
  LINKS
***********************************************/
app.post("/link", links.create);
app.get("/shared/link/:linkId", links.getSharedLink);

/***********************************************
  ORDERS
***********************************************/
app.get("/order/:orderId", orders.getOne);
app.post("/order", upload.array('files',10), orders.create);
app.put("/order/status/:orderId", orders.changeStatus);
app.post("/order/note/:orderId", orders.addNote);
app.put("/order/:orderId", orders.update);
app.post("/order/find", orders.find);
app.get("/orders", orders.getMine);


/***********************************************
  COMMUNICATIONS
***********************************************/
app.post("/communications/send/:templateKey", communications.send);
app.post("/communications/mailer", communications.mailer);

/***********************************************
  REPORTING
***********************************************/
app.post("/reporting/chart",charts.getChartData);


/***********************************************
  ACTIVITY TRACKING
***********************************************/
app.post("/activity/log", activity.log);
app.get("/e/:emailId", activity.emailView);


/***********************************************
  FORM FLOW SUBMISSION
***********************************************/
app.get("/formflows/:org_id/:id", formFlows.get);

/***********************************************
  FORM FLOW SUBMISSION
***********************************************/
app.get("/formflowsubmission/flow/:id", formFlowSubmission.getFlow);
app.post("/formflowsubmission/:id", formFlowSubmission.create);
app.put("/formflowsubmission/:id", formFlowSubmission.submit);
app.get("/formflowsubmission/pdf/:id", formFlowSubmission.pdf);
app.get("/formflowsubmission/file/:id/:fieldName", formFlowSubmission.file);
app.get("/formflowsubmission/:id", formFlowSubmission.get);


/***********************************************
  REBATES
***********************************************/
app.get("/rebates/active", rebates.getActive);

app.post("/rebates/export/*",(req,res,next)=>{req.REQUIREDPERMS=["rebatesubmission.adm"]; account.verifyUser(req,res,next)});
app.post("/rebates/export/:exportId", rebates.runExport);

/***********************************************
  REBATE SUBMISSION
***********************************************/
app.get("/rebatesubmission/:id", rebateSubmission.getOne);
app.get("/rebatesubmissions", rebateSubmission.list);
app.get("/rebatesubmissions/logs/:rebateSubmissionId", rebateSubmission.getLogs);

app.post("/rebatesubmissions",(req,res,next)=>{req.REQUIREDPERMS=["rebatesubmission.c"]; account.verifyUser(req,res,next)});
app.post("/rebatesubmissions", rebateSubmission.create);

app.put("/rebatesubmissions/*",(req,res,next)=>{req.REQUIREDPERMS=["rebatesubmission.u"]; account.verifyUser(req,res,next)});
app.put("/rebatesubmissions/collaborators/:rebateSubmissionId", rebateSubmission.addCollaborators);
app.put("/rebatesubmissions/note/:rebateSubmissionId", rebateSubmission.addNote);
app.put("/rebatesubmissions/status/:rebateSubmissionId", rebateSubmission.updateStatus);
app.put("/rebatesubmissions/coowner/:rebateSubmissionId", rebateSubmission.updateCoOwner);
app.put("/rebatesubmissions/broker/:rebateSubmissionId", rebateSubmission.updateBroker);
app.put("/rebatesubmissions/checklist/:rebateSubmissionId", rebateSubmission.updateChecklist);
app.put("/rebatesubmissions/log/:rebateSubmissionId", rebateSubmission.log);
app.post("/rebatesubmissions/meta/:rebateSubmissionId", rebateSubmission.metaFields);
app.post("/rebatesubmissions/clone/:rebateSubmissionId", rebateSubmission.clone);

app.delete("/rebatesubmissions/collaborators/:rebateSubmissionId",(req,res,next)=>{req.REQUIREDPERMS=["rebatesubmission.u"]; account.verifyUser(req,res,next)});
app.delete("/rebatesubmissions/collaborators/:rebateSubmissionId", rebateSubmission.deleteCollaborator);

app.delete("/rebatesubmissions/:rebateSubmissionId", rebateSubmission.delete);


app.get("/objectstorage/public/:id", objectStorage.getPublic);
app.get("/objectstorage/details/:ids", objectStorage.getDetails);
app.get("/objectstorage/download/:id", objectStorage.download);
app.delete("/objectstorage/:ids", objectStorage.delete);

/***********************************************
  CRONS
***********************************************/
app.get("/cron/startall", (req,res)=>{
  cron.startAll();
  response.success(res);
});
app.get("/cron/stopall", (req,res)=>{
  cron.stopAll();
  response.success(res);
});
app.get("/cron/start/:jobId", (req,res)=>{  
  if(cron.start(req.params.jobId)){
    response.success(res);
  }else{
    response.fail(res);
  }  
});
app.get("/cron/stop/:jobId", (req,res)=>{
  if(cron.stop(req.params.jobId)){
    response.success(res);
  }else{
    response.fail(res);
  }
});
app.get("/cron/status", (req,res)=>{
    response.success(res, cron.status());  
});

app.post("/cron/trigger/:jobId", async (req,res)=>{
  cron.runOnce(req.params.jobId, req.body);
  response.success(res);  
});


app.get("/syndigo/:orgName/gtin/:gtins", async (req,res)=>{
  console.log(`${req.params.orgName} Syndigo gtin`)
  cron.runOnce(`${req.params.orgName}Syndigo`, req.params);
  response.success(res);  
});

app.get("/syndigo/:orgName/:df", async (req,res)=>{
  console.log(`${req.params.orgName} Syndigo DF`)
  cron.runOnce(`${req.params.orgName}Syndigo`, req.params);
  response.success(res);  
});

app.get("/syndigo/:orgName", async (req,res)=>{
  cron.runOnce(`${req.params.orgName}Syndigo`, {});
  response.success(res);  
});

/***********************************************
  CONTEST
***********************************************/
app.get("/contest/:contestKey/verifyplayer/:playerId", contest.verifyPlayer);
app.post("/contest/:contestKey/play/:playerId", contest.play);

/***********************************************
  FEEDS -- PUBLIC
***********************************************/
app.all("/feeds/:feedKey", feeds.processFeed);

app.get('*', (req, res) => {
  response.fail(res,"invalid request");
});


mdb.connect().then(()=>{
  app.listen(8080, () => {    
    console.log('Server listening - v0229.1');    
    //Only auto init cron on PRODUCTION
    if(process.env["ENV"] && process.env["ENV"]==="PROD"){
      cron.init();
    }      
  });

}).catch((e)=>{
  console.log("MongoError",e);
})


