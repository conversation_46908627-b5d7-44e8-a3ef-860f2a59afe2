{"name": "nodejs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.290.0", "@aws-sdk/client-ses": "^3.272.0", "@pdf-lib/fontkit": "^1.1.1", "@types/node": "^18.0.6", "aws-sdk": "^2.1316.0", "body-parser": "^1.20.2", "canvas": "^2.11.2", "cors": "^2.8.5", "cron": "^2.3.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "dotenv": "^16.0.3", "exceljs": "^4.3.0", "express": "^4.18.2", "handlebars": "^4.7.7", "json-2-csv": "^4.1.0", "lodash": "^4.17.21", "mongodb": "^5.0.1", "multer": "^2.0.2", "node-fetch": "^2.7.0", "node-zendesk": "^6.0.1", "pdf-img-convert": "^1.2.1", "pdf-lib": "^1.17.1", "randomstring": "^1.2.3", "sharp": "^0.32.3", "ssh2-sftp-client": "^12.0.1"}}