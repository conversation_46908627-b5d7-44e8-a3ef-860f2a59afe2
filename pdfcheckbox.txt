const { PDFDocument, rgb } = require('@pdf-lib/pdf-lib');
const fs = require('fs');

async function fillPdfForm() {
  // Read the existing PDF file
  const existingPdfBytes = fs.readFileSync('path/to/your/pdf/file.pdf');

  // Load the existing PDF document
  const pdfDoc = await PDFDocument.load(existingPdfBytes);

  // Get the first page of the document
  const page = pdfDoc.getPage(0);

  // Specify the name of the checkbox field you want to mark
  const checkboxFieldName = 'yourCheckboxFieldName';

  // Get the checkbox field by name
  const checkbox = page.getCheckBox(checkboxFieldName);

  // Mark the checkbox as checked
  checkbox.check();

  // Save the modified PDF document
  const modifiedPdfBytes = await pdfDoc.save();
  fs.writeFileSync('path/to/save/modified/file.pdf', modifiedPdfBytes);

  console.log('PDF form filled and saved successfully!');
}

// Call the function
fillPdfForm();
