const mdb = require("../libs/mdb.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const common = require("../libs/common.js");
const Handlebars = require("handlebars");
const activityModel = require("../models/model_activity.js");
const COLLECTION = "communications";
const ORGS_COLLECTION = "orgs";

module.exports={    
    send:async function(req, res){
        try{
            if(!req.body.to_account_tags){
                response.missing(res, "Need to select an account group to send to.");
                return;
            }
    
            if(!req.body.from_name){
                response.missing(res, "From Name can not be empty.");
                return;
            }
    
            if(!req.body.subject){
                response.missing(res, "Subject can not be empty.");
                return;
            }
    
            if(!req.params.templateKey){
                response.missing(res, "Template Key can not be empty.");
                return;
            }
    
            if(typeof req.ORG.server.templates[req.params.templateKey]==="undefined"){
                response.missing(res, "Missing template.");
                return;
            }
    
            
    
            //----- ITEMS -----------------------------------
            let items=[];        
            
            for(let item of req.body.items){            
                items.push({
                    "url":((process.env["ISDEV"]) ? process.env["SERVER_URL"] : ((typeof req.ORG.url==="string") ? "https://"+req.ORG.url : "https://"+req.ORG.url[0] ) )+"/portal/item/details/"+item.id,
                    "image":encodeURIComponent(item.image),
                    "name":item.name
                })             
            }
    
            //----- GET EMAILS FROM TAGS --------------------
            let toEmails=[];
            let accounts = await mdb.client().collection("accounts").find({"active":true, "org_id":req.ORG._id, "tags":{"$in":req.body.to_account_tags}},{"projection":{"email":true}}).toArray();
            for (let account of accounts){
                if(toEmails.indexOf(account.email)===-1){
                    toEmails.push(account.email);
                }
            }
        
            let emailBody=Handlebars.compile(req.ORG.server.templates[req.params.templateKey].body)({"message":req.body.message, "items":items});
    
            await mdb.client().collection("communications").insertOne({
                "org_id":req.ORG._id,
                "account_id":req.SESSION.account_id,
                "template":req.params.templateKey,
                "from": `${req.body.from_name} <${req.ORG.server.config.contacts.from}>`,
                "reply": req.ORG.server.config.contacts.reply_to,
                "subject": req.body.subject.trim(),
                "body":emailBody,
                "to_tags":req.body.to_account_tags,
                "to_emails":toEmails,
                "created":new Date()
            });
    
            response.success(res);
    
           /* console.log(toEmails);
            console.log(req.ORG.server.config.contacts.from);
            console.log(req.ORG.server.config.contacts.reply_to);*/
    
           for(let toEmail of toEmails){
                if(toEmail && toEmail.length>5){
                    let emailObj = {
                        "to": ((process.env["ISDEV"]) ? `<EMAIL>` : toEmail ),
                        "from": `${req.body.from_name} <${req.ORG.server.config.contacts.from}>`,
                        "reply": req.ORG.server.config.contacts.reply_to,
                        "subject": req.body.subject.trim()+((process.env["ISDEV"]) ? ` [TO: ${toEmail}]` : ""),
                        "body":emailBody
                    };
                    try{                        
                        await aws.email(emailObj);
                    }catch(e){                        
                        console.log("AWS EMAIL error",e);
                        console.log(emailObj);
                    }
                   
                    await common.wait(100); //Throttle the send as we are limited to 14 req/sec or every 72ms
                }            
            }
            
            await mdb.client().collection("accounts").updateOne({_id:req.SESSION.account_id},{"$set":{"outbox":[]}});
    
            console.log("COMMUNICATIONS SENT");
        }catch(e){
            console.log("ERROR - communications.send", e);           
        }        
    },

    mailer:async function(req,res){
        try{

            if(!req.body.to || !req.body.subject || !req.body.body || !req.body.from){
                response.missing(res, "Missing required parameters.")
            }else{
    
                if(typeof req.body.to==="string"){ req.body.to = [req.body.to]; }
                
                await aws.email({
                    "to": ((process.env["ISDEV"]) ? "<EMAIL>" : req.body.to ),
                    "from": `${req.body.from} <${req.ORG.server.config.contacts.from}>`,
                    "reply": ((req.body.replyTo.trim()) ? req.body.replyTo : req.ORG.server.config.contacts.from) ,
                    "subject": req.body.subject.trim(),
                    "body":req.body.body.trim(),
                    "type":((typeof req.body.type==="string") ? req.body.type.toLowerCase().trim() : null)
                });
    
                response.success(res);
            }

        }catch(e){
            console.log(e);
            response.error(res,e);
        }

    }
};