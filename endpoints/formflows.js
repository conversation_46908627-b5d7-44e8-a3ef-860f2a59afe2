const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const COLLECTION="formflows";
const fs = require("fs");

module.exports={
    get: async (req, res) => {  
        try{      
            let dataToFind={"org_id":mdb.objectId(req.params.org_id)};
            try{
                dataToFind._id=mdb.objectId(req.params.id.trim());
            }catch(e){
                dataToFind.key=req.params.id.trim().toLowerCase();
            }

            let flow = await mdb.client().collection(COLLECTION).findOne(dataToFind);           
            if (flow) {
                response.success(res, flow);
            } else {
                console.log("ERROR - No Flow Found",req.params)
                response.missing(res);       
            }

        }catch(e){
            console.log("ERROR - formflows.getFlow", e);
            response.error(res,e);
        }        
    
    }
}