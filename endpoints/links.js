const mdb = require("../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "links";
const response = require("../libs/response.js");
const activityModel = require("../models/model_activity.js");

module.exports={

/********************************************** 
    CREATE LINK
**********************************************/  
  create:async function(req, res){  
    try{
      let reqBody = ((req.body) ? req.body : {});
      let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }, {"projection":{"server":true}});

      if(!reqBody.item_id){
        response.missing(res, "Missing item id");
        return;
      }
      
      let dataToSave={
        "account_id":req.SESSION.account_id,
        "item_id":mdb.objectId(reqBody.item_id),
        "org_id":req.SESSION.org_id,
        "created":new Date()
      };

      if(org && org.server && org.server.config && org.server.config.emailed_link_ttl){
        dataToSave.expires=dayjs().add(org.server.config.emailed_link_ttl, "hour").toDate();
      }
      
      let link = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
      
      if (link) {
        response.success(res,link.insertedId.toString());
      } else {
        response.fail(res,"Failed to create link.")
      }
    }catch(e){
      console.log("ERROR - links.create", e);
      response.error(res,e);
    }
  },


/********************************************** 
    GET SHARED LINK
**********************************************/  
  getSharedLink:async function(req, res){
    try{
      let link = await mdb.client().collection(COLLECTION).aggregate([
        {"$match":{"_id":mdb.objectId(req.params.linkId)}},
        {
          $lookup:
             {
                from: "items",
                localField: "item_id",
                foreignField: "_id",
                as: "item"
            }
       },
        {
          $project:
             {
                "org_id":1, "account_id":1, "item.image":1, "item.file":1, "item.name":1, "item.fields":1, "item.updated":1, "item.files":1
            }
       }
      ]).toArray();
      
      if(link && link[0] && link[0].item){
        response.success(res,link[0].item[0]);
  
        activityModel.log({
            "org_id":link[0].org_id,          
            "account_id":link[0].account_id,
            "event":{
              "system":"link",
              "action":"view"   
            },          
            "data":{
              "link_id":mdb.objectId(req.params.linkId),
              "item_id":link[0].item[0]
            }
        });
        
      }else{
        response.fail(res,"Failed to find your link")
      } 
    }catch(e){
      console.log("ERROR - links.getSharedLink", e);
      response.error(res,e);
    }       
  }
  
};
