const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");

module.exports={

    /*********************************************************************
     *  GET CHART DATA
     ********************************************************************/
    getChartData:async function(req,res){
        try{
            if(!req.body.dataSource){
                response.missing(res, "Missing Data Source");
                return;
            }
    
            let output = null;
    
            switch(req.body.dataSource.trim().toUpperCase()){
                case "ITEMORDERS":
                    output = await module.exports._items(req);
                    output = _.map(output, function(o){ return {"label":o.item, "value":Number(o.totals.orders)}})
                    output = _.sortBy(output, function(o){ return o.value});
                    output.reverse();
    
                break
                case "ITEMQTY":
                    output = await module.exports._items(req);
                    output = _.map(output, function(o){ return {"label":o.item, "value":Number(o.totals.qty)}})
                    output = _.sortBy(output, function(o){ return o.value});
                    output.reverse();
                break;
                case "ITEMSEMAILED":
                    output = await module.exports._itemsEmailed(req);
                    output = _.map(output, function(o){ return {"label":o.label, "value":Number(o.qty)}})
                    output = _.sortBy(output, function(o){ return o.value});
                    output.reverse();
                break;
                case "ITEMSDOWNLOADED":
                    output = await module.exports._itemsDownloaded(req);
                    output = _.map(output, function(o){ return {"label":o.label, "value":Number(o.qty)}})                                    
                break;
                case "TAGSSEARCHED":
                    output = await module.exports._tags(req);
                    output = _.map(output, function(o){ return {"label":o.label, "value":Number(o.qty)}})                                    
                break;
                case "KEYWORDSSEARCHED":
                    output = await module.exports._keywords(req);
                    output = _.map(output, function(o){ return {"label":o.label, "value":Number(o.qty)}})                                    
                break;

            }
    
            if(output){
                response.success(res, output);
            }else{
                response.noData(res);
            }
        }catch(e){
            console.log("ERROR - reporting.getChartData", e);
            response.error(res,e);
        }    
    },

    /*********************************************************************
     *  CHART - ITEMS
     ********************************************************************/
    _items:async function(req){
        let dataToFind={"org_id":req.SESSION.org_id};
        let itemTotals={};
        let output=[];

        if(req.body.from && req.body.to){
            dataToFind["$and"]=[
                {"created":{"$gte":dayjs(req.body.from).toDate()}},
                {"created":{"$lt":dayjs(req.body.to).add(1,"day").toDate()}}
            ];
        }

        let allItems = await mdb.client().collection("items").find({"org_id":req.SESSION.org_id}).toArray();
        let allOrders = await mdb.client().collection("orders").find(dataToFind).toArray();

        for(let order of allOrders){
            for(let itemId of Object.keys(order.items)){
                if(typeof itemTotals[itemId]==="undefined"){
                    itemTotals[itemId]={
                        "orders":0,
                        "qty":0
                    }
                }

                itemTotals[itemId].orders++;
                itemTotals[itemId].qty += Number(order.items[itemId].qty);
            }            
        }

        for(let itemId of Object.keys(itemTotals)){
            output.push({
                "item":_.find(allItems,{"_id":mdb.objectId(itemId)}).name,
                "totals":itemTotals[itemId]
            })
        }

        
        
        return output;
    },


    /*********************************************************************
     *  CHART - ITEMS EMAILED
     ********************************************************************/
    _itemsEmailed:async function(req){
        let dataToFind={"org_id":req.SESSION.org_id};
        let itemTotals={};
        let output=[];

        if(req.body.from && req.body.to){
            dataToFind["$and"]=[
                {"timestamp":{"$gte":dayjs(req.body.from).toDate()}},
                {"timestamp":{"$lt":dayjs(req.body.to).add(1,"day").toDate()}}
            ];
        }
        
        let allEmailed = await mdb.client().collection("emails").find(dataToFind).toArray();

        for(let emailed of allEmailed){
            for(let item of emailed.items){
                if(typeof itemTotals[item.id]==="undefined"){
                    itemTotals[item.id]={
                        "qty":0,
                        "label":item.name                        
                    }                    
                }
                itemTotals[item.id].qty++;
            }
        }

        for(let itemId of Object.keys(itemTotals)){
            output.push({
                "label":itemTotals[itemId].label,
                "qty":itemTotals[itemId].qty
            })
        }

        return output;

    },


    /*********************************************************************
     *  CHART - ITEMS DOWNLOADED
     ********************************************************************/
    _itemsDownloaded:async function(req){
        let dataToFind={"org_id":req.SESSION.org_id,"event.system":"item", "event.action":"downloaded"};
        let itemTotals={};
        let output=[];

        if(req.body.from && req.body.to){
            dataToFind["$and"]=[
                {"timestamp":{"$gte":dayjs(req.body.from).toDate()}},
                {"timestamp":{"$lt":dayjs(req.body.to).add(1,"day").toDate()}}
            ];
        }
        
        let records = await mdb.client().collection("activity").aggregate([
            {"$match":dataToFind},
            {"$group":{ _id: "$data.name", qty: { "$count": {} } }},
            {"$sort":{"qty":-1}}
        ]).toArray();

        for(let record of records){
            output.push({
                "label":record._id,
                "qty":record.qty
            });
        }        

        console.log(output);
        return output;

    },


    /*********************************************************************
     *  CHART - TAGS
     ********************************************************************/
    _tags:async function(req){        
        let dataToFind={"org_id":req.SESSION.org_id,"event.system":"search", "event.action":"tag"};        
        let output=[];

        if(req.body.from && req.body.to){
            dataToFind["$and"]=[
                {"timestamp":{"$gte":dayjs(req.body.from).toDate()}},
                {"timestamp":{"$lt":dayjs(req.body.to).add(1,"day").toDate()}}
            ];
        }
        
        let records = await mdb.client().collection("activity").aggregate([
            {"$match":dataToFind},
            {"$group":{ _id: "$data", qty: { "$count": {} } }},
            {"$sort":{"qty":-1}}
        ]).toArray();

        for(let record of records){
            output.push({
                "label":((req.ORG.item_tags && req.ORG.item_tags[record._id]) ? req.ORG.item_tags[record._id].label : record._id),
                "qty":record.qty
            });
        }        
        
        return output;

    },

    /*********************************************************************
     *  CHART - KEYWORDS
     ********************************************************************/
    _keywords:async function(req){        
        let dataToFind={"org_id":req.SESSION.org_id,"event.system":"search", "event.action":"keyword"};        
        let output=[];

        if(req.body.from && req.body.to){
            dataToFind["$and"]=[
                {"timestamp":{"$gte":dayjs(req.body.from).toDate()}},
                {"timestamp":{"$lt":dayjs(req.body.to).add(1,"day").toDate()}}
            ];
        }
        
        let records = await mdb.client().collection("activity").aggregate([
            {"$match":dataToFind},
            {"$group":{ _id: "$data", qty: { "$count": {} } }},
            {"$sort":{"qty":-1}}
        ]).toArray();

        for(let record of records){
            output.push({
                "label":record._id,
                "qty":record.qty
            });
        }        
        
        return output;

    }
};
