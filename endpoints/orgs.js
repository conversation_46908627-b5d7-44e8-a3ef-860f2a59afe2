const mdb = require("../libs/mdb.js");
const COLLECTION = "orgs";
const response = require("../libs/response.js");
const common = require("../libs/common.js");
const exportLib = require("../libs/exporter.js");
const csv = require("fast-csv");
const fs = require('fs');

module.exports={

/*****************************************
    GET BY URL
*****************************************/
  getByURL:async function(req, res){  
    try{
      let org = await mdb.client().collection(COLLECTION).findOne({ "url": req.params.url, "deleted": { "$exists": false } });
      if (org) {      
        response.success(res, org);
      } else {
        console.log("ERROR - No Org Found",req.params)
        response.missing(res);       
      }
    }catch(e){
      console.log("ERROR - orgs.getByURL", e);
      response.error(res,e);
    }    
    
  
  },

/*****************************************
    SAVE UI TEMPLATE
*****************************************/
  saveUITemplate:async function(req,res){
    try{
      let reqBody = ((req.body) ? req.body : {});
      if(reqBody.template && reqBody.template.length===2 && reqBody.contents){
        let key = `ui.templates.${reqBody.template[0]}.${reqBody.template[1]}`;
        let dataToSet={};
        dataToSet[key]=reqBody.contents.trim();
        
        let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$set":dataToSet});
    
        if(updated){
          response.success(res);
        }else{
          response.fail(res);
        }
      }else{
        response.missing(res, "invalid request parameters");
      }
    }catch(e){
      console.log("ERROR - orgs.saveUITemplate", e);
      response.error(res,e);
    }        
  },

/*****************************************
    SAVE SERVER TEMPLATE
*****************************************/
  saveServerTemplate:async function(req,res){
    try{
      let reqBody = ((req.body) ? req.body : {});
      if(reqBody.template && reqBody.template.length>0 && reqBody.contents){
        let key = `server.templates.${reqBody.template[0]}`;
        if(reqBody.template.length===2){
          key+=`.${reqBody.template[1]}`
        }
        
        let dataToSet={};
        dataToSet[key]={
          "from":reqBody.from.trim(),
          "body":reqBody.contents.trim()
        };

        if(reqBody.subject){
          dataToSet[key].subject = reqBody.subject;
        }
        
        let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$set":dataToSet});
    
        if(updated && updated.modifiedCount>0){
          response.success(res);
        }else{
          response.fail(res);
        }
      }else{
        response.missing(res, "invalid request parameters");
      }
    }catch(e){
      console.log("ERROR - orgs.saveServerTemplate", e);
      response.error(res,e);
    }  
  },

/*****************************************
    SAVE ITEM TAG
*****************************************/
  saveItemTag:async function(req,res){
    try{
      let reqBody = ((req.body) ? req.body : {});
      let dataToSet={};
      let key = `item_tags.${req.params.tagId}`;
      dataToSet[key]={
        "label":reqBody.label,
        "searchable":reqBody.searchable
      };
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
          response.success(res);
      }else{
          response.fail(res);
      }
    }catch(e){
      console.log("ERROR - orgs.saveItemTag", e);
      response.error(res,e);
    }    
  },

/*****************************************
    DELETE ITEM TAG
*****************************************/
deleteItemTag:async function(req,res){  
  try{
    let dataToSet={};
    let key = `item_tags.${req.params.tagId}`;
    dataToSet[key]="";

    await mdb.client().collection("items").updateMany({"org_id":req.SESSION.org_id, "tags":req.params.tagId},{"$pull":{"tags":req.params.tagId}});
    let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$unset":dataToSet});
    
    if(updated && updated.modifiedCount>0){
        response.success(res);
    }else{
        response.fail(res);
    }
  }catch(e){
      console.log("ERROR - orgs.deleteItemTag", e);
      response.error(res,e);
  }  
},

/*****************************************
    EXPORT - CREATE
*****************************************/
createExport:async function(req,res){
    try{     
      let reqBody = ((req.body) ? req.body : {});

      let dataToSave={};
      let newExportId = common.rndString(10,["letters", "numbers", "safespecials"]);
      let exportData = {
        "name":reqBody.name,
        "updated_by":req.SESSION.email,
        "last_update":new Date()
      };

      dataToSave[`exports.${newExportId}`]=exportData;
  
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$set":dataToSave});
      
      if(updated && updated.modifiedCount>0){
          response.success(res, {"id":newExportId, "data":exportData});
      }else{
          response.fail(res);
      }
    }catch(e){
        console.log("ERROR - orgs.createExport", e);
        response.error(res,e);
    }  
},

/*****************************************
  EXPORT - UPDATE
*****************************************/
updateExport:async function(req,res){
    try{     
      let reqBody = ((req.body) ? req.body : {});
      let exportId = req.params.exportId.trim().toLowerCase();

      if((typeof reqBody==="object" && Object.keys(reqBody).length===0) || !exportId){
        response.fail(res);
        
      }else{

        let dataToSave={};
            
        dataToSave[`exports.${exportId}`]=Object.assign(reqBody, {"updated_by":req.SESSION.email, "last_update":new Date()});

        let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$set":dataToSave});
        
        if(updated && updated.modifiedCount>0){
            response.success(res);
        }else{
            response.fail(res);
        }

      }

      
    }catch(e){
        console.log("ERROR - orgs.createExport", e);
        response.error(res,e);
    }  
},

/*****************************************
    EXPORT - DELETE
*****************************************/
deleteExport:async function(req,res){
      try{             
        let exportId = req.params.exportId.trim().toLowerCase();
  
          let dataToSave={};            
          dataToSave[`exports.${exportId}`]={};
          let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.org_id},{"$unset":dataToSave});
          
          if(updated && updated.modifiedCount>0){
              response.success(res);
          }else{
              response.fail(res);
          }
      
    
      }catch(e){
          console.log("ERROR - orgs.createExport", e);
          response.error(res,e);
      }  
},

/*****************************************
  EXPORT - RUN
*****************************************/
runExport:async function(req,res){
  
  let org = await mdb.client().collection(COLLECTION).findOne({_id:req.SESSION.org_id});
  if(!org){
    response.fail(res);
    return false;
  }

  if(typeof org.exports[req.params.exportId]!=="undefined"){
    let config= Object.assign({
      "created_by":req.SESSION.email,
      "email_to":req.body.emailTo,
    },org.exports[req.params.exportId]);

    exportLib.run(req.SESSION.org_id, req.params.exportId, config);

    response.success(res);
  }

  /*exportLib.run(mdb.objectId("6425eef367c2b869c563d7be"),{
    "name":"Kens Items Report",
    "created_by":"<EMAIL>",
    "datasource":"items",
    "date_range":{"rel":"YTD", "field":"last_activity"},
    "filters":[
      {
        "field":"tags",
        "label":"Tags: All",
        "value":null
      }        
    ],
    "fields":[
      {"id":"name","header":"Name"},        
      {"id":"actions.link", "header":"Linkable"},
      {"id":"actions.download", "header":"Downloadable"},
      {"id":"actions.email","header":"Emailable"},
      {"id":"actions.order","header":"Orderable"}        
    ]
  });*/
},

/*****************************************
  IMPORT - RUN
*****************************************/
runImport:async function(req,res){
  
  let org = await mdb.client().collection(COLLECTION).findOne({_id:req.SESSION.org_id});

  if(!org){
    response.fail(res);
    return false;
  }
  let fileBuffer = Buffer.from(req.body.contents, 'base64').toString();

  let uploadedData=[], uploadError=null;
  csv.parseString(fileBuffer, { headers: true })
    .on('error', error => uploadError = error)
    .on('data', row => uploadedData.push(row))
    .on('end', rowCount => processData());

  function processData(){
    switch(req.params.dataSource){
      default:
        processAccountData(uploadedData)
      break;
    }
  }

  async function processAccountData(accounts){
    let errors=[];

    let accountController = require(process.cwd()+"/endpoints/account.js");

    for(let account of accounts){

      Object.keys(account).forEach(ak=>{
         let temp = account[ak];
        delete account[ak];
        account[ak.toLowerCase()] = temp;
      });

      
      if(!account.email){
        errors.push(account);
      }else{
       
        // update account
        let dataToSave={
          "email":account.email.toLowerCase().trim(),          
          "updated":new Date() 
        }

        if(typeof account.status !== "undefined"){
          if(typeof account.status === "string"){
            account.status = account.status.trim();
          }
          try{
            if(account.status.toLowerCase()==="true"){
              account.status = 1;
            }else if(account.status.toLowerCase()==="false"){
              account.status = 0;
            }
            dataToSave.active = Boolean(Number(account.status))
          }catch(e){
            dataToSave.active = false;
          }
          
        }

        if(typeof account.active !== "undefined"){
          if(typeof account.active === "string"){
            account.active = account.active.trim();
          }
          try{
            if(account.active.toLowerCase()==="true"){
              account.active = 1;
            }else if(account.active.toLowerCase()==="false"){
              account.active = 0;
            }
            dataToSave.active = Boolean(Number(account.active));
          }catch(e){
            dataToSave.active = false;
          }
          
        }
  
        if(account.tags){        
          let tags=parseTags(account.tags.trim());        
          if(tags){
            dataToSave.tags = tags;
          }
        }
  
        let fieldMap={}, allFields={};
        Object.keys(org.data_configs.accounts.fields).forEach((fieldId)=>{
          fieldMap[org.data_configs.accounts.fields[fieldId].label.toLowerCase()] = fieldId;
          allFields[fieldId]="";
        });
  

        if(account._id && account._id.trim().length>0){          
          Object.keys(account).forEach((accountField)=>{
            if(fieldMap[accountField]){
              dataToSave[`fields.${fieldMap[accountField]}`] = account[accountField];
            }
          });
          
          
          try{
            let updated = await mdb.client().collection("accounts").updateOne({"_id":mdb.objectId(account._id.trim())}, {"$set":dataToSave});
            
            if(!updated || !updated.matchedCount){
              errors.push(account);
            }else{              
              if(account.welcome===true || account.welcome===1 || (typeof account.welcome==="string"  &&account.welcome.toLowerCase()==="true") || (typeof account.welcome==="string"  && account.welcome.toLowerCase()==="yes")){
                accountController.__sendWelcomeEmail(account._id.trim(),org);
              }
              
            }
          }catch(e){
            console.log(e);
            errors.push(account);
          }
          
  
        }else{          
          dataToSave.org_id = org._id;
          dataToSave.created = new Date();
          dataToSave.favorites = [];
          dataToSave.outbox = [];
          dataToSave.cart = {};
          dataToSave.meta={
            "bulk":true
          };
          dataToSave.fields=allFields;
          Object.keys(account).forEach((accountField)=>{
            if(fieldMap[accountField]){
              dataToSave.fields[fieldMap[accountField]] = account[accountField];
            }
          });
          
          try{
            let newItem = await mdb.client().collection("accounts").insertOne(dataToSave);
            if(!newItem || !newItem.insertedId){
              errors.push(account);
            }else{              
              if(account.welcome===true || (typeof account.welcome==="string"  && account.welcome.toLowerCase()==="true") || account.welcome===1 || (typeof account.welcome==="string"  && account.welcome.toLowerCase()==="yes")){
                accountController.__sendWelcomeEmail(newItem.insertedId,org);
              }
              
            }          
          }catch(e){
            console.log(e.message);
            errors.push(account);
          }
         
        }
      }
  
    }

    response.success(res,errors);

  }

  function parseTags(tagsString){
    let output=[];
    if(tagsString.length>0 && tagsString!=="[]"){

      let orgAccountTags={};
      Object.keys(org.account_tags).forEach(function(tagId){
        orgAccountTags[org.account_tags[tagId].label] = tagId;
      })

      let tagsLabel = tagsString.split(",");
      tagsLabel.forEach((tagLabel)=>{
        if(orgAccountTags[tagLabel]){
          output.push(orgAccountTags[tagLabel]);
        }
      });
    }

    return output;

  }

}

  
  
};
