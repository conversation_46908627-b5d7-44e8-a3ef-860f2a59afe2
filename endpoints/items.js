const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const COLLECTION = "items";
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const pdfToImage = require("../libs/pdftoimage.js");


module.exports={

/********************************************** 
    GET ALL
**********************************************/  
  getAll:async function(req, res){
    try{
      let dataToFind = { "org_id": req.SESSION.org_id, "archived": { "$exists": false } };
      if(req.params.context && req.params.context==="portal"){
        dataToFind.status=1;
      }else if(req.params.context && req.params.context==="everything"){
        delete dataToFind.archived;
      }

      //let orgItems = await mdb.client().collection(COLLECTION).find(dataToFind).toArray();

      let orgItems = await mdb.client().collection(COLLECTION).aggregate([
        {"$match":dataToFind},
        {
          "$lookup": {
            "from": "inventory",
            "localField": "_id",
            "foreignField": "item_id",
            "as": "inventory_records"
          }
        },
        { $addFields: {
          "totalInventoryQty": { $sum: "$inventory_records.qty" }
        }},
        {
          "$project": {
            "inventory_records": 0
          }
        }

      ]).toArray();

      if (!orgItems) {      
        response.missing(res);
      } else {
        let dataToReturn=[];
        orgItems.forEach((orgItem)=>{        
            if(!orgItem.visible_to || orgItem.visible_to.length===0 || (req.SESSION.tags && common.hasTag(req.SESSION.tags, orgItem.visible_to)) || req.params.context==="admin" || req.params.context==="everything"){
              dataToReturn.push(orgItem);
            }
          
        });
        orgItems=[];
        response.success(res,dataToReturn);
        dataToReturn=[];
        
      }
    }catch(e){
      console.log("ERROR - items.getAll", e);
      response.error(res,e);
    }    
  },

  /********************************************** 
    GET ARCHIVED
  **********************************************/  
  getArchived:async function(req,res){
    try{
      let dataToFind = { "org_id": req.SESSION.org_id, "archived": { "$exists": true } };
      let orgItems = await mdb.client().collection(COLLECTION).find(dataToFind).toArray();

      if (!orgItems) {      
        response.missing(res);
      } else {
        let dataToReturn=[];
        orgItems.forEach((orgItem)=>{        
            if(!orgItem.visible_to || orgItem.visible_to.length===0 || (req.SESSION.tags && common.hasTag(req.SESSION.tags, orgItem.visible_to)) || req.params.context==="admin"){
              dataToReturn.push(orgItem);
            }
          
        });
        orgItems=[];
        response.success(res,dataToReturn);
        dataToReturn=[];
        
      }
    }catch(e){
      console.log("ERROR - items.getArchived", e);
      response.error(res,e);
    }   
  },

/********************************************** 
    GET Non-active
**********************************************/  
  getNonactive:async function(req, res){    
    try{
      let dataToFind = { "org_id": req.SESSION.org_id, "$or":[{"archived": { "$exists": true }}, {"status":{"$ne":1}}] };
      let orgItems = await mdb.client().collection(COLLECTION).find(dataToFind).toArray();
      if (!orgItems) {      
        response.missing(res);
      } else {
        let dataToReturn=[];
        orgItems.forEach((orgItem)=>{        
            if(!orgItem.visible_to || orgItem.visible_to.length===0 || (req.SESSION.tags && common.hasTag(req.SESSION.tags, orgItem.visible_to))){
              dataToReturn.push(orgItem);
            }
          
        });
        orgItems=[];
        response.success(res,dataToReturn);
        dataToReturn=[];    
      }
    }catch(e){
      console.log("ERROR - items.getNonactive", e);
      response.error(res,e);
    }
  },

/********************************************** 
    GET ONE
**********************************************/  
  get:async function(req, res){    
    try{
      let orgItem = await mdb.client().collection(COLLECTION).aggregate([
        {
          "$match": {
            "_id": {"$in": req.params.itemId.split(",").map(i => mdb.objectId(i))}, 
            "org_id": req.SESSION.org_id
          }
        },
        {
          "$lookup": {
            "from": "inventory",
            "localField": "_id",
            "foreignField": "item_id",
            "as": "inventory_records"
          }
        },
        { $addFields: {
          "totalInventoryQty": { $sum: "$inventory_records.qty" }
        }}
      ]).toArray();
      
      if (orgItem && orgItem.length>0) {
        response.success(res, orgItem[0]);        
      } else {
        response.missing(res);   
      }
    }catch(e){
      console.log("ERROR - items.get", e);
      response.error(res,e);
    }  
  },

/********************************************** 
    ADD
**********************************************/    
  add:async function(req,res){
    try{
      if(!req.body.name || !req.body.name.trim()){
        response.missing(res, "Missing item name.");
      }
  
      let dataToSave={      
        "org_id":req.SESSION.org_id,
        "name":req.body.name.trim(),
        "image":"",
        "actions":{
          "link":false,
          "download":false,
          "email":false,
          "order":false
        },
        "fields":{},
        "tags":[],
        "inventory":{
          "track" : true,         
          "restock_level" : Number(0),
          "oos_action" : ""
        },
        "status" : Number(0),
        "files":{},
        "created":new Date(),
        "updated_by":"System",
        "updated":new Date()     
      };
  
      let newItem = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
      if(!newItem || !newItem.insertedId){
        response.error(res,"Failed to create item");
      }else{
        response.success(res, newItem.insertedId);
      }
    }catch(e){
      console.log("ERROR - items.add", e);
      response.error(res,e);
    }
    
  },

/********************************************** 
    SAVE BASIC
**********************************************/  
  saveBasic:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      };
  
      dataToSet.name=req.body.name;
      dataToSet.status=req.body.status;
      if(req.body.image){
        dataToSet.image=common.safeFilename(req.body.image);
      }
    
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
  
        // If this is a new file to upload, then upload to storage
        if(req.body.image_src && req.body.thumb_src){
  
          await aws.putS3Object({
            "key":req.SESSION.org_id+"/images/"+dataToSet.image,
            "body":Buffer.from(req.body.image_src, 'base64'),
            "encoding":"base64",
            "contentType":req.body.content_type,
            "acl":"public-read"
          });
  
          await aws.putS3Object({
            "key":req.SESSION.org_id+"/images/thumbs/"+dataToSet.image,
            "body":Buffer.from(req.body.thumb_src, 'base64'),
            "encoding":"base64",
            "contentType":req.body.content_type,
            "acl":"public-read"
          });
  
        }
  
        response.success(res);
  
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.saveBasic", e);
      response.error(res,e);
    }    
  },

/********************************************** 
   SAVE FILES
**********************************************/  
  saveFiles:async function(req,res){
    try{
      let currentItem = await mdb.client().collection(COLLECTION).findOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id});
      let dataToSave={};

      dataToSave["$set"]={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      };

      Object.keys(req.body).forEach(function(fId){
        
        if(req.ORG.data_configs.items.file_types[fId] && req.body[fId].name.trim().length>0){
          dataToSave["$set"][`files.${fId}`]={
            "name":common.safeFilename(req.body[fId].name),
            "size":((req.body[fId].size) ? req.body[fId].size : -1)
          }

          //IF there is already a file in this slot, move it to the library.
          if(currentItem.files && currentItem.files[fId] && currentItem.files[fId].name){
            if(!dataToSave["$push"]){
              dataToSave["$push"]={};
            }
            if(!dataToSave["$push"].file_archive){
              dataToSave["$push"].file_archive={"$each":[]};
            }

            dataToSave["$push"].file_archive["$each"].push({
              "name":currentItem.files[fId].name,
              "desc":`Updated by: ${req.SESSION.email}`,
              "dt":new Date()
            })
          }
        }  
      })   

      if(typeof dataToSave["$set"].files==="object" && Object.keys(dataToSave["$set"].files).length===0){
        delete dataToSave["$set"].files;
      }        

    
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},dataToSave);
      
      if(updated && updated.modifiedCount>0){

        for(let fId of Object.keys(req.body)){
          if(req.ORG.data_configs.items.file_types[fId] && req.body[fId].contents){
            await aws.putS3Object({
              "key":req.SESSION.org_id+"/files/"+common.safeFilename(req.body[fId].name),
              "body":Buffer.from(req.body[fId].contents, 'base64'),
              "encoding":"base64",
              "contentType":req.body[fId].type,
              "acl":"public-read"
            });
          }  
        };

        response.success(res);

      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.saveFiles", e);
      response.error(res,e);
    }    
  },

/********************************************** 
   DELETE FILE
**********************************************/  
  deleteFile:async function(req,res){
    try{
      let dataToSet={};
      if(req.ORG.data_configs.items.file_types[req.params.fileId]){
        dataToSet["files."+req.params.fileId]="";
      }

      let updated = await mdb.client().collection(COLLECTION).findOneAndUpdate({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$unset":dataToSet});

      if(updated && updated.value.files[req.params.fileId] && updated.value.files[req.params.fileId].name){
        let deleted = await aws.deleteS3Object({
          "key":req.SESSION.org_id+"/files/"+updated.value.files[req.params.fileId].name
        });    
        response.success(res);
      }else{
        response.fail(res, "Failed to delete file");
      }
    }catch(e){
      console.log("ERROR - items.deleteFile", e);
      response.error(res,e);
    }  
  },

/********************************************** 
    SAVE TAGS
**********************************************/  
  saveTags:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      };
  
      dataToSet.tags=req.body.tags;
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
  
        response.success(res);
  
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.saveTags", e);
      response.error(res,e);
    }
  },

/********************************************** 
  SAVE PERMISSIONS
**********************************************/  
  savePermissions:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      };
  
      dataToSet.actions=req.body.actions;
      if(typeof req.body.visible_to!=="undefined"){
        dataToSet.visible_to=req.body.visible_to;
      }
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
        response.success(res);
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.savePermissions", e);
      response.error(res,e);
    }    
  },

/********************************************** 
  SAVE FIELDS
**********************************************/  
  saveFields:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      };
    
      dataToSet.fields=req.body.fields;
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
        response.success(res);
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
        console.log("ERROR - items.saveFields", e);
        response.error(res,e);
    }  
  },

/********************************************** 
  SAVE INVENTORY
**********************************************/  
  saveInventory:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email,
        "inventory.restock_level":0,
        "inventory.oos_action":"nothing"
      };
      dataToSet["inventory.restock_level"]=req.body.inventory.restock_level;
      dataToSet["inventory.oos_action"]=req.body.inventory.oos_action;
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      if(updated && updated.modifiedCount>0){
        response.success(res);
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.saveInventory", e);
      response.error(res,e);
    }    
  },

/********************************************** 
  LINKED ITEMS
**********************************************/  
  saveLinkedItems:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email,
        "linked_items":[]
      };
    
      if(req.body.linked_items && req.body.linked_items.length>0){
        req.body.linked_items.forEach((itemId)=>{
          if(itemId && itemId.trim() && mdb.objectId(itemId.trim())){
            dataToSet.linked_items.push(mdb.objectId(itemId.trim()))
          }
        });    
      }
      
      //First Get the current list of linked items 
      let currentlyLinkedSet = await mdb.client().collection(COLLECTION).findOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id});
    
      // Get any items we need to unlink
      let unlinkSet=[];
      if(currentlyLinkedSet && currentlyLinkedSet.linked_items){
        currentlyLinkedSet.linked_items.forEach((currentlyLinked)=>{
          if(currentlyLinked && req.body.linked_items.indexOf(currentlyLinked.toString())){
            unlinkSet.push(currentlyLinked);
          }
        })  
      }
    
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
      
      
      if(updated && updated.modifiedCount>0){
        
        //Make the 2-way link
        if(req.body && req.body.linked_items){
          for(let linkedItemId of req.body.linked_items){        
            if(req.params.itemId && mdb.objectId(req.params.itemId)){
              await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(linkedItemId), org_id:req.SESSION.org_id},{"$addToSet":{"linked_items":mdb.objectId(req.params.itemId)}});      
            }          
          }
        }
          
        //Now 2-way unlink
        for(let unlink of unlinkSet){
          await mdb.client().collection(COLLECTION).updateOne({_id:unlink, org_id:req.SESSION.org_id},{"$pull":{"linked_items":mdb.objectId(req.params.itemId)}});
        }
        
        response.success(res);
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.saveLinkedItems", e);
      response.error(res,e);
    }
  },

/********************************************** 
  SAVE VARIANTS
**********************************************/ 
  saveVariants:async function(req,res){
    let dataToSet={
      "updated":new Date(),
      "updated_by":req.SESSION.email,
      "variants":[]
    };

    if(req.body.variants && req.body.variants.length>0){
      req.body.variants.forEach((itemId)=>{
        if(itemId && itemId.trim() && mdb.objectId(itemId.trim())){
          dataToSet.variants.push(mdb.objectId(itemId.trim()))
        }
      });    
    }

    let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});

    if(updated && updated.modifiedCount>0){
      response.success(res);
    }else{
      response.fail(res, "Failed to save changes.");
    }
  },

/********************************************** 
  ARCHIVE ITEM
**********************************************/ 
  archive:async function(req,res){
    try{
      let dataToSet={
        "archived":new Date(),
        "updated":new Date(),        
        "updated_by":req.SESSION.email
      }
    
      if(!req.params.itemId){
        response.missing(res, "Missing item id");
      }

      const ITEMID = mdb.objectId(req.params.itemId.trim());
      let item = await mdb.client().collection(COLLECTION).findOne({_id:ITEMID});  
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:ITEMID, org_id:req.SESSION.org_id},{"$set":dataToSet, "$unset":{"parent_id":"", "linked_items":"", "is_variant":""}});
        
      if(updated && updated.modifiedCount>0){

        // If this is a linked item, we will need to unlink the remaining items from this one.
        if(item.linked_items && item.linked_items.length>0){
          for(let linkedId of item.linked_items){
            await mdb.client().collection(COLLECTION).updateOne({_id:linkedId, org_id:req.SESSION.org_id},{"$pull":{"linked_items":ITEMID}});
          }          
        }

        // If this is a variant item, we will need to unlink it from the parent.
        if(item.parent_id){          
            await mdb.client().collection(COLLECTION).updateOne({_id:item.parent_id, org_id:req.SESSION.org_id},{"$pull":{"variants":ITEMID}});                             
        }
    
        response.success(res);
    
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to archive item.");
      }
    }catch(e){
      console.log("ERROR - items.archive", e);
      response.error(res,e);
    }  
  },

/********************************************** 
  UN-ARCHIVE ITEM
**********************************************/ 
  unarchive:async function(req,res){
    try{
      let dataToSet={
        "updated":new Date(),
        "updated_by":req.SESSION.email
      }
    
      let dataToUnSet={
        "archived":""
      }

      if(!req.params.itemId){
        response.missing(res, "Missing item id");
      }
    
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.params.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet,"$unset":dataToUnSet});
        
      if(updated && updated.modifiedCount>0){
    
        response.success(res);
    
      }else{
        console.log(new Date(), updated);
        response.fail(res, "Failed to unarchive item.");
      }
    }catch(e){
      console.log("ERROR - items.unarchive", e);
      response.error(res,e);
    }  
  },

/********************************************** 
  GENERATE IMAGE
**********************************************/ 
  generateImage:async function(req,res){
    
    try{
      
      //=== CREATE IMAGES =====================
      let imageBuffer = await pdfToImage.generate({
        "url":req.body.url,
        "size":800
      });

      let thumbBuffer = await pdfToImage.generate({
        "url":req.body.url,
        "size":400
      });

      let fileName = req.body.itemId+"_pdf.jpg"

      if(imageBuffer && thumbBuffer){
        //=== UPLOAD IMAGES TO SPACES =====================
        await aws.putS3Object({
          "key":req.SESSION.org_id+"/images/"+fileName,
          "body":Buffer.from(imageBuffer, 'base64'),
          "encoding":"base64",
          "contentType":"image/jpeg",
          "acl":"public-read"
        });

        await aws.putS3Object({
          "key":req.SESSION.org_id+"/images/thumbs/"+fileName,
          "body":Buffer.from(thumbBuffer, 'base64'),
          "encoding":"base64",
          "contentType":"image/jpeg",
          "acl":"public-read"
        });

        let dataToSet={
          "image":fileName,
          "updated":new Date(),
          "updated_by":req.SESSION.email
        };
        
        //=== UPDATE ITEM RECORD =====================
        let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.body.itemId), org_id:req.SESSION.org_id},{"$set":dataToSet});
        
        if(updated && updated.modifiedCount>0){  
          response.success(res,{"filename":fileName});
        }else{
          response.fail(res,"DB Fail");
        }

      }else{
        response.fail(res,"No buffer")
      }

      

    }catch(e){
      console.log("ERROR - items.generateImage", e);
      response.error(res,e);      
    }


  },

/********************************************** 
  COPY
**********************************************/ 
  copy:async function(req,res){
    try{
      let currentItemId = mdb.objectId(req.params.itemId.trim())
  
      let currentItem = await mdb.client().collection(COLLECTION).findOne({_id:currentItemId});
      
      if(currentItem){
        delete currentItem._id;
        delete currentItem.variants;
        currentItem.created=new Date();
        currentItem.updated=new Date();
        currentItem.updated_by=req.SESSION.email;
        currentItem.name = req.body.variantName;
        currentItem.is_variant=true;
        currentItem.parent_id = currentItemId;

        let newItem = await mdb.client().collection(COLLECTION).insertOne(currentItem);

        if(!newItem || !newItem.insertedId){
          console.log("Failed Copy",newItem);
          response.fail(res, "Failed to copy item.");
        }else{
          // Update existing 
          let updated = await mdb.client().collection(COLLECTION).updateOne({_id:currentItemId}, {"$addToSet":{"variants":newItem.insertedId}});
          console.log(currentItemId + "<>" +newItem.insertedId)
          console.log(updated);

          response.success(res, Object.assign({"_id":newItem.insertedId}, currentItem));
        }
        
  
      }else{        
        response.fail(res, "Failed to save changes.");
      }
    }catch(e){
      console.log("ERROR - items.copy", e);
      response.error(res,e);
    }
  },

/********************************************** 
  TAG SORT
**********************************************/ 
  tagSort:async function(req,res){
    try{

      if(req.body.itemIds.length>0 && req.params.tagId.trim().length>0){
        for(let i=0; i<req.body.itemIds.length; i++){
          let dataToSet={
            "updated":new Date(),
            "updated_by":req.SESSION.email,
            "tag_sort":{}
          }

          dataToSet.tag_sort[req.params.tagId.trim()]=i;
          let updated = await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(req.body.itemIds[i]), org_id:req.SESSION.org_id},{"$set":dataToSet});
        }

        response.success(res);
      }else{
        response.fail(res, "Missing required data.");
      }
              
    }catch(e){
      console.log("ERROR - items.tagsort", e);
      response.error(res,e);
    }  
  },
};
