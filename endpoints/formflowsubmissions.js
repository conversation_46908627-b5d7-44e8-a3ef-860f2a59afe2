const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const COLLECTION="formflow_submissions";
const cryptojs = require("crypto-js");
const pdf = require('pdf-lib');
const fs = require("fs");


let _get =async function(submissionId){
    let submission = await mdb.client().collection(COLLECTION).aggregate([
        {"$match":{"_id": mdb.objectId(submissionId.trim())}},
        {"$limit":1},
        { "$lookup": { "from": "formflows", "localField": "formflow_id", "foreignField": "_id", "as": "formflow" } }
      ]).toArray()

      if (submission && submission.length>0) {
        submission = submission[0];   
        submission.formflow = submission.formflow[0];   
        return submission;
      } else {
        return false;
      }
}

module.exports={



/*****************************************
    GET FLOW details
*****************************************/
    getFlow:async function(req, res){  
        try{      
            let flow = await _get(req.params.id);           
            if (flow) {
                response.success(res, flow.formflow);
            } else {
                console.log("ERROR - No Submission Found",req.params)
                response.missing(res);       
            }

        }catch(e){
            console.log("ERROR - formflowsubmissions.getConfig", e);
            response.error(res,e);
        }        
    
    },

/*****************************************
    CREATE
*****************************************/
    create:async function(req, res){
        try{
            let dataToInsert={
                "org_id":((req.SESSION.org_id) ? req.SESSION.org_id : req.ORG._id),
                "formflow_id":mdb.objectId(req.params.id),                        
                "response":{},
                "created":new Date()
            };
            if(req.SESSION.account_id){
                dataToInsert.account_id = req.SESSION.account_id;
            }

            if(req.body && typeof req.body==="object" && Object.keys(req.body).length>0){
                dataToInsert = Object.assign(Object.assign({}, req.body), dataToInsert);
            }
            
            let created = await mdb.client().collection("formflow_submissions").insertOne(dataToInsert);
            if(created && created.insertedId){
                response.success(res, Object.assign({"_id":created.insertedId}, dataToInsert));
            }else{
                response.fail(res);
            }

        }catch(e){
            console.log(e);
            response.error(res,e);
        }
    },

/*****************************************
    SUBMIT
*****************************************/
    submit:async function(req, res){  
        try{    
            let dataToSet=Object.assign({},req.body);

            let flow = await _get(req.params.id);
            if(flow){
                flow = flow.formflow;
                let processor = null;        
                                
                if(flow.processors.backend && fs.existsSync(process.cwd()+flow.processors.backend)){
                    processor = require(process.cwd()+flow.processors.backend);
                }
                
                // ==== CUSTOM BEFORE SAVE =======================================================
                if(processor){
                    if(processor.beforeSave){
                        try{
                            dataToSet = await processor.beforeSave(req, flow, dataToSet);      
                        }catch(e){
                            console.log("Before Save", e)
                        }
                    }else if(processor.beforeSet){
                        try{
                            dataToSet = await processor.beforeSet(req, flow, dataToSet);      
                        }catch(e){
                            console.log("Before Set", e)
                        }
                    }
                }

                //==== Check for fields that we need to encrypt ========================================
                Object.keys(dataToSet).forEach(function(key){                                        
                    if(flow.fields[key].encrypt){
                        try{
                            if(flow.fields[key].type==="file"){
                                dataToSet[key].content = cryptojs.AES.encrypt(dataToSet[key].content,process.env["CRYPTOAESKEY"]).toString();
                            }else{
                                dataToSet[key] = cryptojs.AES.encrypt(dataToSet[key],process.env["CRYPTOAESKEY"]).toString();
                            }
                            
                        }catch(e){
                            console.log("Encrypt Error",e);
                        }
                    }
                });                      

                //==== UPDATE UPDATE UPDATE ========================================

                let setObj={
                    "updated":new Date()
                };

                if(Object.keys(dataToSet).length>0){
                    Object.keys(dataToSet).forEach(function(k){
                        setObj[`response.${k}`] = dataToSet[k];
                    })
                }else{
                    setObj[`response`]={};
                }                
                let updated = await mdb.client().collection(COLLECTION).updateOne({"_id": mdb.objectId(req.params.id) }, {"$set":setObj})

                if(updated && updated.modifiedCount>0){

                    // ==== CUSTOM AFTER SAVE ====================================================
                    if(processor){
                        if(processor.afterSave){
                            try{
                                await processor.afterSave(req, res, dataToSet);
                            }catch(e){
                                console.log("After Save",e);
                                response.error(res,e);
                            }
                            
                        }else if(processor.afterSet){
                            try{
                                await processor.afterSet(req, res, dataToSet);
                            }catch(e){
                                console.log("After Set",e);
                                response.error(res,e);
                            }
                        }else{
                            response.success(res, dataToSet);
                        }
                    }                                        
                    
                }else{
                    response.fail(res);
                }

            }else{
                response.fail(res,"Missing flow submission");
            }                

        }catch(e){
            console.log("ERROR - formflowsubmissions.submit", e);
            response.error(res,e);
        }    
            
    },

/*****************************************
    GET SUBMISSION
*****************************************/
    get: async function(req,res){
        try{
            
            let submission = await _get(req.params.id);

            if(submission){                
                //Check to see if the user is authenticated to view response
                if(
                    (submission.account_id.toString() === req.SESSION.account_id.toString()) 
                    || (typeof submission.collaborator_ids==="object" && submission.collaborator_ids.length>0 && submission.collaborator_ids.indexOf(req.SESSION.account_id)>=0) 
                    || _.intersection(req.SESSION.tags, submission.formflow.permissions.adm).length>0
                ){
                    
                     //Check for fields that we need to DECRYPT
                    Object.keys(submission.response).forEach(function(key){
                        if(typeof submission.response[key] && submission.formflow.fields[key].encrypt){
                            if(submission.formflow.fields[key].type==="file"){                                
                            }else{                                
                                submission.response[key] = cryptojs.AES.decrypt(submission.response[key],process.env["CRYPTOAESKEY"]).toString(cryptojs.enc.Utf8);
                            }                            
                        }

                        if(submission.formflow.fields[key].type==="file"){                            
                            delete submission.response[key].content;
                        }

                    });

                }else{
                    submission.response={};
                }                               

                response.success(res, submission);

            }else{
                response.missing(res);
            }

        }catch(e){
            console.log(e);
            response.error(res,e);
            
        }        
    },

/*****************************************
    PDF
*****************************************/
    pdf:async function(req,res){

        let submission = await _get(req.params.id);
    
        //Check for fields that we need to encrypt
        Object.keys(submission.response).forEach(function(key){
            if(((typeof submission.response[key]==="string" && submission.response[key].trim().length>0) || typeof submission.response[key]!=="string")  && submission.formflow.fields[key].encrypt){
                submission.response[key] = cryptojs.AES.decrypt(submission.response[key],process.env["CRYPTOAESKEY"]).toString(cryptojs.enc.Utf8);
            }
        });

        try{
            if(submission && submission.formflow.processors.pdf){
                if(fs.existsSync(process.cwd()+submission.formflow.processors.pdf)){

                    let fileName = submission.formflow.name.trim().replace(/([^A-Za-z0-9])/ig,"")+".pdf";//+"_"+submission.response.OPERATORNAME.trim().replace(/([^A-Za-z0-9])/ig,"")
                    
                    if(typeof submission.response==="object" && Object.keys(submission.response).length>0){
                        try{
                            let pdf = await require(process.cwd()+submission.formflow.processors.pdf)(submission);                            
                            if(typeof pdf==="object"){
                                res.writeHead(200,{
                                    'Content-Type': "application/pdf",
                                    'Content-disposition': 'attachment;filename='+fileName,
                                }).end(Buffer.from(pdf, 'binary'));
                            }else{
                                res.writeHead(200,{
                                    'Content-Type': "text/plain",
                                    'Content-disposition': 'attachment;filename=error.txt',
                                }).end(Buffer.from(pdf, 'utf-8'));
                            }
                            pdf=null;
                            
                        }catch(e){
                            console.log(e);
                            response.fail(res,"Can not generate PDF. Processor error.");
                        }
                        
                    }else{
                        response.fail(res,"Can not generate PDF. Response is empty.");
                    }
                    
                    
                }else{
                    response.fail(res,"PDF processor not found. "+process.cwd()+submission.formflow.processors.pdf)
                }
            }else{
                response.fail(res,"Empty PDF processor");
            }
        }catch(e){
            console.log(e);
            //response.error(res,e)
        }

        
        /*

        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+submission.formflow.templates.pdf));
        const form = pdfDoc.getForm();

        // Get all the form fields for this form. 
        // Loop the field keys and write the response value into the matching field.
        try{
            form.getTextField('OPERATORCONTACT').setText("IT WORKS!");
        }catch(e){}
        

        //Make fileName === the formflow name + OPERATORNAME + DATETIME

        res.writeHead(200,{
            'Content-Type': "application/pdf",
            'Content-disposition': 'attachment;filename=woo.pdf',
        }).end(Buffer.from(await pdfDoc.save(), 'binary')); 
      // response.success(res);
        

       // console.log(submission);*/
        
    },

/*****************************************
    FILE
*****************************************/
    file:async function(req,res){

        let submission = await _get(req.params.id);
        let key = req.params.fieldName.trim();

        //Check for fields that we need to encrypt
        if(typeof submission.response[key]==="object" && submission.response[key].content.trim().length>0 && submission.formflow.fields[key].encrypt){
           submission.response[key].content = cryptojs.AES.decrypt(submission.response[key].content,process.env["CRYPTOAESKEY"]).toString(cryptojs.enc.Utf8);
        }

        try{

            res.writeHead(200,{
                'Content-Type': submission.response[key].type,
                'Content-disposition': 'attachment;filename='+submission.response[key].name,
            }).end(Buffer.from(submission.response[key].content, 'base64'));

        }catch(e){
            console.log(e);
            response.error(res,e)
        }

        

        
    }
}