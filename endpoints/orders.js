const mdb = require("../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require("../libs/aws.js");
const Handlebars = require("handlebars");
const activityModel = require("../models/model_activity.js");
const _ = require("lodash");
const response = require("../libs/response.js");


module.exports={

/*************************************************
  GET ALL
*************************************************/
getAll:async function(req, res){ 
  try{
    response.success(res, await mdb.client().collection("orders").find({ "org_id": req.SESSION.org_id}).sort({"created":-1}).toArray() );         
  }catch(e){
    console.log("ERROR - orders.getAll", e);
    response.error(res,e);
  }     
},

/*************************************************
  GET Mine
*************************************************/
getMine:async function(req, res){      
  try{
    response.success(res, await mdb.client().collection("orders").find({ "org_id": req.SESSION.org_id, "account_id":req.SESSION.account_id }).sort({"created":-1}).toArray() );         
  }catch(e){
    console.log("ERROR - orders.getMine", e);
    response.error(res,e);
  }  
},

/*************************************************
GET ONE
*************************************************/
getOne:async function(req, res){   
  try{
    let order = await mdb.client().collection("orders").aggregate([
      {"$match":{"_id": mdb.objectId(req.params.orderId), "org_id": req.SESSION.org_id}},
      {"$limit":1},
      { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "account" } }
    ]).toArray()
  
    if(order && order.length>0){
      order = order[0];
      if(order.account && order.account.length>0){
        order.account = order.account[0];
      }    
    }
    
    response.success(res,order);
  }catch(e){
    console.log("ERROR - orders.getOne", e);
    response.error(res,e);
  }          
},

/*************************************************
  ON CREATE HANDLER
*************************************************/
create:async function(req, res){      
    try{
      let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }); 

      if (org.server.scripts && org.server.scripts.orders && typeof org.server.scripts.orders.create==="string"){
        try{
          require(process.cwd()+"/custom_processors"+org.server.scripts.orders.create)(req,res,org);
        }catch(e){
          console.log(e);        
          response.fail(res,"order processor error");
        }
        

      }else if (typeof org.data_configs.orders.events.onCreate.script==="string"){      
          try{
            require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.onCreate.script)(req,res,org);  
          }catch(e){
            console.log(e);        
            response.fail(res,"Missing processor");
          }
          
      }else{
          require(process.cwd()+"/libs/orders.js").create(req.body, req.SESSION, org).then((orderId)=>{
            response.success(res);
          }).catch((errMsg)=>{
            console.log(errMsg); 
            response.fail(res, errMsg);
          })
          //_onCreate(req,res,org);
      }
      
      
    }catch(e){
      console.log("ERROR - orders.create", e);
      response.error(res,e);
    }          
},

/*************************************************
  UPDATE
*************************************************/
update:async function(req, res){ 
  try{
    let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }); 
  
    if (typeof org.data_configs.orders.events.onUpdate==="object" && typeof org.data_configs.orders.events.onUpdate.script && typeof org.data_configs.orders.events.onUpdate.script.length>0){      
      try{
        require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.onUpdate.script)(req,res,org)  
      }catch(e){
        res.status(500).send("Missing processor");
      }
      
    }else{

      require(process.cwd()+"/libs/orders.js").update(req.body, req.params, req.SESSION, org).then(()=>{
        response.success(res);
      }).catch((errMsg)=>{
        console.log(errMsg)
        response.fail(res, errMsg);
      });

    } 
  }catch(e){
    console.log("ERROR - orders.update", e);
    response.error(res,e);
  }     
},

/*************************************************
  CHANGE STATUS
*************************************************/
changeStatus:async function(req, res){  
  try{
    let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }); 
  
    if (typeof org.data_configs.orders.events.onStatusChange==="object" && org.data_configs.orders.events.onStatusChange.script && org.data_configs.orders.events.onStatusChange.script.length>0){      
      try{
        require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.onStatusChange.script)(req,res,org)  
      }catch(e){
        res.status(500).send("Missing processor");
      }
      
    }else{

      require(process.cwd()+"/libs/orders.js").changeStatus(req.body, req.params, req.SESSION, org).then(()=>{    
        response.success(res);
      }).catch((errMsg)=>{      
        response.fail(res, errMsg);
      })    
    }
  }catch(e){
    console.log("ERROR - orders.changeStatus", e);
    response.error(res,e);
  }            
},
  
/*************************************************
  ADD NOTE
*************************************************/
addNote:async function(req, res){  
  try{
    let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }); 
  
    if (typeof org.data_configs.orders.events.onNote==="object" && org.data_configs.orders.events.onNote.script && org.data_configs.orders.events.onNote.script.length>0){      
      try{
        require(process.cwd()+"/custom_processors"+org.data_configs.orders.events.onNote.script)(req,res,org)  
      }catch(e){
        res.status(500).send("Missing processor");
      }
      
    }else{

      require(process.cwd()+"/libs/orders.js").addNote(req.body, req.params, req.SESSION, org).then(()=>{
        response.success(res);
      }).catch((errMsg)=>{
        console.log(errMsg);
        response.fail(res, errMsg);
      });

    }
  }catch(e){
    console.log("ERROR - orders.addNote", e);
    response.error(res,e);
  }            
},

/*************************************************
  FIND
*************************************************/
find:async function(req,res){
  try{
    let dataToMatch={"org_id": req.SESSION.org_id, "deleted":{"$exists":false} }

    if(req.body && typeof req.body.criteria==="object"){
      dataToMatch=Object.assign({},req.body.criteria, dataToMatch);
    }
    
    if (typeof dataToMatch._id==="string"){
      try{
        dataToMatch._id = mdb.objectId(dataToMatch._id);
      }catch(e){
        dataToMatch._id = "NA";
      }
      
    }

    let orders = await mdb.client().collection("orders").aggregate([
      {"$match":dataToMatch},    
      { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "account" } }
    ]).toArray()

    if(orders && orders.length>0){
      orders.forEach((order, i)=>{
        if(order.account && order.account.length>0){
          orders[i].account = order.account[0];
        } 
      });       
    }
    
    response.success(res,orders);
  }catch(e){
    console.log("ERROR - orders.find", e);
    response.error(res,e);
  }  
},

/*************************************************
  SHIPSTATION
*************************************************/
shipstation:{
  createOrder:async function(req,res){
    try{
      req.params.orderId = req.params.orderId.trim();
      let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }); 
      let order = await mdb.client().collection("orders").findOne({ "_id": mdb.objectId(req.params.orderId), "org_id": req.SESSION.org_id }); 

      let orderToSend={
        "orderNumber": order._id.toString(),
        "orderDate": dayjs(order.created).format("YYYY-MM-DDTHH:mm:ss"),
        "orderStatus": "awaiting_shipment",
        "customerUsername": order.fields.EMAIL || "",
        "customerEmail": order.fields.EMAIL || "",
        "billTo": {
          "name":account.fields.FIRST_NAME+" "+account.fields.LAST_NAME,
        },
        "shipTo": {
          "name": order.fields.FNAME+" "+order.fields.LNAME,
          "street1": order.fields.ADDRESS_LINE1 || "",
          "street2": order.fields.ADDRESS_LINE2 || "",
          "city": order.fields.CITY || "",
          "state": order.fields.STATE || "",
          "postalCode": order.fields.ZIP || "",
          "country": order.fields.COUNTRY || "",
          "phone": order.fields.PHONE || ""
        },
      };
      
      
    }catch(e){
      console.log("ERROR - orders.shipstation.createOrder", e);
      response.error(res,e);
    }          
  } 
}


};
