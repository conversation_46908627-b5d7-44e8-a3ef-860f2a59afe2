const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const exportLib = require("../libs/exporter.js");
const COLLECTION="rebates";


module.exports={
/*****************************************
    GET REBATES
*****************************************/
    getActive: async function(req,res){        
        try{            
            let rebates = await mdb.client().collection(COLLECTION).find({
                "org_id":req.SESSION.org_id,
                "$and":[
                    {"$or":[
                        {"end_date":{"$exists":false}},
                        {"end_date":{"$gt":new Date()}}
                    ]},
                    {"$or":[
                        {"permissions":{"$exists":false}},
                        {"permissions":[]},
                        {"permissions":{"$in":req.SESSION.tags}},
                    ]}
                ]
                
            }).sort({"name":1}).toArray();

            response.success(res, rebates);

        }catch(e){
            console.log(e);
            response.error(res, e);
        }       
    },

/*****************************************
    RUN EXPORT
*****************************************/

    runExport:async function(req,res){
  
        let org = await mdb.client().collection("orgs").findOne({_id:req.SESSION.org_id});
    
        if(!org){
          response.fail(res);
          return false;
        }

        if (typeof org.server.scripts==="object" && org.server.scripts.rebates && org.server.scripts.rebates.exports){      
            try{
              require(process.cwd()+org.server.scripts.rebates.exports)(req,res,org);  
            }catch(e){
                console.log(e);
                response.error(res, "Missing processor");
            }         
        }else{
            response.fail(res,"Processor not defined");
        }        
    }


};