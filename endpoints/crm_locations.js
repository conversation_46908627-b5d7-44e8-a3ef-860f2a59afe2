const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={

    /*****************************************
        ADD ACCOUNTS
    *****************************************/
    add:async (req, res) => {
        try{
          let dataToInsert = req.body;
            dataToInsert.contacts=[];
            dataToInsert.reps=[];
            dataToInsert.org_id = req.ACCOUNT.org_id;
            dataToInsert.created = new Date();
            dataToInsert.created_by = req.ACCOUNT._id;
            dataToInsert.updated = new Date();
            dataToInsert.updated_by = req.ACCOUNT._id;

          let newLocationId = mdb.objectId();
          let locations = await mdb.client().collection("crm_locations").findOneAndUpdate(
            { _id: newLocationId },
            {"$setOnInsert":dataToInsert},
            {upsert:true, returnDocument: 'after'});

          if(locations && locations.ok){
            response.success(res,locations.value);
          }else{
            response.fail(res)
          }
        }catch(e){
          console.log("ERROR - crm_locations.add", e);
          response.error(res,e);
        }   
    },

    /*****************************************
        GET LOCATIONS
    *****************************************/
    getAll:async (req, res) => {       
        try{
          let locations = await mdb.client().collection("crm_locations").find({"account_id":req.params.accountId, "org_id":req.ACCOUNT.org_id, "deleted":{"$exists":false}}).toArray();
          if(locations){
           response.success(res,locations); 
          }else{
            response.fail(res)
          }
        }catch(e){
          console.log("ERROR - crm_locations.getAll", e);
          response.error(res,e);
        }        
    }

}