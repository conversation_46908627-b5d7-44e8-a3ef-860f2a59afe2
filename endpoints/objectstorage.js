const mdb = require(process.cwd()+"/libs/mdb.js");
const objectStorage = require(process.cwd()+"/libs/objectstorage.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={

/***************************************************************************
 * GET DETAILS
 ****************************************************************************/
    getDetails:async function(req, res){
        if(!req.params.ids){ response.fail(res); return; }

        let ids = req.params.ids.split(",");
        ids.forEach(function(id, i){
            ids[i] = mdb.objectId(id);
        });

        let processor = {};
        
        if(req.ORG.server.scripts.objectStorage && fs.existsSync(process.cwd()+req.ORG.server.scripts.objectStorage)){
            processor = require(process.cwd()+req.ORG.server.scripts.objectStorage);
        }

        if(typeof processor.beforeGetDetails==="function"){
            try{
                ids = await processor.beforeGetDetails(ids, req);
            }catch(e){
                console.log("objectStorage processor.beforeGetDetails", e);
                response.error(res);
                return;
            }
        }

        try{            
            let objects = await objectStorage.get({
                "ids":ids,
                "orgId":req.ORG._id
            });   
            
            console.log(objects)

            if(typeof processor.afterGetDetails==="function"){
                try{
                    objects = await processor.afterGetDetails(objects, req);
                }catch(e){
                    console.log("objectStorage processor.afterGetDetails",e);
                    response.error(res);
                    return;
                }
            }
            
            response.success(res, objects);
        }catch(e){
            console.log("objectStorage getDetails", e);
            if(e){
                response.error(res);
            }else{
                response.noData(res);
            }
            
        }
        
    },

/***************************************************************************
 * DOWNLOAD
 ****************************************************************************/
    download:async function(req,res){
        if(!req.params.id){ response.fail(res); return; }

        let processor = {};
        if(req.ORG.server.scripts.objectStorage && fs.existsSync(process.cwd()+req.ORG.server.scripts.objectStorage)){
            processor = require(process.cwd()+req.ORG.server.scripts.objectStorage);
        }

        let objectFile = await objectStorage.getOne({
            "id":req.params.id,
            "orgId":req.ORG._id,
            "includeContent":true
        });

        if(typeof processor.afterGetOne==="function"){
            try{
                objectFile = await processor.afterGetOne(objectFile, req);
            }catch(e){
                console.log("objectStorage processor.afterGetOne",e);
                response.error(res);
                return;
            }
        }

        try{
            res.writeHead(200,{
                'Content-Type': objectFile.type,
                'Content-disposition': 'attachment;filename='+objectFile.name,
            }).end(Buffer.from(objectFile.content, 'base64'));

        }catch(e){
            console.log(e);
            response.error(res,e)
        }
    },


/***************************************************************************
 * GET PUBLIC
 ****************************************************************************/
    getPublic:async function(req,res){        
        if(!req.params.id){ response.fail(res); return; }
            
        let objectFile = await objectStorage.getPublic({
            "id":req.params.id,        
            "includeContent":true
        });        

        if(objectFile){
            try{            
                res.writeHead(200,{
                    'Content-Type': objectFile.type,
                    'Content-disposition': 'inline;filename='+objectFile.name,
                }).end(Buffer.from(objectFile.content, 'base64'));

            }catch(e){
                console.log(e);
                response.error(res,e)
            }
        }else{
            response.noData(res,"Object not found.");
        }

        
    },


/***************************************************************************
 * DELETE
 ****************************************************************************/
    delete:async function(req,res){
        if(!req.params.ids){ response.fail(res); return; }
        let ids = req.params.ids.split(",");
        ids.forEach(function(id, i){
            ids[i] = mdb.objectId(id);
        });


        /*-------------------------------------------
            Custom processor beforeDelete
        ---------------------------------------------*/
        let processor = {};
        if(req.ORG.server.scripts.objectStorage && fs.existsSync(process.cwd()+req.ORG.server.scripts.objectStorage)){ processor = require(process.cwd()+req.ORG.server.scripts.objectStorage); }
        if(typeof processor.beforeDelete==="function"){
            try{
                ids = await processor.beforeDelete(ids, req);
            }catch(e){
                console.log("objectStorage processor.beforeDelete",e);
                response.error(res);
                return;
            }
        }

        if(ids.length>0){
            try{
                await objectStorage.deleteMany({
                    "ids":ids,
                    "orgId":req.ORG._id
                });
                response.success(res)
            }catch(e){
                console.log(e);
                response.error(res,e)
            }        
        }else{
            response.fail(res,"No ids to delete");
        }
    }

}