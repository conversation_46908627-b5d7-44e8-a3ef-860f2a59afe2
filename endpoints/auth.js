const mdb = require("../libs/mdb.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const randomstring = require("randomstring");
const Handlebars = require("handlebars");
const activityModel = require("../models/model_activity.js");
const dayjs = require("dayjs");

const COLLECTION = {
  "accounts":"accounts",
  "orgs":"orgs",
  "logins":"logins",
  "sessions":"sessions"
};

module.exports={

  _createAndSendOTP:async function(res, org, dataToSave){
    dataToSave.passcode = randomstring.generate(6).toUpperCase();
        
    let newLogin = await mdb.client().collection(COLLECTION.logins).insertOne(dataToSave);

    const emailBody = Handlebars.compile(org.server.templates.otp.body);
    
    aws.email({
      "to":dataToSave.email,
      "from":`${org.server.templates.otp.from} <${org.server.config.contacts.from}>`,
      "subject": org.server.templates.otp.subject,
      "body":emailBody({"OTP":dataToSave.passcode})
    }).then(r=>{   
      console.log(dataToSave.email+"Request OTP Success", r);
      response.success(res);
    }).catch(e=>{
      console.log(dataToSave.email+"Request OTP Fail", e)               
      response.error(res);
    });
  },

  /****************************************** 
    REQUEST OTP
  ******************************************/
  requestOTP:async function(req, res){
    try{
      let reqBody = ((req.body) ? req.body : {});
      let email = reqBody.email.trim().toLowerCase()

      let user = await mdb.client().collection(COLLECTION.accounts).findOne({ "org_id": mdb.objectId(reqBody.org_id), "email":email, "active": true });
      let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id": mdb.objectId(reqBody.org_id)});
      
      if(!user){
        response.missing(res,"Invalid user");
      }else{

        await mdb.client().collection(COLLECTION.logins).deleteMany({"email":email});
        
        await module.exports._createAndSendOTP(res, org, {
          "org_id":org._id,
          "account_id":user._id,
          "email":email,
          "tags":user.tags,
          "created":new Date()
        })

        activityModel.log([
          {
            "org_id":org._id,          
            "account_id":user._id,
            "event":{
              "system":"account",
              "action":"login"   
            }
          }
        ]);
        
      }
    }catch(e){
      console.log("ERROR - auth.requestOTP", e);
      response.error(res,e);
    }

    
    
  },

  /****************************************** 
    COLLABORATOR OTP
     - org_id
     - id
     - email
  ******************************************/
  collaboratorOTP:async function(req,res){
    let that = this;
    try{

      let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id": mdb.objectId(req.body.org_id)});    

      let email = req.body.email.trim().toLowerCase(),
        _id = mdb.objectId(req.body.id),
        dataToSend = {};
         
      let found=null;
      switch(req.body.type.trim().toLowerCase()){
        case "rebate":
          found = await mdb.client().collection("rebate_submissions").findOne({"_id": _id, "collaborators":email});
          dataToSend={
            "org_id":org._id,
            "rebate_submission_id":_id,
            "email":email,                    
            "created":new Date()
          }
        break;
      }

      if(found){        
        await module.exports._createAndSendOTP(res, org, dataToSend)

        activityModel.log([
          {
            "org_id":org._id,          
            "email":email,
            "event":{
              "system":"collaborator",
              "action":"otp"  
            }
          }
        ]);
      }

    }catch(e){
      console.log(e);
      response.error(res,e);
    }

  },

  /****************************************** 
    CREATE LOGIN LINK
  ******************************************/
  createLoginLink:async function(req,res){        

    let user = await mdb.client().collection(COLLECTION.accounts).findOne({ "_id": mdb.objectId(req.body.account_id), "active": true });
    let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id":req.SESSION.org_id});
      
    if(!user){
        response.missing(res,"Invalid user");
    }else{
      let newPasscode = randomstring.generate(32).toUpperCase();
      let newLogin = await mdb.client().collection(COLLECTION.logins).insertOne({
        "org_id":org._id,
        "account_id":user._id,
        "email":user.email,
        "tags":user.tags,
        "passcode":newPasscode,
        "created":dayjs().add(1,"day").toDate()
      });      
      response.success(res,newPasscode);
    }
        
  },

  /****************************************** 
    USE LOGIN LINK
  ******************************************/
    useLoginLink:async function(req,res){          
      let userLogin = await mdb.client().collection(COLLECTION.logins).findOneAndDelete({"passcode":req.body.otp,"org_id": mdb.objectId(req.body.org_id)});
        
      if(!userLogin || !userLogin.value){
        response.missing(res);
      }else if(typeof userLogin.value==="object"){
        delete userLogin.value._id;
        delete userLogin.value.passcode;
        userLogin.value.created = new Date();
        let session = await mdb.client().collection(COLLECTION.sessions).insertOne(userLogin.value);
        
        if(!session || !session.insertedId){
          response.error(res,"Failed to create session");
        }else{
          response.success(res,{
            "id":session.insertedId.toString(),
            "email":userLogin.value.email,
            "tags":userLogin.value.tags,
            "created":userLogin.value.created.toString()
          });          
        }
        
      }else{
        console.log("AUTH VERIFY OTP", reqBody, userLogin)
        response.missing(res);
      }
      
      
    },

  /****************************************** 
    VERIFY OTP
  ******************************************/
  verifyOTP:async function(req,res){
    try{

      let reqBody = ((req.body) ? req.body : {});
      const email = reqBody.email.trim().toLowerCase();
      const otp = reqBody.otp.trim().toUpperCase();

      let userLogin = await mdb.client().collection(COLLECTION.logins).findOneAndDelete({"email":email, "passcode":otp});
    
      if(!userLogin || !userLogin.value){
        response.missing(res);
      }else if(typeof userLogin.value==="object"){
        delete userLogin.value._id;
        delete userLogin.value.passcode;
        userLogin.value.created = new Date();
        let session = await mdb.client().collection(COLLECTION.sessions).insertOne(userLogin.value);
        
        if(!session || !session.insertedId){
          response.error(res,"Failed to create session");
        }else{
          response.success(res,{
            "id":session.insertedId.toString(),
            "email":userLogin.value.email,
            "tags":((userLogin.value.tags) ? userLogin.value.tags : null),
            "created":userLogin.value.created.toString()
          });
          
        }
        
      }else{
        console.log("AUTH VERIFY OTP", reqBody, userLogin)
        response.missing(res);
      }
      
    }catch(e){
      console.log("ERROR - auth.verifyOTP",e);
      response.error(res);
    }
    
      
  },

  /****************************************** 
   GET SESSION
  ******************************************/
  getSession:async function(req,res){
    try{
      if(!req.headers["x-session-id"]){
        response.fail(res);
      }else{
        let session = await mdb.client().collection(COLLECTION.sessions).findOne({"_id":mdb.objectId(req.headers["x-session-id"].trim())});      
        if(session){
          await mdb.client().collection(COLLECTION.accounts).updateOne({_id:session.account_id},{"$set":{"last_activity":new Date()}, "$unset":{"warn":""}});
          delete session.account_id;
          response.success(res, session);
        }else{
          response.fail(res);
        }
      }
    }catch(e){
      console.log("ERROR - auth.getSession", e);
      response.error(res,e);
    }
    
  },

  /****************************************** 
   DELETE SESSION
  ******************************************/
  deleteSession:async function(req,res){
    try{
      if(!req.headers["x-session-id"]){
        response.fail(res);
      }else{
        let session = await mdb.client().collection(COLLECTION.sessions).deleteOne({"_id":mdb.objectId(req.headers["x-session-id"].trim())});      
        response.success(res, session);
      }
    }catch(e){
      console.log("ERROR - auth.deleteSession", e);
      response.error(res,e);
    }
    
  },

  /****************************************** 
   CHECK EMAIL
  ******************************************/
  checkEmail:async function(req,res){
    try{
      let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id": mdb.objectId(req.body.org_id)});
      let account = await mdb.client().collection(COLLECTION.accounts).findOne({"org_id":org._id, "email":req.body.email.trim().toLowerCase()});
      if(account){
        response.success(res);
      }else{
        response.missing(res);
      }
    }catch(e){
      console.log("ERROR - auth.checkEmail", e);
      response.error(res,e);
    }
    
  },

  /****************************************** 
    REGISTER
  ******************************************/
   register:async function(req,res){
    try{
      let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id": mdb.objectId(req.body.org_id)});    
      let dataToSave=Object.assign({},req.body,{
        "org_id":org._id,
        "favorites":[],
        "outbox":[],
        "tags":[],
        "cart":{},
        "active":false,
        "pending":true,
        "created":new Date(),
        "last_activity":new Date()      
      });
      
      dataToSave.email  = dataToSave.email.trim().toLowerCase();

      let newAccount = await mdb.client().collection(COLLECTION.accounts).insertOne(dataToSave);

      if(newAccount && newAccount.insertedId){
        response.success(res);
      
        aws.email({          
          "to":((process.env["ISDEV"]) ? "<EMAIL>" : org.server.config.contacts.account_notification ),
          "from":`${org.server.templates.register.admin.from} <${org.server.config.contacts.from}>`,
          "subject": org.server.templates.register.admin.subject,
          "body":Handlebars.compile(org.server.templates.register.admin.body)({"account":dataToSave})
        }).then(r=>{                
        }).catch(e=>{        
          response.error(res);
        });

      }else{
        response.missing(res);
      }
    }catch(e){
      console.log("ERROR - auth.register", e);
      response.error(res,e);
    }
  },

  /****************************************** 
    ACKNOWLEDGED
  ******************************************/
  acknowledged:async function(req,res){
    try{
      let org = await mdb.client().collection(COLLECTION.orgs).findOne({ "_id": mdb.objectId(req.body.org_id)});
      let updated = await mdb.client().collection(COLLECTION.accounts).updateOne({"_id":mdb.objectId(req.params.accountId), "org_id":org._id}, {"$unset":{"warn":""}, "$set":{"last_activity":new Date()}});
      
      if(updated && updated.matchedCount>0){
        response.success(res);
      }else{
        response.missing(res);
      }
    }catch(e){
      console.log("ERROR - auth.acknowledged", e);
      response.error(res,e);
    }
  }

}