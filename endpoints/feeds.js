const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={
    getOrg:async (req, res, next)=>{
        let hostname="";

        try{
            hostname = new URL((req.headers.referer || req.headers.referrer)).hostname;
        }catch(e){
            if(req.body["url"] || req.query["url"]){
                hostname = (req.body["url"]) ? req.body["url"].trim().toLowerCase() : req.query["url"].trim().toLowerCase();
            }
        }                
                 
        console.log(hostname);
        console.log((req.headers.referer || req.headers.referrer));

        if(hostname.length>0){
            let org = await mdb.client().collection("orgs").findOne({"url":hostname});
            if(org){
                req.ORG = org;
                next();
            }else{
                response.fail(res, "org not found");
            }
        }else{
            response.fail(res, "missing url");
        }
    },

    processFeed:async (req, res) => {
        if (typeof req.ORG.server.scripts.feeds==="object" && typeof req.ORG.server.scripts.feeds[req.params.feedKey]==="string" && req.ORG.server.scripts.feeds[req.params.feedKey].length>0){      
      
            try{
                require(process.cwd()+req.ORG.server.scripts.feeds[req.params.feedKey])(req,res)  
            }catch(e){
                res.status(500).send("Failed processor");
            }

        }else{
            res.status(500).send("Missing processor");
        }
    }

}