const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={
    getOrg:async (req, res, next)=>{
        if(req.body["url"] || req.query["url"]){            
            let url = (req.body["url"]) ? req.body["url"].trim().toLowerCase() : req.query["url"].trim().toLowerCase();
            let org = await mdb.client().collection("orgs").findOne({"url":url});
            if(org){
                req.ORG = org;
                next();
            }else{
                response.fail(res, "invalid url request");
            }
        }else{
            response.fail(res, "missing url");
        }        
    },

    processFeed:async (req, res) => {
        if (typeof req.ORG.server.scripts.feeds==="object" && typeof req.ORG.server.scripts.feeds[req.params.feedKey]==="string" && req.ORG.server.scripts.feeds[req.params.feedKey].length>0){      
      
            try{
                require(process.cwd()+"/custom_processors"+req.ORG.server.scripts.feeds[req.params.feedKey])(req,res)  
            }catch(e){
                res.status(500).send("Failed processor");
            }

        }else{
            res.status(500).send("Missing processor");
        }
    }

}