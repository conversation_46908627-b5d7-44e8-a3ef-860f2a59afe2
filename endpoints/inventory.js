const mdb = require(process.cwd()+"/libs/mdb.js");
const dayjs = require("dayjs");
const response = require(process.cwd()+"/libs/response.js");

module.exports={
    adjust:async (req, res)=>{
        try {            
            //let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id }, {"projection":{"server":true}});
            
            if(!req.params.itemId){
                response.missing(res, "Missing item id");
                return;
            }
            if(req.body.qty==0){
                response.missing(res, "Quantity can not be zero");
                return;
            }

            let dataToSave = {                
                "account": {
                    "_id": req.SESSION.account_id,
                    "email": req.SESSION.email
                },
                "item_id": mdb.objectId(req.params.itemId),
                "org_id": req.SESSION.org_id,
                "qty": req.body.qty,
                "details": req.body.details,
                "ts": new Date()
            };

            let newRecord = await mdb.client().collection("inventory").insertOne(dataToSave);
            
            if(newRecord && newRecord.insertedId){                            
                response.success(res,Object.assign({"_id":newRecord.insertedId}, dataToSave));
            } else {
                response.fail(res, "Failed to insert record.");                
            }
        } catch (e) {
            console.error("ERROR - inventory.adjust", e);
            response.error(res, e);            
        }
    },
}