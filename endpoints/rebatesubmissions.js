const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const COLLECTION="rebate_submissions";
const cryptojs = require("crypto-js");
const pdf = require('pdf-lib');
const fs = require("fs");

let _get =async function(submissionId, matchCriteria){    
    let agg=[
        {"$match":Object.assign({"_id": mdb.objectId(submissionId.trim())},((typeof matchCriteria==="object" && matchCriteria.length>0) ? matchCriteria[0] : {}))},
        {"$limit":1},
        { "$lookup": { "from": "formflows", "localField": "formflows", "foreignField": "_id", "as": "formflows" } },
        { "$lookup": { "from": "formflow_submissions", "localField": "formflow_submissions", "foreignField": "_id", "as": "formflow_submissions" } },
        { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
        { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
        { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } },
      ];
    if(typeof matchCriteria==="object" && matchCriteria.length>1){
        agg.push({
            "$match":matchCriteria[1]
        })
    }
    let submission = await mdb.client().collection(COLLECTION).aggregate(agg).toArray()
    
      if (submission && submission.length>0) {
        submission=submission[0];
        submission.rebate = submission.rebate[0];
        submission.operator = submission.operator[0];
        submission.broker = submission.broker[0];
        delete submission.broker.cart;
        delete submission.broker.favorites;
        delete submission.broker.outbox;
        return submission;
      } else {
        return false;
      }
}

module.exports={
/*****************************************
    GET SUBMISSION 
*****************************************/
    getOne: async function(req,res){
        try{
            
            let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id });

            if (typeof org.server.scripts==="object" && org.server.scripts.rebateSubmissions && org.server.scripts.rebateSubmissions.getOne){
                try{
                    require(process.cwd()+org.server.scripts.rebateSubmissions.getOne)(req,res,org, _get);  
                  }catch(e){
                      response.error(res, "Missing getOne processor");
                  }
            }else{                

                let rebateSubmission = await _get(req.params.id);

                if(rebateSubmission){ 
                    //Check to see if the user is authenticated to view response
                    if(
                        (req.SESSION.account_id && rebateSubmission.account_id.toString() === req.SESSION.account_id.toString()) 
                        || (typeof rebateSubmission.collaborators==="object" && rebateSubmission.collaborators.length>0 && rebateSubmission.collaborators.indexOf(req.SESSION.email)>=0)                     
                    ){
                        
                        // Now loop all the formSubmissions
                        rebateSubmission.formflow_submissions.forEach(function(formFlowSubmission, i){

                            //Check for fields that we need to encrypt
                            Object.keys(formFlowSubmission.response).forEach(function(key){

                                let formFlow = _.find(rebateSubmission.formflows,{"_id":formFlowSubmission.formflow_id});

                                if(
                                    typeof formFlowSubmission.response[key]!=="undefined" && 
                                        (
                                            (typeof formFlowSubmission.response[key]==="string" && formFlowSubmission.response[key].trim().length>0) 
                                            || 
                                            typeof formFlowSubmission.response[key]!=="string"
                                        )  
                                        && formFlow.fields[key].encrypt
                                        && formFlow.fields[key].type!=="file"){
                                    rebateSubmission.formflow_submissions[i].response[key] = cryptojs.AES.decrypt(formFlowSubmission.response[key],process.env["CRYPTOAESKEY"]).toString(cryptojs.enc.Utf8);
                                }

                                if(formFlow.fields[key].type==='file'){
                                // delete rebateSubmission.formflow_submissions[i].response[key].content;
                                }

                            });

                        });
                                            

                        if(
                            !req.SESSION.account_id
                        ){
                            delete rebateSubmission.notes;
                            rebateSubmission.broker={
                                "email":rebateSubmission.broker.email,                            
                            };

                        }
                    }else{                        
                        rebateSubmission.response={};
                    }                               

                    response.success(res, rebateSubmission);

                }else{
                    response.missing(res);
                }

            }

            

        }catch(e){
            console.log(e);
            response.error(res,e);
            
        }        
    },

/*****************************************
    GET MINE SUBMISSIONS
*****************************************/
    list: async function(req,res){
        
        let org = await mdb.client().collection("orgs").findOne({ "_id": req.SESSION.org_id });

        if (typeof org.server.scripts==="object" && org.server.scripts.rebateSubmissions && org.server.scripts.rebateSubmissions.list){      
            try{
              require(process.cwd()+org.server.scripts.rebateSubmissions.list)(req,res,org);  
            }catch(e){
                response.error(res, "Missing processor");
            }            
        }else{
            try{                
                let submissions = await mdb.client().collection(COLLECTION).aggregate([
                    {"$match":{
                        "org_id":req.SESSION.org_id,
                        "account_id":req.SESSION.account_id
                    }},        
                    { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
                    { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
                    { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } }
                  ]).toArray()
                                    
                  if (submissions) {                    
                    submissions.forEach(function(submission, i){
                        submissions[i].rebate = submissions[i].rebate[0];
                        submissions[i].operator = submissions[i].operator[0];
                        submissions[i].broker = submissions[i].broker[0];
                    });                                        
                  }

                  response.success(res, submissions);
                
            }catch(e){
                console.log(e);
                response.error(res, e);
            }            
        }   
    },

/*****************************************
    CREATE SUBMISSION
*****************************************/
    create: async function(req,res){

        // First look up the rebate campaign to get the details.    
        try{ 
            
            let rebate = await mdb.client().collection("rebates").findOne({
                "_id":mdb.objectId(req.body.rebate_id),
                "org_id":req.SESSION.org_id
            });

            if(rebate){

                let rebateSubmissionId = mdb.objectId();
                let dataToInsert={
                    "_id":rebateSubmissionId,
                    "org_id":req.SESSION.org_id,
                    "account_id":req.SESSION.account_id,
                    "rebate_id":mdb.objectId(req.body.rebate_id),
                    "collaborators":[],
                    "formflows":[],
                    "formflow_submissions":[],
                    "checklist":{},
                    "status":"Created",
                    "created":new Date(),
                    "updated":new Date()
                };

                //First Create formflow submission 
                for(let formFlow of rebate.formflows){                

                    let formFlowSubmission = await mdb.client().collection("formflow_submissions").insertOne({
                        "org_id":req.SESSION.org_id,
                        "account_id":req.SESSION.account_id,
                        "rebate_id":mdb.objectId(req.body.rebate_id),
                        "rebatesubmission_id":rebateSubmissionId,
                        "formflow_id":formFlow.id,                        
                        "response":{},
                        "created":new Date()
                    });

                    dataToInsert.formflow_submissions.push(formFlowSubmission.insertedId);
                }
                
    
                rebate.formflows.forEach(function(formFlow){
                    dataToInsert.formflows.push(formFlow.id)
                });
                rebate.checklist.items.forEach(function(checklistItem){
                    dataToInsert.checklist[checklistItem.id]=false
                });

                let newRebateSubmission = await mdb.client().collection(COLLECTION).insertOne(dataToInsert);
    
                if(newRebateSubmission && newRebateSubmission.insertedId){
                    response.success(res, newRebateSubmission.insertedId);
                }else{
                    console.log(newRebateSubmission);
                    response.error(res,"Failed to insert");
                }
                
            }

            

        }catch(e){
            console.log(e);
            response.error(res, e);
        }
    },

/*****************************************
    ADD COLLABORATORS
*****************************************/
    addCollaborators: async function(req,res){

        try{
            if(req.params.rebateSubmissionId && req.body.emails && req.body.emails.length>0){
                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, 
                    {
                        "$addToSet":{"collaborators":{"$each":req.body.emails}},
                        "$push":{"logs":{
                            "d":new Date(),
                            "e":req.SESSION.email,
                            "m":"Added collaborator: "+req.body.emails.join(",")
                        }}
                    }
                );
            }                
            response.success(res);
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    DELETE COLLABORATORS
*****************************************/
    deleteCollaborator: async function(req,res){
        try{
            if(req.params.rebateSubmissionId && req.body.email && req.body.email.length>0){
                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {
                    "$pull":{"collaborators":req.body.email},
                    "$push":{"logs":{
                        "d":new Date(),
                        "e":req.SESSION.email,
                        "m":"Removed collaborator: "+req.body.email
                    }}
                });
                await mdb.client().collection("sessions").deleteMany({"email":req.body.email,"account_id":{"#exists":false}});
                response.success(res);
            }else{
                response.fail(res);
            }                
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    ADD NOTE
*****************************************/
    addNote: async function(req,res){

        try{
            if(req.params.rebateSubmissionId && req.body.note && req.body.note.length>0){
                let dataToSave={
                    "account_id":req.SESSION.account_id,
                    "email":req.SESSION.email,
                    "ts":new Date(),
                    "msg":req.body.note
                };
                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {"$addToSet":{"notes":dataToSave}});
                response.success(res,dataToSave);
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    UPDATE STATUS
*****************************************/
    updateStatus: async function(req,res){

            try{
                if(req.params.rebateSubmissionId && req.body.status && req.body.status.length>0){                    
                    let dataToSet={
                        "status":req.body.status,
                        "updated":new Date(),
                        "updated_by":req.SESSION.email
                    };

                    await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {"$set":dataToSet});
                    response.success(res,dataToSet);
                }else{
                    response.fail(res);
                }
                
            }catch(e){
                console.log(e);
                response.fail(res);
            }
    },

/*****************************************
    UPDATE CHECKLIST
*****************************************/
    updateChecklist: async function(req,res){

        try{            
            if(req.params.rebateSubmissionId && req.body.checklist && Object.keys(req.body.checklist).length>0){                    

                let submission = await mdb.client().collection("rebate_submissions").aggregate([
                    {"$match":{"_id": mdb.objectId(req.params.rebateSubmissionId.trim())}},
                    {"$limit":1},                    
                    { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },                    
                  ]).toArray();

                  if (submission) {
                    submission=submission[0];
                    submission.rebate = submission.rebate[0];                   
                  }

                let dataToSet={                    
                    "updated":new Date(),
                    "status_updated":new Date(),
                    "updated_by":req.SESSION.email                    
                };

                if(req.body.status){ dataToSet.status=req.body.status; }

                let log=[];
                Object.keys(req.body.checklist).forEach(function(itemId){
                    dataToSet[`checklist.${itemId}`]=req.body.checklist[itemId];
                    log.push(itemId+":"+((req.body.checklist[itemId]) ? "Checked" : "Unchecked"))
                });

                let updated = await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {
                    "$set":dataToSet,
                    "$push":{"logs":{
                        "d":new Date(),
                        "e":req.SESSION.email,
                        "m":"Updated checklist: <code>"+log.join(", ")+"</code>"
                    }}
                });
                
                if (typeof submission.rebate==="object" && typeof submission.rebate.processor==="object" && submission.rebate.processor.updateChecklist){      
                    try{
                        require(process.cwd()+submission.rebate.processor.updateChecklist)(req,res);  
                    }catch(e){
                        response.error(res, "Missing processor");
                    }            
                }else{
                    response.success(res,updated.modifiedCount);
                }
                
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    UPDATE BROKER
*****************************************/
    updateBroker: async function(req,res){

        try{
            if(req.params.rebateSubmissionId && req.body.broker && req.body.broker._id.length>0){                    
                let dataToSet={
                    "account_id": mdb.objectId(req.body.broker._id.trim()),
                    "updated":new Date(),
                    "updated_by":req.SESSION.email
                };
                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {
                    "$set":dataToSet,
                    "$push":{"logs":{
                        "d":new Date(),
                        "e":req.SESSION.email,
                        "m":"Updated owner account to: <code>"+req.body.broker.email+"</code>"
                    }}
                });
                response.success(res, dataToSet);
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    UPDATE CO-OWNER
*****************************************/
    updateCoOwner: async function(req,res){

        try{
            if(req.params.rebateSubmissionId && req.body.account && req.body.account._id.length>0){                    
                let dataToSet={
                    "coowner_id": mdb.objectId(req.body.account._id.trim()),
                    "updated":new Date(),
                    "updated_by":req.SESSION.email
                };
                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {
                    "$set":dataToSet,
                    "$push":{"logs":{
                        "d":new Date(),
                        "e":req.SESSION.email,
                        "m":"Updated co-owner account to: <code>"+req.body.account.email+"</code>"
                    }}
                });
                response.success(res, dataToSet);
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    DELETE REQUEST
*****************************************/
    delete: async function(req,res){
        let rebateSubmission = await mdb.client().collection("rebate_submissions").findOne({"_id":mdb.objectId(req.params.rebateSubmissionId.trim())});
        if(rebateSubmission){
            await mdb.client().collection("formflow_submissions").deleteMany({"_id":{"$in":rebateSubmission.formflow_submissions}});
            await mdb.client().collection("rebate_submissions").deleteOne({"_id":rebateSubmission._id});
            response.success(res);
        }else{
            response.fail(res);
        }

        
    },
    
/*****************************************
    LOG EVENT
*****************************************/
    log: async function(req,res){
        if(req.body.details){
            let dataToSave={
                "d":new Date(),
                "e":req.SESSION.email,
                "m":req.body.details.trim()
            };
            await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, 
                {                
                    "$push":{"logs":dataToSave}
                }
            );

            response.success(res,dataToSave);
        }else{
            response.missing(res,"Missing details");
        }   
    },

/*****************************************
    LOGS
*****************************************/
    getLogs: async function(req,res){
        
        let rebateSubmission = await mdb.client().collection("rebate_submissions").findOne({"_id":mdb.objectId(req.params.rebateSubmissionId)});
        if(rebateSubmission && rebateSubmission.logs){
            response.success(res,rebateSubmission.logs);
        }else{
            response.success(res,[]);
        }
           
    },

/*****************************************
    META FIELDS
*****************************************/
    metaFields:async function(req,res){
        try{
            if(req.params.rebateSubmissionId && Object.keys(req.body).length>0){                    
                let dataToSet={                                        
                    "updated":new Date(),
                    "updated_by":req.SESSION.email,
                    "meta":req.body
                };            

                await mdb.client().collection("rebate_submissions").updateOne({"_id":mdb.objectId(req.params.rebateSubmissionId)}, {
                    "$set":dataToSet,
                    "$push":{"logs":{
                        "d":new Date(),
                        "e":req.SESSION.email,
                        "m":"Updated meta fields: <code>"+JSON.stringify(req.body)+"</code>"
                    }}
                });
                response.success(res, req.body);
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    },

/*****************************************
    CLONE
*****************************************/
    clone:async function(req,res){        
        try{
            if(req.params.rebateSubmissionId){                    
                
                let customProcessor=null;
                let rebateSubmission = await mdb.client().collection("rebate_submissions").findOne({"_id":mdb.objectId(req.params.rebateSubmissionId)});
                let rebate = await mdb.client().collection("rebates").findOne({"_id":rebateSubmission.rebate_id});

                if(rebate.processor.backend && fs.existsSync(process.cwd()+rebate.processor.backend)){                    
                    customProcessor = require(process.cwd()+rebate.processor.backend)(_get);                    
                }

                /*------------------------------------------------------------------------
                Get all the form flow submissons ids we need to clone. If none are passed in, clone all of them. 
                -------------------------------------------------------------------------*/
                let formFlowSubmissionsToClone = rebateSubmission.formflow_submissions;
                if(typeof req.body.formFlowSubmissions==="object"){
                    formFlowSubmissionsToClone = [];
                    req.body.formFlowSubmissions.forEach(function(ffsId){
                        if(ffsId){
                            formFlowSubmissionsToClone.push(mdb.objectId(ffsId.trim()))
                        }                        
                    })
                }             

                /*------------------------------------------------------------------------
                    Find the leftover formflow submissions that were not passed in to be cloned
                    Needed to cast them to string in order to use the _.difference function. 
                    Then had to cast them back to _id objects
                -------------------------------------------------------------------------*/
                rebateSubmission.formflow_submissions.forEach(function(r,i){
                    rebateSubmission.formflow_submissions[i]=rebateSubmission.formflow_submissions[i].toString();
                });
                formFlowSubmissionsToClone.forEach(function(r,i){
                    formFlowSubmissionsToClone[i]=formFlowSubmissionsToClone[i].toString();
                });        
                let formFlowSubmissionsToReset = _.difference(rebateSubmission.formflow_submissions, formFlowSubmissionsToClone);
                formFlowSubmissionsToClone.forEach(function(r,i){
                    formFlowSubmissionsToClone[i]=mdb.objectId(formFlowSubmissionsToClone[i]);
                });
                formFlowSubmissionsToReset.forEach(function(r,i){
                    formFlowSubmissionsToReset[i]=mdb.objectId(formFlowSubmissionsToReset[i]);
                });

                let formFlowSubmissionsDocuments={"clone":null, "reset":null}
                let newFormFlowSubmissionIds=[];

                /*------------------------------------------------------------------------
                    Create a new rebate submission id
                -------------------------------------------------------------------------*/
                let newRebateSubmissionId=mdb.objectId();
                
                /*------------------------------------------------------------------------
                    Get the formflow submission records to be used later when cloning
                -------------------------------------------------------------------------*/
                formFlowSubmissionsDocuments.clone = await mdb.client().collection("formflow_submissions").find({"_id":{"$in":formFlowSubmissionsToClone}}).toArray();
                if(formFlowSubmissionsToReset.length>0){
                    formFlowSubmissionsDocuments.reset = await mdb.client().collection("formflow_submissions").find({"_id":{"$in":formFlowSubmissionsToReset}}).toArray();
                }
                
                
                /*------------------------------------------------------------------------

                    Now that we have the data. Lets start creating new records
                    #1 -- Create rebatesubmission _id
                    #2 -- Create new formflow_submission records and keep track of their IDs
                    #3 -- Use those IDs when creating a rebate_submission
                -------------------------------------------------------------------------*/


                /*------------------------------------------------------------------------
                    Create the CLONED formflow submission documents
                -------------------------------------------------------------------------*/
                if(formFlowSubmissionsDocuments.clone && formFlowSubmissionsDocuments.clone.length>0){
                    for (let documentToClone of formFlowSubmissionsDocuments.clone){
                        documentToClone._id = mdb.objectId();
                        newFormFlowSubmissionIds.push(documentToClone._id);
                        documentToClone.created=new Date();
                        documentToClone.rebatesubmission_id=newRebateSubmissionId;                        
                        await await mdb.client().collection("formflow_submissions").insertOne(documentToClone);                        
                    }
                }

                /*------------------------------------------------------------------------
                    Create the RESET formflow submission documents
                -------------------------------------------------------------------------*/
                if(formFlowSubmissionsDocuments.reset && formFlowSubmissionsDocuments.reset.length>0){
                    for (let documentToClone of formFlowSubmissionsDocuments.reset){
                        documentToClone._id = mdb.objectId();
                        newFormFlowSubmissionIds.push(documentToClone._id);
                        documentToClone.response={};
                        documentToClone.created=new Date();
                        documentToClone.rebatesubmission_id=newRebateSubmissionId;                        
                        await await mdb.client().collection("formflow_submissions").insertOne(documentToClone);                        
                    }
                }

                /*------------------------------------------------------------------------
                    Create the rebate submission record
                -------------------------------------------------------------------------*/
                let newRebateSubmissionDocument = Object.assign({}, rebateSubmission);
                
                newRebateSubmissionDocument._id = newRebateSubmissionId;
                newRebateSubmissionDocument.formflow_submissions = newFormFlowSubmissionIds;
                            
                Object.keys(newRebateSubmissionDocument.checklist).forEach(function(item){
                    newRebateSubmissionDocument.checklist[item] = false;
                });

                newRebateSubmissionDocument.status = "Created";
                newRebateSubmissionDocument.created = new Date();
                newRebateSubmissionDocument.updated = new Date();
                delete newRebateSubmissionDocument.status_updated;
                delete newRebateSubmissionDocument.updated_by;
                newRebateSubmissionDocument.logs=[
                    {
                        "d" : new Date(),
                        "e" : req.SESSION.email,
                        "m" : "Created via cloning: "+req.params.rebateSubmissionId
                    }
                ];
                newRebateSubmissionDocument.closed=false;
                newRebateSubmissionDocument.meta={};
                newRebateSubmissionDocument.notes=[];
                
                let newRebateSubmissionCreated = await mdb.client().collection("rebate_submissions").insertOne(newRebateSubmissionDocument);
                if(newRebateSubmissionCreated && newRebateSubmissionCreated.insertedId){
                   
                    if(customProcessor && typeof customProcessor.afterClone==="function"){                        
                        await customProcessor.afterClone(req, newRebateSubmissionDocument);
                    }

                    response.success(res, newRebateSubmissionId);
                }else{
                    response.fail(res);
                }
                
            }else{
                response.fail(res);
            }
            
        }catch(e){
            console.log(e);
            response.fail(res);
        }
    }
};