const mdb = require("../libs/mdb.js");
const common = require("../libs/common.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");


async function __verifyPlayer(req){
    try{
    
        req.params.playerId = req.params.playerId.trim().toLowerCase();
        req.params.contestKey = req.params.contestKey.trim().toLowerCase();

        let contest = await mdb.client().collection("contests").findOne({"key":req.params.contestKey});

        if(!contest){
            return [false,"Invalid contest"];                    
        }

        if(req.params.playerId==="test" || !contest.config.player || !contest.config.player.account_required){
            return [true,null];
            
        }else{
            
            let playerDataToFind={ "active":true };
            playerDataToFind[contest.config.player.id_field]=req.params.playerId;
            let contestPlayer = await mdb.client().collection("contest_players").findOne(playerDataToFind);

            if(!contestPlayer){                
                return [false,"Active player not found"];                
            }
            
            let contestPlays = await mdb.client().collection("contest_plays").find({"player":req.params.playerId}).toArray();
            return [true, {"player":contestPlayer,"plays":contestPlays}]; 
        }

    }catch(e){
        console.log("HUH");
        return [false,null]
    }
}

module.exports={
    /*****************************************
        VERIFY PLAYER
    *****************************************/
        verifyPlayer: async function(req,res){
            try{        
                
               let verifiedPlayer = await __verifyPlayer(req);
               if(verifiedPlayer && verifiedPlayer[0]){
                response.success(res,verifiedPlayer[1]);
               }else{
                response.fail(res,verifiedPlayer[1]);
               }                                                                
            }catch(e){
                console.log(e);
                response.error(res, e);
            }       
        },

    /*****************************************
        PLAY
    *****************************************/
        play: async function(req,res){
            try{        
                let verifiedPlayer = await __verifyPlayer(req);
                
                
                if(verifiedPlayer && verifiedPlayer[0]){
                    let contest = await mdb.client().collection("contests").findOne({"key":req.params.contestKey.trim().toLowerCase()});

                    if(!contest){
                        response.fail(res,"Invalid contest");
                        return false;
                    }

                    if(contest.config.processors && contest.config.processors.play){
                        try{
                            require(process.cwd()+"/custom_processors"+contest.config.processors.play)(req,res, contest);  
                        }catch(e){
                            response.error(res, "Missing processor");
                        }  
                    }
                }else{                                    
                    response.fail(res, verifiedPlayer[1]);
                }
                

            }catch(e){
                console.log(e);
                response.error(res, e);
            } 
        }
}    