const mdb = require("../libs/mdb.js");
const response = require("../libs/response.js");
const aws = require("../libs/aws.js");
const links = require("../libs/links.js");
const common = require("../libs/common.js");
const Handlebars = require("handlebars");
const activityModel = require("../models/model_activity.js");
const COLLECTION = "accounts";
const ORGS_COLLECTION = "orgs";


/*...........................................
  CLEAR OUTBOX
............................................*/
let __clearOutbox = async function(userId){
  let updated = await mdb.client().collection(COLLECTION).updateOne({_id:userId},{"$set":{"outbox":[]}});
    if(updated && updated.modifiedCount>0){
      return true; 
    }else{
      return false;
    }    
}



module.exports={

/*****************************************
  CREATE
*****************************************/
  create:async(req,res)=>{
    try{
      let dataToSave={
        "org_id":req.SESSION.org_id,
        "email":req.body.email.trim().toLowerCase(),
        "fields":{},
        "favorites":[],
        "outbox":[],
        "tags":[],
        "cart":{},
        "active":false,      
        "created":new Date()      
      };
      
      let newAccount = await mdb.client().collection(COLLECTION).insertOne(dataToSave);
      if(!newAccount || !newAccount.insertedId){
        response.error(res,"Failed to create account.");
      }else{
        response.success(res, newAccount.insertedId);
      }
    }catch(e){
      console.log("ERROR - account.create", e);
      response.error(res,e);
    }
    
  },


  /*****************************************
    SEND WELCOME EMAIL
  *****************************************/
    __sendWelcomeEmail : async(accountId, ORG)=>{
      let account = await mdb.client().collection(COLLECTION).findOne({_id:mdb.objectId(accountId)});
        if(account){
          
          let emailBody = Handlebars.compile(ORG.server.templates.register.welcome.body)( Object.assign({"EMAIL":account.email}, account.fields));
            
          try{
                    
            await aws.email({
              "to":[account.email],
              "from": `${ORG.server.templates.register.welcome.from} <${ORG.server.config.contacts.from}>`,          
              "subject": ORG.server.templates.register.welcome.subject.trim(),
              "body":emailBody
            });
    
            await mdb.client().collection(COLLECTION).updateOne({_id:mdb.objectId(accountId)},{"$set":{"welcomed": new Date()}});
    
            return true;
          }catch(e){
            console.log(e);
            return false;
          }
      
        }else{
          return false;
        }
    },
  sendWelcomeEmail:async(req,res)=>{
    if(typeof req.body.account_id!=="undefined" && typeof req.ORG.server.templates.register.welcome==="object"){           
      if( module.exports.__sendWelcomeEmail(req.body.account_id.trim(), req.ORG)){
        response.success(res);
      }else{        
        response.error(res);
      }         
    }else{
      response.error(res);
    }
  },

  /*****************************************
    VERIFY PERMISSIONS
  *****************************************/
  verifyPerms:async(req,res,next)=>{
    try{
      let hasPermissions=false;
      req.REQUIREDPERMS.some(P=>{
        let p = P.split(".");
        let requiredPermissions = ((typeof req.ORG.permissions[p[0]]!=="unefined" && typeof req.ORG.permissions[p[0]][p[1]]!=="undefined") ? req.ORG.permissions[p[0]][p[1]] : false);
        if(requiredPermissions===true || common.hasTag(req.ACCOUNT.tags, requiredPermissions)){
          hasPermissions=true;
        }else{
          hasPermissions=false;
          return true;
        }
      });
            
      if(hasPermissions){
          next();          
        }else{
          response.forbidden(res);
      }
    }catch(e){
      console.log("ERROR - account.verifyPerms", e);
      response.error(res,e);
    }
    
  },
  
  /*****************************************
    VERIFY USER
  *****************************************/
  verifyUser:async (req,res,next)=>{   
    try{
      let sessionId = ((req.headers["x-session-id"]) ? req.headers["x-session-id"] : ((req.query.session) ? req.query.session.trim() : ""))

      if(!sessionId){
        response.fail(res, "invalid request");
      }else{
        try{
          let session = await mdb.client().collection("sessions").findOne({_id:mdb.objectId(sessionId)});                
          if(session && session._id){
            req.SESSION = session;
            req.ACCOUNT = await mdb.client().collection(COLLECTION).findOne({_id:session.account_id}); 
            req.ORG = await mdb.client().collection(ORGS_COLLECTION).findOne({_id:session.org_id},{"projection":{"ui":false}});           
            let reqPath = req.baseUrl + req.path;
            if(reqPath.substr(reqPath.length-1)==="/"){
              reqPath = reqPath.substr(0,reqPath.length-1);
            }
  
            if(typeof req.REQUIREDPERMS!=="undefined"){        
              module.exports.verifyPerms(req,res,next);
            }else{
              next();
            }        
            
          }else{
            response.fail(res, "invalid user");
          }
        }catch(e){
          response.fail(res, "invalid user");
        }      
      }
    }catch(e){
      console.log("ERROR - account.verifyUser", e);
      response.error(res,e);
    }

    
  },

  /*****************************************
    LOAD USER
  *****************************************/
  loadIfUser:async (req,res,next)=>{
    try{
      
      let sessionId = ((req.headers["x-session-id"]) ? req.headers["x-session-id"] : ((req.query.session) ? req.query.session.trim() : ""))

      try{
          mdb.objectId(sessionId)
      }catch(e){
        sessionId=null;    
      }

      if(!sessionId){      
        req.SESSION={};
        let hostname="";
        try{
            hostname = new URL((req.headers.referer || req.headers.referrer)).hostname;
        }catch(e){
            if(req.body["url"] || req.query["url"]){
                hostname = (req.body["url"]) ? req.body["url"].trim().toLowerCase() : req.query["url"].trim().toLowerCase();
            }
        } 
        req.ORG = await mdb.client().collection(ORGS_COLLECTION).findOne({"url":hostname},{"projection":{"ui":false}});
        next();
      }else{        
        let session = await mdb.client().collection("sessions").findOne({_id:mdb.objectId(sessionId)});      
        if(session && session._id){
          req.SESSION = session;
          req.ORG = await mdb.client().collection(ORGS_COLLECTION).findOne({_id:session.org_id},{"projection":{"ui":false}});
          next();
        }else{
          req.SESSION=null;
          next();
        }      
      }
    }catch(e){
      console.log("ERROR - account.loadIfUser", e);
      response.error(res,e);
    }

    
  },

  /*****************************************
    GET
  *****************************************/
  get:async (req, res) => {       
    try{
      let account = await mdb.client().collection(COLLECTION).findOne({_id:req.SESSION.account_id},{"projection":{"fields":true,"favorites":true, "cart":true, "outbox":true, "tags":true, "_id":false}});
      if(account){
       response.success(res,account); 
      }else{
        response.fail(res)
      }
    }catch(e){
      console.log("ERROR - account.get", e);
      response.error(res,e);
    }

      
    
  },

  /*****************************************
    ADD FAVORITE
  *****************************************/
  addFavorite:async (req, res) => {
    try{
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$addToSet":{"favorites":req.body.item_id}});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }
    }catch(e){
      console.log("ERROR - account.addFavorite", e);
      response.error(res,e);
    }
        
  },

  /*****************************************
    DELETE FAVORITE
  *****************************************/
  deleteFavorite:async (req, res) => {
    try{
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$pullAll":{"favorites":[req.params.itemId]}});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }    
    }catch(e){
      console.log("ERROR - account.deleteFavorite", e);
      response.error(res,e);
    }
    
  },

  /*****************************************
    ADD MAIL ITEM
  *****************************************/
  addMailItem:async (req, res) => {
    try{
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$addToSet":{"outbox":req.body.item_id}});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      } 
    }catch(e){
      console.log("ERROR - account.addMailItem", e);
      response.error(res,e);
    }
       
  },

  /*****************************************
    DELETE FAVORITE
  *****************************************/
  removeMailItem:async (req, res) => {
    try{
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$pullAll":{"outbox":[req.body.item_id]}});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }  
    }catch(e){
      console.log("ERROR - account.removeMailItem", e);
      response.error(res,e);
    }
      
  },
  
 /*****************************************
    SEND OUTBOX
    // Get template
    // Replace template with products
    // Send SES
  *****************************************/
  sendOutbox:async (req, res) => {
    
    try{
      //----- VERIFY FIELDS --------
      if(!req.body.to_emails){
          response.fail(res,"Missing TO email");
          return false;
      } 
      if(!req.body.subject){
        response.fail(res,"Missing subject");
          return false;
      }  
      if(!req.body.items || req.body.items.length===0){
        response.fail(res,"Missing items to send");
          return false;
      }


      //----- GET ORG -----------------------------------
      let org = await mdb.client().collection(ORGS_COLLECTION).findOne({ "_id": req.SESSION.org_id, "deleted": { "$exists": false } });
      if(!org){
        response.fail(res,"Invalid organization");      
      }else{

        //----- PARSE TO EMAILS -----------------------------------
        let toEmails = common.extractEmails(req.body.to_emails.trim());
        let linkURL="/link/";
        if(typeof org.url==="string"){
          linkURL = org.url+linkURL;
        }else if(typeof org.url==="object" && org.url.length>0){
          linkURL = org.url[0]+linkURL;
        }

        if(linkURL.indexOf("https://")<0){
          linkURL = "https://"+linkURL;
        }

        //----- CONVERT ITEMS TO LINKS -----------------------------------
        let items=[];
        
        for(let item of req.body.items){
          let linkId = await links.create({
            "org_id":req.SESSION.org_id,
            "account_id":req.SESSION.account_id,
            "item_id":mdb.objectId(item.id),
            "item_name":item.name,
            "expire":((org.server.config.emailed_link_ttl) ? org.server.config.emailed_link_ttl : null)
          });
          if(linkId){
            items.push({
              "url":linkURL+linkId,
              "img":process.env["DOSPACES_ENDPOINT"]+item.image,
              "name":item.name
            })  
          }else{
            response.error(res, "Failed to create link");
            break;
          }              
        }

        //----- GENERATE BODY -----------------------------------
        let emailBody = Handlebars.compile(org.server.templates.outbox.body)({"message":req.body.message, "items":items});
        
        // Create an email record
        let emailRecord = await mdb.client().collection("emails").insertOne({
          "org_id":req.SESSION.org_id,
          "account_id":req.SESSION.account_id,
          "from":req.SESSION.email,
          "to":toEmails,
          "subject":req.body.subject.trim(),
          "body":emailBody,
          "items":req.body.items,
          "notify": ((req.body.notify) ? req.body.notify : false),
          "created":new Date()
        });

        
        aws.email({
          "to":toEmails,
          "from": `${org.server.templates.outbox.from} <${org.server.config.contacts.from}>`,
          "reply":req.SESSION.email,
          "subject": req.body.subject.trim(),
          "body":emailBody+((emailRecord && emailRecord.insertedId) ? `<img src="${process.env.SERVER_URL}/e/${emailRecord.insertedId}">` : "")
        }).then(r=>{        
          __clearOutbox(req.SESSION.account_id);
          response.success(res);
        }).catch(e=>{
          console.log("AWS EMAIL error",e)
          console.log(toEmails);
          response.error(res,e);
        });

        // LOG ACTIVITY ---------------------------   
        let activityDataToLog=[];
        
        for(let item of req.body.items){
          activityDataToLog.push({
            "org_id":req.SESSION.org_id,          
            "account_id":req.SESSION.account_id,
            "event":{
              "system":"item",
              "action":"emailed"   
            },          
            "data":{
              "item_id":mdb.objectId(item.id),
              "name":item.name
            }
          });
        }
        
        activityModel.log(activityDataToLog);
        
      }
    }catch(e){
      console.log("ERROR - account.sendOutbox", e);
      response.error(res,e);
    }    
  },

/*****************************************
    ADD CART ITEM
*****************************************/
  addCartItem:async (req, res) => {
    try{
      let dataToSet={};
      dataToSet["cart."+req.body.item_id]={"qty":1};
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$set":dataToSet});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }
    }catch(e){
      console.log("ERROR - account.addCartItem", e);
      response.error(res,e);
    }
        
  },

/*****************************************
    ADD CART
*****************************************/
  addCart:async (req, res) => {
    try{
      let dataToSet={"cart":{}};
      dataToSet.cart = req.body.cart;
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$set":dataToSet});
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }
    }catch(e){
      console.log("ERROR - account.addCart", e);
      response.error(res,e);
    }
        
  },

/*****************************************
    REMOVE CART ITEM
*****************************************/
  removeCartItem:async (req, res) => {
    try{
      let dataToUnSet={};
      dataToUnSet[`cart.${req.params.itemId}`]="";

      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$unset":dataToUnSet});
      
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }  
    }catch(e){
      console.log("ERROR - account.removeCartItem", e);
      response.error(res,e);
    }
      
  },

/*****************************************
    CLEAR CART
*****************************************/
  clearCart:async (req, res) => {
    try{
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$set":{"cart":{}}});
    
      if(updated && updated.modifiedCount>0){
        response.success(res); 
      }else{
        response.fail(res)
      }
    }catch(e){
      console.log("ERROR - account.clearCart", e);
      response.error(res,e);
    }
        
  },


/*****************************************
    UPDATE CART ITEM
*****************************************/
  updateCartItem:async (req,res)=>{
    try{
      let dataToSet={};
    
    // dataToSet.cart[req.body.item_id]={ "qty": Number(((req.body.qty) ? req.body.qty : 1)) };

      Object.keys(req.body.dataToSet).forEach(key=>{
        dataToSet[`cart.${req.body.item_id}.${key}`] = req.body.dataToSet[key];
      })
      
      let updated = await mdb.client().collection(COLLECTION).updateOne({_id:req.SESSION.account_id},{"$set":dataToSet});
      
      if(updated && updated.matchedCount>0){ //modifiedCount
        response.success(res); 
      }else{
        response.fail(res)
      } 
    }catch(e){
      console.log("ERROR - account.updateCartItem", e);
      response.error(res,e);
    }
       
  },

  /*****************************************
    LOG ACTIVITY EVENT
*****************************************/  
  getEmails:async (req, res) => {
    try{      
      let myEmails = await mdb.client().collection("emails").find({"account_id":req.SESSION.account_id}).sort({"created":-1}).toArray();
      if(myEmails){
        response.success(res, myEmails);
      }else{
        response.missing(res);
      }
    }catch(e){
      console.log("ERROR - account.getEmails", e);
      response.error(res,e);
    }

    
  },

  /*****************************************
    GET ALL
  *****************************************/
    getAll:async (req, res) => {  
      try{
        let accounts = await mdb.client().collection(COLLECTION).find({"org_id":req.SESSION.org_id, "archived":{"$exists":false}}).toArray();
        if(accounts && accounts.length>0){
        response.success(res, accounts); 
        }else{
          response.noData(res);
        }   
      }catch(e){
        console.log("ERROR - account.getAll", e);
        response.error(res,e);
      }       
       
  },

/*****************************************
  GET ONE
*****************************************/
  getOne:async (req, res) => {       
    try{
      let account = await mdb.client().collection(COLLECTION).findOne({"_id":mdb.objectId(req.params.accountId), "org_id":req.SESSION.org_id, "archived":{"$exists":false}});
      if(account){
       response.success(res, account); 
      }else{
        response.fail(res);
      } 
    }catch(e){
      console.log("ERROR - account.getOne", e);
      response.error(res,e);
    } 
         
  },

/*****************************************
  GET USER SESSIONS
*****************************************/
  getUserSessions:async (req, res) => {        
    try{
      let sessions = await mdb.client().collection("sessions").find({"account_id":mdb.objectId(req.params.accountId), "org_id":req.SESSION.org_id},{"projection":{"created":true}}).toArray();
      if(sessions){
      response.success(res, sessions); 
      }else{
        response.fail(res);
      } 
    }catch(e){
      console.log("ERROR - account.getUserSessions", e);
      response.error(res,e);
    }
       
  },

/*****************************************
  DELETE USER SESSIONS
*****************************************/
  deleteUserSessions:async (req,res)=>{
    try{
      let sessions = await mdb.client().collection("sessions").deleteMany({"account_id":mdb.objectId(req.params.accountId), "org_id":req.SESSION.org_id});
      if(sessions){
      response.success(res, sessions); 
      }else{
        response.fail(res);
      }
    }catch(e){
      console.log("ERROR - account.deleteUserSessions", e);
      response.error(res,e);
    }
    
  },

/*****************************************
    SAVE ONE
*****************************************/
    saveOne:async (req, res) => {     
      try{
        let dataToSet={
          "updated":new Date(),
          "updated_by":req.SESSION.email
        };
  
        let sessionData={};
  
        switch(req.body.context){
          case "basic":
            dataToSet.active = ((req.body.active) ? true : false);
            if(req.body.email && req.body.email.length>0){
              dataToSet.email = req.body.email.trim().toLowerCase();
              sessionData.email = req.body.email.trim().toLowerCase();
            }          
          break;
          case "fields":
            dataToSet.fields = ((typeof req.body.fields==="object") ? req.body.fields : {});
          break;
          case "tags":
            dataToSet.tags = ((typeof req.body.tags==="object") ? req.body.tags : []);
            sessionData.tags = dataToSet.tags;
          break;
        }
  
        let account = await mdb.client().collection(COLLECTION).updateOne({"_id":mdb.objectId(req.params.accountId), "org_id":req.SESSION.org_id}, {"$set":dataToSet});
        
        await mdb.client().collection("sessions").updateMany({"account_id":mdb.objectId(req.params.accountId), "org_id":req.SESSION.org_id}, {"$set":sessionData});
  
        if(account){
         response.success(res, account); 
        }else{
          response.fail(res);
        }   
      }catch(e){
        console.log("ERROR - account.saveOne", e);
        response.error(res,e);
      }       
  },

/*****************************************
    APPROVER
*****************************************/
  approver:async(req,res) => {
    try{

      if(!req.body.action || !req.body.accountId){
        response.missing(res,"Missing request data");
      }else{      
        
        let org = await mdb.client().collection("orgs").findOne({ "_id":req.SESSION.org_id});
        let account = await mdb.client().collection("accounts").findOne({"_id":mdb.objectId(req.body.accountId), "org_id":req.SESSION.org_id});

        if(account){

          let dbResult=false;
          let emailTemplate="denied";

          if(req.body.action.trim().toLowerCase()==="approved"){
            let dataToSet={
              "active":true,
              "updated":new Date(),
              "updated_by":req.SESSION.email
            };

            emailTemplate="approved";
            dbResult = await mdb.client().collection("accounts").updateOne({"_id":mdb.objectId(req.body.accountId), "org_id":req.SESSION.org_id}, {"$set":dataToSet});  
          }else{
            dbResult = await mdb.client().collection("accounts").deleteOne({"_id":mdb.objectId(req.body.accountId), "org_id":req.SESSION.org_id});  
          }          
        
          if(dbResult && (dbResult.matchedCount>0 || dbResult.deletedCount>0)){      

            try{
              aws.email({
                "to":account.email,
                "from":`${org.server.templates.register[emailTemplate].from} <${org.server.config.contacts.from}>`,
                "subject": org.server.templates.register[emailTemplate].subject,
                "body":Handlebars.compile(org.server.templates.register[emailTemplate].body)({"account":account})
              }).then(r=>{                
                response.success(res); 
              }).catch(e=>{        
                response.error(res);
              }); 
            }catch(e){
              console.log(e);
              response.success(res);
            }
                   
        
          }else{
            response.fail(res);
          }
        }else{
          response.fail(res, "Invalid user");
        }
         
      }

      
    }catch(e){
      console.log("ERROR - account.APPROVER", e);
      response.error(res,e);
    }
  }
}