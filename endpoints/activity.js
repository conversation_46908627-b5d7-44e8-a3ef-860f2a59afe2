const mdb = require("../libs/mdb.js");
const response = require("../libs/response.js");
const activityModel = require("../models/model_activity.js");
const aws = require("../libs/aws.js");

 

module.exports={

/*****************************************
    LOG ACTIVITY EVENT
*****************************************/  
  log:async (req, res) => {
    let dataToSave = ((req.body) ? req.body : null);
    
    if(dataToSave){

      if(typeof dataToSave==="object" && !dataToSave.length){
        dataToSave = [dataToSave];
      }
  
      dataToSave.forEach(function(d,i){
        if(req.SESSION && req.SESSION.org_id){
          dataToSave[i].org_id = req.SESSION.org_id;
        }else if(typeof dataToSave[i].org_id === "string"){
          dataToSave[i].org_id = mdb.objectId(dataToSave[i].org_id);  
        }
    
        if(req.SESSION && req.SESSION.account_id){
          dataToSave[i].account_id = req.SESSION.account_id;
        }else if(typeof dataToSave[i].account_id === "string"){
          dataToSave[i].account_id = mdb.objectId(dataToSave[i].account_id);  
        }
      });
      
      let logId = activityModel.log(dataToSave);
      
      if(logId){
        response.success(res, { id: logId });
      }else{
        response.fail(res, "actvity not saved");
      }
      
    }else{
      response.missing(res, "no activity to log");
    }
  
  },


  request:async(req,res,next)=>{
    let dataToSave={
      "method":req.method,
      "path":req.path,
      "body":req.body,
      "params":req.params,      
      "ts":new Date()
    };


    if(req.SESSION){
      dataToSave.session = req.SESSION;
    }

    await mdb.client().collection("requests").insertOne(dataToSave);
    next();
  },

  alert:async function(req,res){    
    aws.email({          
      "to":"<EMAIL>",
      "from":`BMGHUB ALERTS <<EMAIL>>`,
      "subject": "BMH HUB ALERT",
      "body":(req.body.message || "-")
    }).then(r=>{       
      response.success(res)         
    }).catch(e=>{        
      console.log(e);
      response.error(res);
    });
  },

  
  
/*****************************************
    EMAIL VIEW PIXEL
*****************************************/
  emailView:async (req,res)=>{
    if(req.params && req.params.emailId){
      //Update Email Record
      let email = await mdb.client().collection("emails").findOneAndUpdate({"_id":mdb.objectId(req.params.emailId.trim())}, {"$inc":{"views":1},"$set":{"last_viewed":new Date()} });
  
    
      if(email){
        // Log activity event    
        activityModel.log({
          "org_id":email.value.org_id,
          "account_id":email.value.account_id,
          "event":{
            "system":"email",
            "action":"view"   
          },          
          "data":{
            "email_id":email.value._id  
          }
          
        });
  
        // Send email notification
        if(email.value.notify && !email.value.views){
          aws.email({
            "to":email.value.from,
            "from":`Notification Service <${org.server.config.contacts.from}>`,
            "subject": "Email View Notification",
            "body":`<p>The following email was viewed:</p><p><b>To</b> ${email.value.to.join(", ")}<br><b>Subject:</b> ${email.value.subject}</p><p>-------------</p>${email.value.body}`
          }).catch(e=>{
            console.log("AWS EMAIL error",e)          
          });
        }
        
      }
        
    }

    res.redirect(process.env.SERVER_URL+'/static/1x1.png');
    
  }
  
}