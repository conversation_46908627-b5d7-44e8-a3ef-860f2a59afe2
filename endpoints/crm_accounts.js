const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={

    /*****************************************
        GET ACCOUNTS
    *****************************************/
    get:async (req, res) => {       
        try{
          let dataToMatch ={org_id:req.ACCOUNT.org_id};
          let accounts = await mdb.client().collection("crm_accounts").aggregate([
            {"$match":dataToMatch},
            {"$lookup":{ "from": "accounts", "localField": "owner_id", "foreignField": "_id", "as": "owner" }}
          ]).toArray();

          accounts.forEach(account=>{
            if(account.owner && account.owner.length>0){
              account.owner = account.owner[0];
              delete account.owner.cart;
              delete account.owner.favorites;
              delete account.owner.outbox;
              delete account.owner.tags;
            }
          })
          if(accounts){
           response.success(res,accounts); 
          }else{
            response.fail(res)
          }
        }catch(e){
          console.log("ERROR - accounts.get", e);
          response.error(res,e);
        }        
    },

    /*****************************************
        ADD ACCOUNTS
    *****************************************/
    add:async (req, res) => {       
        try{
          let dataToInsert = req.body;
            dataToInsert.org_id = req.ACCOUNT.org_id;
            dataToInsert.created = new Date();
            dataToInsert.created_by = req.ACCOUNT._id;
            dataToInsert.updated = new Date();
            dataToInsert.updated_by = req.ACCOUNT._id;
            dataToInsert.owner_id = req.ACCOUNT._id;            
            dataToInsert.logs=[
              {
                "note": "Account created",
                "dt":new Date(),
                "by":req.ACCOUNT.email
              }
            ];
            
          let newAccountId = mdb.objectId();
          let accounts = await mdb.client().collection("crm_accounts").findOneAndUpdate(
            { _id: newAccountId },
            {"$setOnInsert":dataToInsert},
            {upsert:true, returnDocument: 'after'});

          if(accounts && accounts.ok){

            await mdb.client().collection("crm_logs").insert({
                'org_id':req.ACCOUNT.org_id,
                'crm_account_id':newAccountId,
                'account_id':req.ACCOUNT._id,
                "note": "Account created",
                "dt":new Date(),
                "by":req.ACCOUNT.email
              })

           response.success(res,accounts.value); 
          }else{
            response.fail(res)
          }

        }catch(e){
          console.log("ERROR - accounts.add", e);
          response.error(res,e);
        }        
    }

}