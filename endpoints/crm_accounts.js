const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports={

    _get:async (req, res) => {       
        try{
          let accounts = await mdb.client().collection("crm_accounts").findOne({_id:mdb.ObjectId(req.params.id),org_id:req.ACCOUNT.org_id});
          if(accounts){
           response.success(res,accounts);

    /*****************************************
        GET ACCOUNTS
    *****************************************/
    get:async (req, res) => {       
        try{
          let accounts = await mdb.client().collection("crm_accounts").find({org_id:req.ACCOUNT.org_id}).toArray();
          if(accounts){
           response.success(res,accounts); 
          }else{
            response.fail(res)
          }
        }catch(e){
          console.log("ERROR - accounts.get", e);
          response.error(res,e);
        }        
    },

    /*****************************************
        Add ACCOUNTS
    *****************************************/
    add:async (req, res) => {       
        try{
          let dataToInsert = req.body;
          dataToInsert.org_id = req.ACCOUNT.org_id;
          let accounts = await mdb.client().collection("crm_accounts").insertOne(dataToInsert,{});
          if(accounts){
           response.success(res,accounts); 
          }else{
            response.fail(res)
          }
        }catch(e){
          console.log("ERROR - accounts.get", e);
          response.error(res,e);
        }        
    }

}