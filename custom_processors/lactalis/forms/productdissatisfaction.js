const mdb = require(process.cwd()+"/libs/mdb.js");
const objectStorage = require(process.cwd()+"/libs/objectstorage.js");
const response = require(process.cwd()+"/libs/response.js");
const zendesk = require('node-zendesk');    
const FIELDNAME = "FILES";

const zdClient = zendesk.createClient({
  username:  'k<PERSON><PERSON><PERSON><PERSON>@bmgmarketing.us',
  token:     process.env.ZENDESK_API_KEY,
  subdomain: 'bmgmarketinghelp'
});


module.exports={

    beforeSave:async function(req, flow, dataToSave){
        let dataToReturn = Object.assign({},req.body);
        dataToReturn[FIELDNAME]=[];
             

        async function _createObject(req, data){
            return await objectStorage.create(Object.assign({ "meta": {                    
                "formflow_submissions_id":mdb.objectId(req.params.id)
            }, 
                "org_id":req.ORG._id,
                "public":true
            },data));        
        }
                
        if(typeof req.body[FIELDNAME]!=="undefined"){            
            try{

                if(typeof req.body[FIELDNAME]==="object" && req.body[FIELDNAME].length>0){
                    for(const file of req.body[FIELDNAME]){
                        let newObjectId = await _createObject(req, file);
                        if(newObjectId){
                            dataToReturn[FIELDNAME].push(newObjectId);
                        }
                    }
                }else{
                    let newObjectId = await _createObject(req, req.body[FIELDNAME]);
                    if(newObjectId){
                        dataToReturn[FIELDNAME].push(newObjectId);
                    }
                }
                
            }catch(e){
                console.log(dataToSave);
                console.log("Product Dissatisfaction before save",e);    
            }
        }
        
        return dataToReturn;

    },
/*****************************************
    AFTER SAVE
*****************************************/
    afterSave:async function(req, res){

        console.log("CALL ZENDESK");

        let requesterData={"locale_id": 1, "name": "Product Dissatisfaction Form", "email": "<EMAIL>"};

        if(req.body.CUSTOMERCONTACT){
            requesterData.name = req.body.CUSTOMERCONTACT.trim();
        }
        if(req.body.CONTACTEMAIL){
            requesterData.email = req.body.CONTACTEMAIL.trim();
        }

        console.log(await zdClient.tickets.create({
            ticket: {
                subject: 'Test Ticket from API',
                comment: {body: "Plain text\nwith new line"},
                requester: requesterData
        }}));

        response.success(res);
    }
}