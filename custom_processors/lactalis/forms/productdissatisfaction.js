const mdb = require(process.cwd()+"/libs/mdb.js");
const objectStorage = require(process.cwd()+"/libs/objectstorage.js");
const response = require(process.cwd()+"/libs/response.js");
    
const FIELDNAME = "FILES";


module.exports={

    beforeSave:async function(req, flow, dataToSave){
        let dataToReturn = {};
        dataToReturn[FIELDNAME]=[];
        
        let submission = await mdb.client().collection("formflow_submissions").findOne({"_id": mdb.objectId(req.params.id)});
        
        if(typeof submission.response==="object" && typeof submission.response[FIELDNAME]==="object" && submission.response[FIELDNAME].length>0){
            dataToReturn[FIELDNAME] = dataToReturn[FIELDNAME].concat(submission.response[FIELDNAME]);
        }

        console.log(submission.response);
        process.exit();
                
        if(submission && typeof submission.response!=="undefined"){
            // --- Save File Object -----------------------------------------------
            try{
                let newObjectId = await objectStorage.create(Object.assign({ "meta": {                    
                    "formflow_submissions_id":mdb.objectId(req.params.id)
                }, 
                    "org_id":req.ORG._id,
                    "account_id":null
                },dataToSave[FIELDNAME]));
            
                if(newObjectId){
                    dataToReturn[FIELDNAME].push(newObjectId);
                }
            }catch(e){
                console.log(dataToSave);
                console.log("Sahlen Proof of Purchase before save",e);    
            }
        }
        
        return dataToReturn;

    },
/*****************************************
    AFTER SAVE
*****************************************/
    afterSave:async function(req, res){

        console.log("CALL ZENDESK");

        response.success(res);
    }
}