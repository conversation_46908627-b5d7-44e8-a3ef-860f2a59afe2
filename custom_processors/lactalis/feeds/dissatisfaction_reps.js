const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports=async function(req,res){
    try{
        if(req.ORG._id.toString()==="6425eef367c2b869c563d7be"){
            let dataToReturn = [];
            let accounts = await mdb.client().collection("cache").findOne({"src":"feeds","key":"dissatisfactionreps"});
            if(accounts && accounts.data){
                dataToReturn = accounts.data;
            }else{
                accounts = await mdb.client().collection("accounts").find({ "org_id": req.ORG._id}).sort({"fields.LNAME":1, "fields.LNAME":1}).toArray();                
                accounts.forEach(function(account){
                    if(account.fields.FNAME && account.fields.LNAME){
                        dataToReturn.push({
                            "_id": account._id.toString(),
                            "name": (account.fields.FNAME && account.fields.LNAME) ? (account.fields.FNAME+" "+account.fields.LNAME) : (account.fields.FNAME || account.fields.LNAME || "")
                        });
                    }
                });
                await mdb.client().collection("cache").insertOne({"src":"feeds","key":"dissatisfactionreps","data":dataToReturn, "created":new Date()});
            }
        
            
            response.success(res, dataToReturn);
        }else{
            response.error(res,"not authorized");
        }
        
    }catch(e){
        console.log("ERROR - lactalis.feeds.dissatisfaction_reps", e);
        response.error(res,e);
    }
}