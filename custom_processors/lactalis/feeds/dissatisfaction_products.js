const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");

module.exports=async function(req,res){
    try{
        if(req.ORG._id.toString()==="6425eef367c2b869c563d7be"){
            let dataTofind={"org_id": req.ORG._id};
            let accounts = await mdb.client().collection("items").find(dataTofind).sort({"fields.FULLNAME":-1}).toArray();
            let dataToReturn = [];
            accounts.forEach(function(account){
                dataToReturn.push({
                    "_id": item._id.toString(),                    
                    "name": item.fields.FULLNAME
                });
            });
            response.success(res, dataToReturn);
        }else{
            response.error(res,"not authorized");
        }
        
    }catch(e){
        console.log("ERROR - lactalis.feeds.dissatisfaction_products", e);
        response.error(res,e);
    }
}