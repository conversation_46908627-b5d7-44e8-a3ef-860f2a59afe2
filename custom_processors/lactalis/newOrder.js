const mdb = require("../../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require("../../libs/aws.js");
const HB = require("../../libs/handlebars.js");
const activityModel = require("../../models/model_activity.js");
const _ = require("lodash");
const response = require("../../libs/response.js");
const orderProcessor = require("../../libs/orders.js");


const PARISHABLE_ITEM_TAGS = ["lQ3aM6"], // Type > Product
    NON_PARISHABLE_ITEM_TAGS = ["dbxOnb","30Q9OP"], // Type > POS Materials | Type > Promotional Items
    STATUS_PENDING="c3yf7",
    STATUS_WHEN_APPROVED="7yc3f"; //<-- currently set to SUBMITTED  | Approved="wW9DK",    
    BMGUSER="fF01zd2W",
    ACCOUNT_TAGS_NEEDING_APPROVAL=["cjNhcjen"]; // Brokers


module.exports=async function(req,res,org){
    let reqBody = ((req.body) ? req.body : {});    

    let ISTEST = false;
    
    if(!reqBody.items){
        response.fail(res, "We could not place your order because your cart is empty.");
        return;
    }

    // ---- Find the account that placed order ---------------------------------------
    let orderAccount = await mdb.client().collection("accounts").findOne({"_id":req.SESSION.account_id});

    if(orderAccount.tags.indexOf("territorySelf")>=0){
        reqBody.approved_by={
            "name":orderAccount.fields.FNAME+" "+orderAccount.fields.LNAME,
            "email":orderAccount.email
        };
    }else if(orderAccount.tags.indexOf("territoryOther")>=0){
        reqBody.approved_by={
            "name":"Tim Conner",
            "email":"<EMAIL>"
        };
    }

    // ---- Make sure we cast the form fields as the correct type ----------------------------------
    Object.keys(reqBody.fields).forEach((f)=>{   

        // ---- Allow BMG users to send TEST orders
        if(reqBody.fields[f].trim().toLowerCase()==="test"){
            if(orderAccount.tags.indexOf(BMGUSER)>=0){
                ISTEST=true;
            }            
        }

        switch(org.data_configs.orders.fields[f].type){
            case "date":
                reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
            break;
            case "number":
                reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
            break;
        }
    });

    // ---- Find the order Items -----------------------------------------------
    let itemsInOrder=[];
    Object.keys(reqBody.items).forEach(function(itemId){        
        itemsInOrder.push(mdb.objectId(itemId));
    });

    let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemsInOrder}}).toArray();    

    let orderHasNonParishable=[],orderHasParishable=[];
    
    for(let orderItem of orderItems){    
        
        //If this is tagged "Type > Product" AND the unit is not being passed in from the cart, then set it to the default "each".
        if(orderItem.tags.indexOf("lQ3aM6")>=0 && (!reqBody.items[orderItem._id.toString()].unit || reqBody.items[orderItem._id.toString()].unit.trim().length===0)){            
            reqBody.items[orderItem._id.toString()].unit = "each";            
        }
        
        
        if(_.intersection(orderItem.tags,NON_PARISHABLE_ITEM_TAGS).length>0){
            //orderHasNonParishable.push(Object.assign({"qty":reqBody.items[orderItem._id.toString()].qty},orderItem));
            orderHasNonParishable.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        }else if(_.intersection(orderItem.tags,PARISHABLE_ITEM_TAGS).length>0){
            //orderHasParishable.push(Object.assign({"qty":reqBody.items[orderItem._id.toString()].qty},orderItem));
            orderHasParishable.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        }
    }

    let singleOrderItems=[];
    Object.keys(reqBody.items).forEach(function(itemId){             
       // singleOrderItems.push(Object.assign({"qty": Number(reqBody.items[itemId].qty) },_.find(orderItems,{"_id":mdb.objectId(itemId)})));
        singleOrderItems.push(Object.assign({},reqBody.items[itemId],_.find(orderItems,{"_id":mdb.objectId(itemId)})));
    });

  


    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //This order has BOTH types of items and we need to split it.


    if(orderHasNonParishable.length>0 && orderHasParishable.length>0){  /////////////////////////////////////////

        //=== Construct BMG Order =============================
        let bmgItems={};
        orderHasNonParishable.forEach(i=>{
            //bmgItems[i._id]={"qty": reqBody.items[i._id].qty };
            bmgItems[i._id]=reqBody.items[i._id];
        })
        let bmgOrder=Object.assign({},reqBody,{"items":bmgItems});

        bmgOrder.limited_to_tags = NON_PARISHABLE_ITEM_TAGS; 

        //For BMG, we just create the order in the system. No need for an email
        orderProcessor.create(bmgOrder, req.SESSION, org).then((orderTemplateData)=>{
            aws.email({
                "to": ((ISTEST) ? orderAccount.email  : org.server.config.contacts.bmg_warehouse),
                "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":orderHasNonParishable})
            }).then(r=>{                
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            }); 
        });
        
        //=== Construct Lcatalis Order =============================
        let lactalisItems={};
        orderHasParishable.forEach(i=>{
            //lactalisItems[i._id]={"qty": reqBody.items[i._id].qty };
            lactalisItems[i._id]=reqBody.items[i._id];
        })
        let lactalisOrder=Object.assign({},reqBody,{"items":lactalisItems});        

        lactalisOrder.limited_to_tags = PARISHABLE_ITEM_TAGS; 

        if(_.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0){  lactalisOrder.status = STATUS_PENDING;  }

        let approvalManager = ((req.ORG.server.config.contacts && req.ORG.server.config.contacts.approval_managers && req.ORG.server.config.contacts.approval_managers[lactalisOrder.fields.STATE]) ? req.ORG.server.config.contacts.approval_managers[lactalisOrder.fields.STATE] : {"name":"System Error","email":"<EMAIL>"});
        
        if(lactalisOrder.status === STATUS_PENDING && approvalManager.auto){
            lactalisOrder.status = STATUS_WHEN_APPROVED;
            lactalisOrder.approved_by=approvalManager;
        }

        orderProcessor.create(lactalisOrder, req.SESSION, org).then((orderTemplateData)=>{
            
            if(_.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0 && !approvalManager.auto){                 

                //9.7.23 let approvalManager = ((req.ORG.server.config.contacts && req.ORG.server.config.contacts.approval_managers && req.ORG.server.config.contacts.approval_managers[lactalisOrder.fields.STATE]) ? req.ORG.server.config.contacts.approval_managers[lactalisOrder.fields.STATE] : {"name":"System Error","email":"<EMAIL>"});                

                // user is a BROKER -- Need to request approval first 
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>"  : approvalManager.email)),
                    "from": `${org.server.templates.order.lactalisBroker.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.lactalisBroker.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.lactalisBroker.body, {"order":orderTemplateData, "items":orderHasParishable})
                }).then(r=>{                           
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });
                                    

            }else{

                //user is STAFF or ADMIN -- just need to send email to warehouse
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : org.server.config.contacts.lactalis_warehouse),
                    "from": `${org.server.templates.order.lactalisWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.lactalisWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.lactalisWarehouse.body, {"order":orderTemplateData, "items":orderHasParishable})
                }).then(r=>{     
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });  
                                    
            } 

            response.success(res);

        }).catch(err=>{
            console.log(err);
            response.fail(res);
        });




    }else{ // Create just the one order //////////////////////////////////////////////////////////////////////////

        if(orderHasParishable.length>0){
            reqBody.limited_to_tags = PARISHABLE_ITEM_TAGS;
        }else{
            reqBody.limited_to_tags = NON_PARISHABLE_ITEM_TAGS;
        }        

        if(orderHasParishable.length>0 && _.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0){ 
            reqBody.status = STATUS_PENDING;
        }

        let approvalManager = ((req.ORG.server.config && req.ORG.server.config.contacts && req.ORG.server.config.contacts.approval_managers) ? req.ORG.server.config.contacts.approval_managers[reqBody.fields.STATE] : "");

        if(_.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0 && approvalManager && approvalManager.auto){
            reqBody.status = STATUS_WHEN_APPROVED;
            reqBody.approved_by=approvalManager;
        }

        orderProcessor.create(reqBody, req.SESSION, org).then((orderTemplateData)=>{
                    
            //This is a LACTALIS warehouse order
            if(orderHasParishable.length>0){
            
                //First check to see if user is staff or broker         

                if(_.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0 && !approvalManager.auto){ 
                    
                    //let approvalManager = ((req.ORG.server.config && req.ORG.server.config.contacts && req.ORG.server.config.contacts.approval_managers) ? req.ORG.server.config.contacts.approval_managers[reqBody.fields.STATE] : "");

                    // user is a BROKER -- Need to request approval first
                    aws.email({
                        "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : approvalManager.email) ),
                        "from": `${org.server.templates.order.lactalisBroker.from} <${org.server.config.contacts.from}>`,
                        "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.lactalisBroker.subject.trim(), {"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.lactalisBroker.body,{"order":orderTemplateData, "items":singleOrderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });
                                        

                }else{

                    //user is STAFF -- just need to send email to warehouse
                    aws.email({
                        "to": ((ISTEST) ? orderAccount.email  : org.server.config.contacts.lactalis_warehouse),
                        "from": `${org.server.templates.order.lactalisWarehouse.from} <${org.server.config.contacts.from}>`,
                        "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.lactalisWarehouse.subject.trim(), {"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.lactalisWarehouse.body,{"order":orderTemplateData, "items":singleOrderItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });  
                                        
                }

            }else{ // EMAIL BMG STAFF ONLY
                 
                aws.email({
                    "to": ((ISTEST) ? orderAccount.email  : org.server.config.contacts.bmg_warehouse),
                    "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":singleOrderItems})
                }).then(r=>{                
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });  

            }

            response.success(res);

        }).catch(err=>{ 
            console.log(err);
            response.fail(res, err);
        })

    }
   
}