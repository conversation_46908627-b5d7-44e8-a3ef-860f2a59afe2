const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const operatorsModel = require(process.cwd()+"/models/model_operators.js");

module.exports={
    syncFieldMapper:function(data){
            let output={};
    
            Object.keys(data).forEach(function(key){
    
                switch(key){                
                    case "yui_3_18_1_1_1619200199475_5939":
                        output.business_name = data[key][0];
                    break;
                    case "ma-form-element-yui_3_18_1_1_1663699049619_8467":
                        output.restaurant_units = ((data[key][0]) ? Number(data[key][0].trim()) : 0);
                    break;
                    case "ma-form-element-yui_3_18_1_1_1619200199475_5639":
                        output.phone = data[key][0];
                    break;
                    case "ma-form-element-yui_3_18_1_1_1618934353870_4699":
                        output.email = data[key][0];
                    break;
                    case "ma-form-element-yui_3_18_1_1_1663699049619_9163":
                        output.distributor_name = data[key][0];
                    break;
                    default:
                        output[key] = data[key][0];
                    break;                
                }
    
            });
    
            return output;
    },

    syncProcessRequest:function(req, P){
        return new Promise(async (resolve, reject)=>{
            try{
                const OWNERACCOUNTID = mdb.objectId("645c41f5a4832c507ed82cf7");
                const REGISTRATIONFORMIDS=["65492626a60f9e2163b663e3", "65b90a70ef38d02bb8518d8a", "6631906ef36536520569a984"];
            
                const ORGID = P.REBATE.org_id;
                let formData = module.exports.syncFieldMapper(req.body.submission_values);
                let rebateSubmissionId = mdb.objectId();
    
                // We need to create an operator 
                let operatorId = await operatorsModel.create({
                    "org_id":ORGID,
                    "name":formData.business_name,
                    "address":formData.address_1 + ((formData.address_2) ? " "+formData.address_2 : ""),
                    "city":formData.city,
                    "state":formData.state,
                    "zip":formData.zip,
                    "phone":formData.phone,
                    "contacts":[
                        {
                            "email":formData.email,
                            "name":formData.first_name + " " + formData.last_name
                        }
                    ],
                    "created":new Date(),
                    "updated_by":"BMG Sync"
                });
    
                // Create base rebate submission record
                let dataToInsert={
                    "_id":rebateSubmissionId,
                    "operator_id":operatorId,
                    "org_id":ORGID,
                    "account_id":OWNERACCOUNTID,
                    "rebate_id":P.REBATEID,
                    "collaborators":[formData.email],
                    "formflows":[],
                    "formflow_submissions":[],
                    "checklist":{},
                    "status":"Created",
                    "created":new Date(),
                    "updated":new Date()
                };
            
                //Now Create formflow submissions
                for(let formFlow of P.REBATE.formflows){           
                    
                    let responseData={};
                    if(REGISTRATIONFORMIDS.indexOf(formFlow.id.toString())>=0){
                        responseData={
                            "OPERATORNAME" : formData.business_name,
                            "ADDRESS" : formData.address_1 + ((formData.address_2) ? " "+formData.address_2 : ""),
                            "CITY" : formData.city,
                            "STATE" : formData.state,
                            "ZIP" : formData.zip,
                            "OPERATORCONTACT" : formData.first_name + " " + formData.last_name,
                            "PHONE" : formData.phone,
                            "EMAIL" : formData.email,
                            "OPERATORSIGNATURE" : "",
                            "SALESPERSON" : "",
                            "DISTRIBUTORNAME" : formData.distributor_name,
                            "DISTRIBUTORNUMBER" : "",
                            "FIRSTSHIPMENTDATE" : "",
                            "RESTAURANTUNITS":formData.restaurant_units
                        }
                    }
    
                    let formFlowSubmission = await mdb.client().collection("formflow_submissions").insertOne({
                        "org_id":ORGID,
                        "account_id":OWNERACCOUNTID,
                        "rebate_id":P.REBATEID,
                        "rebatesubmission_id":rebateSubmissionId,
                        "formflow_id":formFlow.id,
                        "name":"",
                        "response":responseData,
                        "created":new Date()
                    });
    
                    dataToInsert.formflow_submissions.push(formFlowSubmission.insertedId);
                }
    
                P.REBATE.formflows.forEach(function(formFlow){
                    dataToInsert.formflows.push(formFlow.id)
                });
    
                P.REBATE.checklist.items.forEach(function(checklistItem){
                    dataToInsert.checklist[checklistItem.id]=false
                });
    
                let newRebateSubmission = await mdb.client().collection("rebate_submissions").insertOne(dataToInsert);
                
                if(newRebateSubmission && newRebateSubmission.insertedId){
                    resolve();
                }else{
                    reject("Failed to record Sync Rebate Submission - E1");
                    console.log("Failed to record Sync Rebate Submission - E1",dataToInsert);            
                }
    
            }catch(e){
                reject("Failed to record Sync Rebate Submission - E2");
                console.log("Failed to record Sync Rebate Submission - E2");
                console.log(e);
            }
        });
    }
}