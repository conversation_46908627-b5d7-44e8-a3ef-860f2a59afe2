const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const operatorsModel = require(process.cwd()+"/models/model_operators.js");

module.exports={
    syncFieldMapper:function(data){
            let output={};
    
            Object.keys(data).forEach(function(key){
    
                switch(key){                   
                    default:
                        output[key] = data[key][0];
                    break;                
                }
    
            });
    
            return output;
    },

    syncProcessRequest:function(req, P){
        return new Promise(async (resolve, reject)=>{
            try{
                const OWNERACCOUNTID = mdb.objectId("645c41f5a4832c507ed82cf7");
                const REGISTRATIONFORMIDS=["65492626a60f9e2163b663e3", "65b90a70ef38d02bb8518d8a", "6631906ef36536520569a984"];
            
                const ORGID = P.REBATE.org_id;
                let formData = req.body //module.exports.syncFieldMapper(req.body);
                let rebateSubmissionId = mdb.objectId();
    
                // We need to create an operator 
                let operatorId = await operatorsModel.create({
                    "org_id":ORGID,
                    "name":formData.account,
                    "address":formData.contact_address_1 + ((formData.contact_address_2) ? " "+formData.contact_address_2 : ""),
                    "city":formData.City,
                    "state":formData.state.name,
                    "zip":formData.contact_postalcode,
                    "phone":formData.contact_work_phone,
                    "contacts":[
                        {
                            "email":formData.contact_email,
                            "name":formData.contact_first_name + " " + formData.contact_last_name
                        }
                    ],
                    "created":new Date(),
                    "updated_by":"BMG Sync"
                });
    
                // Create base rebate submission record
                let dataToInsert={
                    "_id":rebateSubmissionId,
                    "operator_id":operatorId,
                    "org_id":ORGID,
                    "account_id":OWNERACCOUNTID,
                    "rebate_id":P.REBATEID,
                    "collaborators":[formData["Email Address"]],
                    "formflows":[],
                    "formflow_submissions":[],
                    "checklist":{},
                    "status":"Created",
                    "created":new Date(),
                    "updated":new Date()
                };
            
                //Now Create formflow submissions
                for(let formFlow of P.REBATE.formflows){           
                    
                    let responseData={};
                    if(REGISTRATIONFORMIDS.indexOf(formFlow.id.toString())>=0){
                        responseData={
                            "OPERATORNAME" : formData.account,
                            "ADDRESS" : formData.contact_address_1 + ((formData.contact_address_2) ? " "+formData.contact_address_2 : ""),
                            "CITY" : formData.City,
                            "STATE" : formData.state.name,
                            "ZIP" : formData.contact_postalcode,
                            "OPERATORCONTACT" : formData.contact_first_name + " " + formData.contact_last_name,
                            "PHONE" : formData.contact_work_phone,
                            "EMAIL" : formData.contact_email,
                            "OPERATORSIGNATURE" : "",
                            "SALESPERSON" : formData["Lactalis rep"],
                            "DISTRIBUTORNAME" : formData.distributor_name,
                            "DISTRIBUTORNUMBER" : formData["Distributor From Request"],
                            "FIRSTSHIPMENTDATE" : formData["First Shipment Date"],
                            "RESTAURANTUNITS":formData["Number of"]
                        }
                    }
    
                    let formFlowSubmission = await mdb.client().collection("formflow_submissions").insertOne({
                        "org_id":ORGID,
                        "account_id":OWNERACCOUNTID,
                        "rebate_id":P.REBATEID,
                        "rebatesubmission_id":rebateSubmissionId,
                        "formflow_id":formFlow.id,
                        "name":"",
                        "response":responseData,
                        "created":new Date()
                    });
    
                    dataToInsert.formflow_submissions.push(formFlowSubmission.insertedId);
                }
    
                P.REBATE.formflows.forEach(function(formFlow){
                    dataToInsert.formflows.push(formFlow.id)
                });
    
                P.REBATE.checklist.items.forEach(function(checklistItem){
                    dataToInsert.checklist[checklistItem.id]=false
                });
    
                let newRebateSubmission = await mdb.client().collection("rebate_submissions").insertOne(dataToInsert);
                
                if(newRebateSubmission && newRebateSubmission.insertedId){
                    resolve();
                }else{
                    reject("Failed to record Sync Rebate Submission - E1");
                    console.log("Failed to record Sync Rebate Submission - E1",dataToInsert);            
                }
    
            }catch(e){
                reject("Failed to record Sync Rebate Submission - E2");
                console.log("Failed to record Sync Rebate Submission - E2");
                console.log(e);
            }
        });
    }
}