const mdb = require(process.cwd() + "/libs/mdb.js");
const _ = require("lodash");
const response = require(process.cwd() + "/libs/response.js");
const cryptojs = require("crypto-js");

module.exports = async function (req, res, org, _get) {

    let rebateSubmission =null;
    
        try{
            rebateSubmission=await _get(req.params.id, require(process.cwd() + "/custom_processors/lactalis/rebates/permissions.js")(req));
        }catch(e){
            console.log(e);
        }

    if (rebateSubmission) {

            // Now loop all the formSubmissions
            rebateSubmission.formflow_submissions.forEach(function (formFlowSubmission, i) {

                //Check for fields that we need to encrypt
                Object.keys(formFlowSubmission.response).forEach(function (key) {
                    let formFlow = _.find(rebateSubmission.formflows, { "_id": formFlowSubmission.formflow_id });
                    if (
                        typeof formFlowSubmission.response[key] !== "undefined" &&
                        (
                            (typeof formFlowSubmission.response[key] === "string" && formFlowSubmission.response[key].trim().length > 0)
                            ||
                            typeof formFlowSubmission.response[key] !== "string"
                        )
                        && formFlow.fields[key].encrypt
                        && formFlow.fields[key].type !== "file") {
                        rebateSubmission.formflow_submissions[i].response[key] = cryptojs.AES.decrypt(formFlowSubmission.response[key], process.env["CRYPTOAESKEY"]).toString(cryptojs.enc.Utf8);
                    }

                    if (formFlow.fields[key].type === 'file') {
                        delete rebateSubmission.formflow_submissions[i].response[key].content;
                    }

                });

            });


            if (
                !req.SESSION.account_id
            ) {
                delete rebateSubmission.notes;
                rebateSubmission.broker = {
                    "email": rebateSubmission.broker.email,
                };

            }
            response.success(res, rebateSubmission);
        

        

    } else {
        response.missing(res);
    }
}