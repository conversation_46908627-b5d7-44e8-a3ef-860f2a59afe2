const mdb = require(process.cwd()+"/libs/mdb.js");
const _ = require("lodash");
const response = require(process.cwd()+"/libs/response.js");

module.exports=async function(req,res,org){

    let _list =async function(matchCriteria){
        let agg=[
            {"$match":matchCriteria[0]},        
            { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
            { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
            { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } }
          ];

          if(typeof matchCriteria==="object" && matchCriteria.length>1){
            agg.push({
                "$match":matchCriteria[1]
            })
          }
        
        let submissions = await mdb.client().collection("rebate_submissions").aggregate(agg).toArray();          
    
          if (submissions) {
            
            submissions.forEach(function(submission, i){
                submissions[i].rebate = submissions[i].rebate[0];
                submissions[i].operator = submissions[i].operator[0];
                submissions[i].broker = submissions[i].broker[0];
            })        
            return submissions;
          } else {
            return false;
          }
    }

   

    try{

        let submissions= await _list(require(process.cwd() + "/custom_processors/lactalis/rebates/permissions.js")(req));
        response.success(res, submissions);

    }catch(e){ 
        console.log(e);
        response.error(res, e);
    } 
    // Apply the logic for who gets to see what
    


}