const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/vendorrequestformgosimple.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        const form = pdfDoc.getForm();        

        form.getTextField('requestFrom').setText((submission.response.VRF_REQUESTFROM || ""));
        form.getTextField('date').setText(dayjs(submission.response.updated).format("MM/DD/YYYY"));
        form.getTextField('plantControllerApproval').setText((submission.response.VRF_PLANTCONTROLLERAPPROVAL || "")); 

        form.getRadioGroup("companyCode").select(submission.response.VRF_COMPANYCODE);

        form.getRadioGroup("newChangeCopy").select(submission.response.VRF_NEWCHANGE);
        if(submission.response.VRF_NEWCHANGE==="COPY"){
           // form.getDropdown("copyVendorFromCompanyCode").select((submission.response.VRF_COPYVENDORFROM || ""))
            form.getTextField('copyVendorFromCompanyCode').setText((submission.response.VRF_COPYVENDORFROM || ""));
            form.getTextField('copyVendorFromNumber').setText((submission.response.VRF_COPYVENDORTO || ""));
        }
        
        // CHANGE CHECKBOX =====================================================================
        if(submission.response && typeof submission.response.VRF_CHANGING==="object" && submission.response.VRF_CHANGING.length>0){
            submission.response.VRF_CHANGING.forEach(function(changing){
                let fieldName="";
                switch(changing){
                    case "NAME":
                        fieldName='changingName';
                    break;
                    case "ADDRESS":
                        fieldName='changingAddress';                    
                    break;
                    case "TAXID":
                        fieldName='changingTaxID';
                    break;
                    case "PAYMENTTERMS":
                        fieldName='changingPaymentTerms';
                    break;
                    case "PAYMENTMETHOD":
                        fieldName='changinPaymentMethod';
                    break;
                    case "BANK":
                        fieldName='changingBankAccount';
                    break;
                    case "UNBLOCK":
                        fieldName='changingUnblock';
                    break;
                    case "OTHER":
                        fieldName='changingOther';
                    break;
                }
                form.getCheckBox(fieldName).check();
            })
        }
        
            
        if(submission.response.VRF_VENDORTYPE){ form.getDropdown("vendorType").select((submission.response.VRF_VENDORTYPE || "")) }

        form.getTextField('nameOnW9').setText((submission.response.VRF_W9NAME || ""));
        form.getTextField('DBA').setText((submission.response.VRF_DBA || ""));
        form.getTextField('address').setText((submission.response.VRF_ADDRESS || ""));
        form.getTextField('city').setText((submission.response.VRF_CITY || ""));
        form.getTextField("state").setText((submission.response.VRF_STATE || ""))
        form.getTextField('zipCode').setText((submission.response.VRF_ZIP || ""));
        form.getTextField('country').setText((submission.response.VRF_COUNTRY || ""));
        if(submission.response.VRF_POBOXSTATE){ form.getRadioGroup("isPOBox").select(submission.response.VRF_POBOX); }
        form.getTextField('poBoxNumber').setText((submission.response.VRF_POBOXNUMBER || ""));
        if(submission.response.VRF_POBOXSTATE){ form.getDropdown("PO Box State").select((submission.response.VRF_POBOXSTATE || "")) }        
        form.getTextField('poBoxZipCode').setText((submission.response.VRF_POBOXZIP || ""));

        form.getTextField('telephone').setText((submission.response.VRF_TELEPHONE || ""));
        form.getTextField('fax').setText((submission.response.VRF_FAX || ""));
        form.getTextField('email').setText((submission.response.VRF_EMAIL || ""));
        form.getTextField('taxID').setText((submission.response.VRF_TAXID || ""));

        form.getRadioGroup("paymentMethod").select(submission.response.VRF_PAYMENTMETHOD);
        form.getDropdown("Terms of Payment_vcMUjLvd18yar*WsUUSHQw").select((submission.response.VRF_PAYMENTTERMS || ""))

        form.getTextField('assignedPayee').setText((submission.response.VRF_ASSIGNEDPAYEE || ""));
        form.getTextField('payeeTaxID').setText((submission.response.VRF_PAYEETAXID || ""));

        form.getCheckBox("requiredW9").check();
        form.getCheckBox("requiredInvoice").check();
        form.getCheckBox("requiredACH").check();

        form.getTextField('comments').setText((submission.response.VRF_COMMENTS || ""));
        form.getTextField('approvedBy').setText((submission.response.VRF_APPROVEDBY || ""));
        if(submission.response.VRF_APPROVED){
            form.getRadioGroup("approval").select(submission.response.VRF_APPROVED);
        }


        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
}