const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fontKit = require('@pdf-lib/fontkit');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/2024CrackerBarrelRebateSheet.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        pdfDoc.registerFontkit(fontKit);
        const form = pdfDoc.getForm();
                    
        let customFont = await pdfDoc.embedFont(fs.readFileSync(process.cwd()+"/static/Whisper-Regular.ttf"))
        form.getTextField('OPERATORSIGNATURE').setText(submission.response.OPERATORSIGNATURE);
        form.getTextField('OPERATORSIGNATURE').setFontSize(13);
        form.getTextField('OPERATORSIGNATURE').updateAppearances(customFont);

        form.getTextField('OPERATORCONTACT').setText((submission.response.OPERATORCONTACT || ""));
        form.getTextField('OPERATORNAME').setText((submission.response.OPERATORNAME || ""));        
        form.getTextField('RESTAURANTUNITS').setText((submission.response.RESTAURANTUNITS && submission.response.RESTAURANTUNITS.toString() || "0"));
        form.getTextField('ADDRESS').setText((submission.response.ADDRESS || ""));
        form.getTextField('CITYSTATEZIP').setText((submission.response.CITY || "")+", "+(submission.response.STATE || "")+" "+(submission.response.ZIP || ""));
        form.getTextField('PHONE').setText((submission.response.PHONE || ""));
        form.getTextField('EMAIL').setText((submission.response.EMAIL || ""));
        form.getTextField('SALESPERSON').setText((submission.response.SALESPERSON || ""));
        form.getTextField('DISTRIBUTORNAME').setText((submission.response.DISTRIBUTORNAME || ""));
        form.getTextField('DISTRIBUTORNUMBER').setText((submission.response.DISTRIBUTORNUMBER || ""));

        if(submission.response.FIRSTSHIPMENTDATE){
            form.getTextField('FIRSTSHIPMENTDATE').setText(dayjs(submission.response.FIRSTSHIPMENTDATE).format("MM/DD/YYYY") );
        }       

        if(submission.response["0002101"] && typeof Number(submission.response["0002101"])==="number"){
            form.getTextField('0002101QTY').setText((submission.response["0002101"].toString() || ""));
            form.getTextField('0002101REBATE').setText("$"+(Number(submission.response["0002101"]) * 3).toString());
        }

        if(submission.response["0161700"] && typeof Number(submission.response["0161700"])==="number"){
            form.getTextField('0161700QTY').setText((submission.response["0161700"].toString() || ""));
            form.getTextField('0161700REBATE').setText("$"+(Number(submission.response["0161700"]) * 3).toString());
        }

        if(submission.response["0042001"] && typeof Number(submission.response["0042001"])==="number"){
            form.getTextField('0042001QTY').setText((submission.response["0042001"].toString() || ""));
            form.getTextField('0042001REBATE').setText("$"+(Number(submission.response["0042001"]) * 3).toString());
        }
            
        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
}