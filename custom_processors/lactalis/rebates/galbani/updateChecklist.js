const mdb = require(process.cwd()+"/libs/mdb.js");
const _ = require("lodash");
const aws = require(process.cwd()+"/libs/aws.js");
const response = require(process.cwd()+"/libs/response.js");
const HB = require(process.cwd()+"/libs/handlebars.js");

const GALBANIMARKETINGTEAM=["655d50d1d6f3bc780f94d594", "65b9088bef38d02bb8518d86","67aa235cea4b46461eda6f5c"];

module.exports=async function(req,res){

    let submission = await mdb.client().collection("rebate_submissions").aggregate([
        {"$match":{"_id":mdb.objectId(req.params.rebateSubmissionId)}}, 
        {"$limit":1},       
        { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
        { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
        { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } },
      ]).toArray();     

      if(!submission || submission.length===0){
        response.fail(res);
        return;
      }

    submission = submission[0];
    submission.rebate = submission.rebate[0];
    submission.operator = submission.operator[0];
    submission.broker = submission.broker[0];

    
    //Switch To Galbani Rebate - Marks Bistro - Ready For Review 
    let to=[];
    let dataToSet={ "closed":false };
    let TEMPLATE="";
    let templateData={};

    // Calculate the current state of the checklist to know the status and action to take.

    if( // Step 6b ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && submission.checklist.bankingInformationComplete 
        && submission.checklist.w9InformationComplete  
        && submission.checklist.brokerReviewComplete 
        && submission.checklist.financeReviewComplete
        && submission.checklist.marketingReviewComplete
        && submission.checklist.paymentComplete        
    ){
        dataToSet.status = "Closed / Won";
        dataToSet.closed = true;
        to.push(submission.broker.email);        
        if(submission.collaborators.length>0){
            to = to.concat(submission.collaborators);
        }
        TEMPLATE="checklistUpdateWon";

    }else if( // Step 6a ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && submission.checklist.bankingInformationComplete 
        && submission.checklist.w9InformationComplete  
        && submission.checklist.brokerReviewComplete 
        && submission.checklist.financeReviewComplete
        && submission.checklist.marketingReviewComplete
    ){
        dataToSet.status = "Payable";

        let formFlowSubmissions = await mdb.client().collection("formflow_submissions").find({
            "_id":{"$in":submission.formflow_submissions}}).toArray();

        templateData.FIRSTSHIPMENTDATE="";
    
        if(formFlowSubmissions && formFlowSubmissions.length>0){            

            let vrfSubmission = _.find(formFlowSubmissions, {"formflow_id": mdb.objectId("65635cb907f9811b593897c7")}); 
            
            let operatorSubmission = _.find(formFlowSubmissions, {"formflow_id": mdb.objectId("65492626a60f9e2163b663e3")}); 

            if(!operatorSubmission){
                operatorSubmission = _.find(formFlowSubmissions, {"formflow_id": mdb.objectId("65b90a70ef38d02bb8518d8a")}); 
            }
            if(!operatorSubmission){
                operatorSubmission = _.find(formFlowSubmissions, {"formflow_id": mdb.objectId("6631906ef36536520569a984")}); 
            }

            if(vrfSubmission && vrfSubmission.response && typeof vrfSubmission.response.VRF_VENDORNUMBER!=="undefined"){
                templateData.VENDORNUMBER=vrfSubmission.response.VRF_VENDORNUMBER;
            }else{
                templateData.VENDORNUMBER="-";
            }

            if(operatorSubmission && operatorSubmission.response && typeof operatorSubmission.response.DISTRIBUTORNAME!=="undefined"){
                templateData.DISTRIBUTORNAME=operatorSubmission.response.DISTRIBUTORNAME;
            }else{
                templateData.DISTRIBUTORNAME="-";
            }

            if(operatorSubmission && operatorSubmission.response && typeof operatorSubmission.response.FIRSTSHIPMENTDATE!=="undefined"){
                templateData.FIRSTSHIPMENTDATE=operatorSubmission.response.FIRSTSHIPMENTDATE;
            }else{
                templateData.FIRSTSHIPMENTDATE="";
            }
        }

        to=req.ORG.server.config.contacts.rebateTeams.payment;                  
        TEMPLATE="checklistUpdatePayable";

    }else if( // Step 5 ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && submission.checklist.bankingInformationComplete 
        && submission.checklist.w9InformationComplete  
        && submission.checklist.brokerReviewComplete 
        && submission.checklist.financeReviewComplete 
    ){
        if(GALBANIMARKETINGTEAM.indexOf(submission.rebate_id.toString())>=0){
            to = req.ORG.server.config.contacts.rebateTeams.marketingGalbani;
        }else{
            to = req.ORG.server.config.contacts.rebateTeams.marketingPCB;
        }
        
        dataToSet.status = "Pending Marketing Review";
        TEMPLATE="checklistUpdatePendingMarketing";


    }else if( // Step 4 ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && submission.checklist.bankingInformationComplete 
        && submission.checklist.w9InformationComplete  
        && submission.checklist.brokerReviewComplete 
        && !submission.checklist.financeReviewComplete 
    ){
        dataToSet.status = "Pending Finance Review";
        dataToSet.dataCollectionComplete = new Date();
        to = req.ORG.server.config.contacts.rebateTeams.finance; 
        TEMPLATE="checklistUpdatePendingFinance";

    }else if( // Step3 ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && submission.checklist.bankingInformationComplete 
        && submission.checklist.w9InformationComplete  
        && !submission.checklist.brokerReviewComplete
    ){
        dataToSet.status = "Pending Sales/Broker Review";
        dataToSet.dataCollectionComplete = new Date();
        to = [submission.broker.email];
        TEMPLATE="checklistUpdatePendingBrokerReview";
    
    }else if( // Step 2 ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.proofOfPurchaseComplete
        && !submission.checklist.bankingInformationComplete 
        && !submission.checklist.w9InformationComplete  
        && !submission.checklist.brokerReviewComplete    
    ){
        dataToSet.status = "Pending Data Collection";
        dataToSet.dataCollectionComplete = new Date();
        to = [submission.broker.email];
        TEMPLATE="checklistUpdatePoPComplete";

    }else if( // Step 1 ----------------------------------------
        submission.checklist.operatorRegistrationComplete 
        && !submission.checklist.proofOfPurchaseComplete 
    ){
        // Lauras grid says "No notification required" for this step. 
        dataToSet.status = "Pending Proof of Purchase"; 
        TEMPLATE="";

    }else if(submission.checklist.closedLost){ // Step 5c ----------------------------------------
        dataToSet.status = "Closed / Lost";
        dataToSet.closed = true;
        if(GALBANIMARKETINGTEAM.indexOf(submission.rebate_id.toString())>=0){
            to = req.ORG.server.config.contacts.rebateTeams.marketingGalbani;
        }else{
            to = req.ORG.server.config.contacts.rebateTeams.marketingPCB;
        }
        
        TEMPLATE="checklistUpdateLost";

    }else if(
        typeof req.body.denied!=="undefined"
    ){        
        dataToSet.status = "Changes Required";
        to.push(submission.broker.email);
        TEMPLATE="checklistUpdateDenied";

    }else{
        dataToSet.status = "Pending Data Collection";        
    }
    

    await mdb.client().collection("rebate_submissions").updateOne({
        "_id":mdb.objectId(req.params.rebateSubmissionId),
        "org_id":req.SESSION.org_id
    },{
        "$set":dataToSet
    });    

    templateData = Object.assign(templateData, {
        "REBATENAME":submission.rebate.name,
        "REBATEID":submission.rebate._id.toString(),
        "OPERATORNAME":((submission.operator && submission.operator.name) ? submission.operator.name : "(Operator name not set)"),
        "BROKERNAME":submission.broker.fields.FNAME + " " + submission.broker.fields.LNAME,
        "BROKEREMAIL":submission.broker.email,
        "STATUS":dataToSet.status,
        "SUBMISSIONID":req.params.rebateSubmissionId,
        "COMMENT":((req.body.comment) ? req.body.comment : ""),
        "CREATED":submission.created.toString(),
    });

    if(typeof submission.meta==="object"){
        templateData.META = submission.meta;
    }

    let subject = "";
    if(typeof req.ORG.server.templates.rebate[TEMPLATE]!=="undefined" && typeof req.ORG.server.templates.rebate[TEMPLATE].subject=="string"){
        subject =HB(req.ORG.server.templates.rebate[TEMPLATE].subject,templateData);
    }

    // Send all emails to the owner of this submission if they are a "rebate tester"
    if(submission.broker.tags.indexOf("rebateTester")>=0){
        to=[submission.broker.email];
        subject = "REBATE TESTER: " + subject;        
    }

    if(to.length>0 && TEMPLATE && TEMPLATE.length>0){
        aws.email({
            "to": ((process.env.ISDEV) ? "<EMAIL>"  : to),
            "from": `${req.ORG.server.templates.rebate[TEMPLATE].from} <${req.ORG.server.config.contacts.from}>`,
            "subject": subject,
            "body":HB(req.ORG.server.templates.rebate[TEMPLATE].body,templateData)
        }).then(r=>{                
        }).catch(e=>{
            console.log("AWS EMAIL error",e)        
        });             
    }    
    

    response.success(res, dataToSet);
}