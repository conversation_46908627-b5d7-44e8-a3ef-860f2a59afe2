const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fontKit = require('@pdf-lib/fontkit');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/switchtogalbani_registration.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        pdfDoc.registerFontkit(fontKit);
        const form = pdfDoc.getForm();
        /*const page = pdfDoc.getPage(1);

        const pngImage = await pdfDoc.embedPng(Buffer.from(submission.response.OPERATORSIGNATURE.replace("data:image/png;base64,",""), 'base64'));
        const pngDims = pngImage.scale(0.25);        
        page.drawImage(pngImage, {
            x: 320,
            y: 215,
            width: pngDims.width,
            height: pngDims.height,
          })
        */                  
        let customFont = await pdfDoc.embedFont(fs.readFileSync(process.cwd()+"/static/Whisper-Regular.ttf"))
        form.getTextField('operatorSignature').setText(submission.response.OPERATORSIGNATURE);
        form.getTextField('operatorSignature').setFontSize(20);
        form.getTextField('operatorSignature').updateAppearances(customFont);

        form.getTextField('operatorContact').setText((submission.response.OPERATORCONTACT || ""));
        form.getTextField('operatorName').setText((submission.response.OPERATORNAME || ""));        
        form.getTextField('numberOfRestaurants').setText((submission.response.RESTAURANTUNITS && submission.response.RESTAURANTUNITS.toString() || "0"));
        form.getTextField('address').setText((submission.response.ADDRESS || ""));
        form.getTextField('cityStateZip').setText((submission.response.CITY || "")+", "+(submission.response.STATE || "")+" "+(submission.response.ZIP || ""));
        form.getTextField('phone').setText((submission.response.PHONE || ""));
        form.getTextField('email').setText((submission.response.EMAIL || ""));
        form.getTextField('lactalisSalesPerson').setText((submission.response.SALESPERSON || ""));
        form.getTextField('distributorName').setText((submission.response.DISTRIBUTORNAME || ""));
        form.getTextField('distributorNumber').setText((submission.response.DISTRIBUTORNUMBER || ""));
        if(submission.response.FIRSTSHIPMENTDATE){
            form.getTextField('firstShipmentDate').setText(dayjs(submission.response.FIRSTSHIPMENTDATE).format("MM/DD/YYYY") );
        }        
        if(submission.response["0001021"] && typeof Number(submission.response["0001021"])==="number"){
            form.getTextField('0001021Qty').setText((submission.response["0001021"].toString() || ""));
            form.getTextField('0001021Rebate').setText("$"+(Number(submission.response["0001021"]) * 5).toString());
        }
        if(submission.response["0002021"] && typeof Number(submission.response["0002021"])==="number"){
            form.getTextField('0002021Qty').setText((submission.response["0002021"].toString() || ""));
            form.getTextField('0002021Rebate').setText("$"+(Number(submission.response["0002021"]) * 5).toString());
        }
        if(submission.response["0160615"] && typeof Number(submission.response["0160615"])==="number"){
            form.getTextField('0160615Qty').setText((submission.response["0160615"].toString() || ""));
            form.getTextField('0160615Rebate').setText("$"+(Number(submission.response["0160615"]) * 5).toString());
        }
        if(submission.response["0160625"] && typeof Number(submission.response["0160625"])==="number"){
            form.getTextField('0160625Qty').setText((submission.response["0160625"].toString() || ""));
            form.getTextField('0160625Rebate').setText("$"+(Number(submission.response["0160625"]) * 5).toString());
        }
        if(submission.response["0160645"] && typeof Number(submission.response["0160645"])==="number"){
            form.getTextField('0160645Qty').setText((submission.response["0160645"].toString() || ""));
            form.getTextField('0160645Rebate').setText("$"+(Number(submission.response["0160645"]) * 5).toString());
        }
        if(submission.response["0160795"] && typeof Number(submission.response["0160795"])==="number"){
            form.getTextField('0160795Qty').setText((submission.response["0160795"].toString() || ""));
            form.getTextField('0160795Rebate').setText("$"+(Number(submission.response["0160795"]) * 5).toString());
        }
        if(submission.response["0160695"] && typeof Number(submission.response["0160695"])==="number"){
            form.getTextField('0160695Qty').setText((submission.response["0160695"].toString() || ""));
            form.getTextField('0160695Rebate').setText("$"+(Number(submission.response["0160695"]) * 5).toString());
        }
        if(submission.response["0160725"] && typeof Number(submission.response["0160725"])==="number"){
            form.getTextField('0160725Qty').setText((submission.response["0160725"].toString() || ""));
            form.getTextField('0160725Rebate').setText("$"+(Number(submission.response["0160725"]) * 5).toString());
        }
        if(submission.response["0160910"] && typeof Number(submission.response["0160910"])==="number"){
            form.getTextField('0160910Qty').setText((submission.response["0160910"].toString() || ""));
            form.getTextField('0160910Rebate').setText("$"+(Number(submission.response["0160910"]) * 5).toString());
        }
        if(submission.response["0160922"] && typeof Number(submission.response["0160922"])==="number"){
            form.getTextField('0160922Qty').setText((submission.response["0160922"].toString() || ""));
            form.getTextField('0160922Rebate').setText("$"+(Number(submission.response["0160922"]) * 5).toString());
        }
        



        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
}