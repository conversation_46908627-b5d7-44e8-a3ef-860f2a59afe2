const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fontKit = require('@pdf-lib/fontkit');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/bankinginformationformcompanyv6.2.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        pdfDoc.registerFontkit(fontKit);
        const form = pdfDoc.getForm();
        const page = pdfDoc.getPage(0);

        form.getTextField('companyName').setText((submission.response.ACH_COMPANYNAME || ""));
        form.getTextField('street').setText((submission.response.ACH_STREET || ""));
        form.getTextField('cityStateZip').setText((submission.response.ACH_CITY +", "+submission.response.ACH_STATE + " " + submission.response.ACH_ZIP || ""));
        form.getTextField('phone').setText((submission.response.ACH_PHONE || ""));
        
        // SIGNATURE --- vendorSignature -- ACH_VENDORSIGNATURE
        let customFont = await pdfDoc.embedFont(fs.readFileSync(process.cwd()+"/static/Whisper-Regular.ttf"))
        form.getTextField('vendorSignature').setText(submission.response.ACH_VENDORSIGNATURE);
        form.getTextField('vendorSignature').setFontSize(12);
        form.getTextField('vendorSignature').updateAppearances(customFont);

        form.getTextField('printName').setText((submission.response.ACH_VENDORNAME || ""));
        form.getTextField('title').setText((submission.response.ACH_VENDORTITLE || ""));
        form.getTextField('directPhone').setText((submission.response.ACH_VENDORPHONE || ""));

        form.getTextField('previousBankName').setText((submission.response.ACH_PREVIOUSBANKNAME || ""));
        form.getTextField('previousBankStreet').setText((submission.response.ACH_PREVIOUSBANKSTREET || ""));
        form.getTextField('previousBankCityStateZip').setText((submission.response.ACH_PREVIOUSBANKCITY + ", " + submission.response.ACH_PREVIOUSBANKSTATE + " " + submission.response.ACH_PREVIOUSBANKZIP || ""));
        form.getTextField('previousBankRouting').setText((submission.response.ACH_PREVIOUSBANKROUTING || ""));
        form.getTextField('previousBankAccountNumber').setText((submission.response.ACH_PREVIOUSBANKACCOUNTNUMBER || ""));
        form.getTextField('previousBankEmail').setText((submission.response.ACH_PREVIOUSBANKEMAILREMITTANCE || ""));
        
        form.getTextField('newBankName').setText((submission.response.ACH_NEWBANKNAME || ""));
        form.getTextField('newBankStreet').setText((submission.response.ACH_NEWBANKSTREET || ""));
        form.getTextField('newBankCityStateZip').setText((submission.response.ACH_NEWBANKCITY + ", " + submission.response.ACH_NEWBANKSTATE + " " + submission.response.ACH_NEWBANKZIP  || ""));
        form.getTextField('newBankRouting').setText((submission.response.ACH_NEWBANKROUTING || ""));
        form.getTextField('newBankAccountNumber').setText((submission.response.ACH_NEWBANKACCOUNTNUMBER || ""));
        form.getTextField('newBankEmail').setText((submission.response.ACH_NEWBANKEMAILREMITTANCE || ""));

        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
};