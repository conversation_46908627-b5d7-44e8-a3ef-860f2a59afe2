const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fontKit = require('@pdf-lib/fontkit');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/fresh_mozz_2025.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        pdfDoc.registerFontkit(fontKit);
        const form = pdfDoc.getForm();
                    
        let customFont = await pdfDoc.embedFont(fs.readFileSync(process.cwd()+"/static/Whisper-Regular.ttf"))
        form.getTextField('Operator Signature').setText(submission.response.OPERATORSIGNATURE);
        form.getTextField('Operator Signature').setFontSize(13);
        form.getTextField('Operator Signature').updateAppearances(customFont);

        form.getTextField('Operator Contact').setText((submission.response.OPERATORCONTACT || ""));
        form.getTextField('Operator Name').setText((submission.response.OPERATORNAME || ""));        
        form.getTextField('Number of Restaurant Units').setText((submission.response.RESTAURANTUNITS && submission.response.RESTAURANTUNITS.toString() || "0"));
        form.getTextField('Address').setText((submission.response.ADDRESS || ""));
        form.getTextField('City State Zip').setText((submission.response.CITY || "")+", "+(submission.response.STATE || "")+" "+(submission.response.ZIP || ""));
        form.getTextField('Phone').setText((submission.response.PHONE || ""));
        form.getTextField('Email').setText((submission.response.EMAIL || ""));
        form.getTextField('Lactalis Culinary Salesperson').setText((submission.response.SALESPERSON || ""));
        form.getTextField('Distributor Name').setText((submission.response.DISTRIBUTORNAME || ""));
        form.getTextField('Distributor Number').setText((submission.response.DISTRIBUTORNUMBER || ""));

        if(submission.response.FIRSTSHIPMENTDATE){
            form.getTextField('First Shipment Date').setText(dayjs(submission.response.FIRSTSHIPMENTDATE).format("MM/DD/YYYY") );
        }       

        if(submission.response["0081465"] && typeof Number(submission.response["0081465"])==="number"){
            form.getTextField('0081465 Quantity').setText((submission.response["0081465"].toString() || ""));
            form.getTextField('0081465 Rebate').setText("$"+(Number(submission.response["0081465"]) * 10).toString());
        }

        if(submission.response["0081405"] && typeof Number(submission.response["0081405"])==="number"){            
            pdfDoc
            form.getTextField('0081405 Quantity').setAlignment(1)
            form.getTextField('0081405 Quantity').setText((submission.response["0081405"].toString() || ""));
            form.getTextField('0081405 Rebate').setText("$"+(Number(submission.response["0081405"]) * 10).toString());
        }

        if(submission.response["5004000"] && typeof Number(submission.response["5004000"])==="number"){
            form.getTextField('5004000 Quantity').setText((submission.response["5004000"].toString() || ""));
            form.getTextField('5004000 Rebate').setText("$"+(Number(submission.response["5004000"]) * 5).toString());
        }

        if(submission.response["0081400"] && typeof Number(submission.response["0081400"])==="number"){
            form.getTextField('0081400 Quantity').setText((submission.response["0081400"].toString() || ""));
            form.getTextField('0081400 Rebate').setText("$"+(Number(submission.response["0081400"]) * 5).toString());
        }

        if(submission.response["0081615"] && typeof Number(submission.response["0081615"])==="number"){
            form.getTextField('0081615 Quantity').setText((submission.response["0081615"].toString() || ""));
            form.getTextField('0081615 Rebate').setText("$"+(Number(submission.response["0081615"]) * 5).toString());
        }

        if(submission.response["0081635"] && typeof Number(submission.response["0081635"])==="number"){
            form.getTextField('0081635 Quantity').setText((submission.response["0081635"].toString() || ""));
            form.getTextField('0081635 Rebate').setText("$"+(Number(submission.response["0081635"]) * 5).toString());
        }

        if(submission.response["0081710"] && typeof Number(submission.response["0081710"])==="number"){
            form.getTextField('0081710 Quantity').setText((submission.response["0081710"].toString() || ""));
            form.getTextField('0081710 Rebate').setText("$"+(Number(submission.response["0081710"]) * 5).toString());
        }
            
        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
}