const _ = require("lodash");

module.exports=function(req){
    
    let match=[{}];
    let accountTagsWhoSeeAll=["rebateManager"], //  Just set this to the new rebate mgr tag    
        accountTagsWhoSeeZone={
            "68a878da52bf9bb245336cf5":["WA", "OR", "ID", "MT", "WY", "UT", "CO", "NE", "SD", "ND"], //<PERSON>.<PERSON>@us.lactalis.com
            "645c41f6a4832c507ed82d09":["CA", "NV", "AZ"], //<EMAIL>
            "645c41f6a4832c507ed82d0a":["NM", "TX", "OK", "AR", "LA"], //<EMAIL>
            "65cf692774905967286e22f8":["MS", "AL", "GA", "FL"], // <EMAIL>
            "645c41f7a4832c507ed82d1c":["KS", "MO", "IA", "MN", "IL", "WI"], // <EMAIL>
            "645c41f7a4832c507ed82d1b":["NY", "NJ", "VT", "NH", "MA", "CT", "RI", "ME", "PA"], // <EMAIL>
            "66901cd43ddf1229dba8b8b4":["MI", "IN", "OH", "PA", "WV", "DE"], //<EMAIL>
            "66901cb13ddf1229dba8b8b3":["KY", "TN", "SC", "NC", "VA", "MD"] //<EMAIL>
        };

    // ALL REQUESTS -------------------------------------
    if(req.SESSION && req.SESSION.tags && accountTagsWhoSeeAll.length>0 && _.intersection(req.SESSION.tags,accountTagsWhoSeeAll).length>0){
        match = [{
            "org_id":req.SESSION.org_id
        }];

    // ZONE REQUESTS -------------------------------------
    }else if(req.SESSION && req.SESSION.account_id && typeof accountTagsWhoSeeZone[req.SESSION.account_id.toString()]!=="undefined"){

        // "broker.0.fields.STATE":{"$in":accountTagsWhoSeeZone[req.SESSION.account_id.toString()]}
        match = [{
            "org_id":req.SESSION.org_id
        },{                      
            "$or":[
                {"operator.0.state":{"$in":accountTagsWhoSeeZone[req.SESSION.account_id.toString()]}},
                {"account_id":req.SESSION.account_id}
            ]                       
        }];

    // JUST MY REQUESTS -----------------------------------
    }else{
        
        match = {
            "org_id":req.SESSION.org_id
        };

        if(req.SESSION && req.SESSION.account_id){
            match.account_id = req.SESSION.account_id;
        }else{
            match.collaborators = req.SESSION.email;
        }
        match=[match];
    }
        
    return match;
}