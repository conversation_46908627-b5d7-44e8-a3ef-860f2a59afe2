const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");


module.exports={
/*****************************************
    AFTER SAVE
*****************************************/
    afterSave:async function(req, res){

        
        //Just update the updated date
        await mdb.client().collection("rebate_submissions").updateOne({
            "formflow_submissions":mdb.objectId(req.params.id),
            "org_id":req.SESSION.org_id
        },{
            "$set":{                
                "updated":new Date(),
                "updated_by":req.SESSION.email
            }
        });

        response.success(res);
    }
}