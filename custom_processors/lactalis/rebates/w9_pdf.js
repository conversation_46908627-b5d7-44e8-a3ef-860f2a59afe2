const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const dayjs = require("dayjs");
const _ = require("lodash");
const pdf = require('pdf-lib');
const fs = require('fs');

const TEMPLATE = "/templates/lactalis/rebates/w9template.pdf"

module.exports=async function(submission){
    try{
        const pdfDoc = await pdf.PDFDocument.load(fs.readFileSync(process.cwd()+TEMPLATE));
        const form = pdfDoc.getForm();                

        form.getTextField('topmostSubform[0].Page1[0].f1_1[0]').setText((submission.response.W9_NAME || ""));
        form.getTextField('topmostSubform[0].Page1[0].f1_2[0]').setText((submission.response.W9_BUSINESSNAME || ""));

        let LLCTaxClass="";
        switch(submission.response.W9_TAXCLASSIFICATION){
            case "I":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[0]').check();                
            break;
            case "C":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[1]').check();
            break;
            case "S":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[2]').check();
            break;
            case "P":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[3]').check();
            break;
            case "T":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[4]').check();
            break;
            case "LLC":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[5]').check();
                LLCTaxClass = (submission.response.W9_LLCTAXCLASS || "");
            break;
            case "OTHER":
                pdfDoc.getForm().getCheckBox('topmostSubform[0].Page1[0].FederalClassification[0].c1_1[6]').check();
            break;
        }

        pdfDoc.getForm().getTextField('topmostSubform[0].Page1[0].FederalClassification[0].f1_3[0]').setText(LLCTaxClass); 
        pdfDoc.getForm().getTextField('topmostSubform[0].Page1[0].FederalClassification[0].f1_4[0]').setText((submission.response.W9_OTHERTAXCLASS || "")); 

        form.getTextField('topmostSubform[0].Page1[0].Address[0].f1_7[0]').setText((submission.response.W9_STREET || ""));
        form.getTextField('topmostSubform[0].Page1[0].Address[0].f1_8[0]').setText((submission.response.W9_CITY +", "+submission.response.W9_STATE + " " + submission.response.W9_ZIP || ""));
        form.getTextField('topmostSubform[0].Page1[0].f1_10[0]').setText((submission.response.W9_ACCOUNTNUMBERS || ""));

        form.getTextField('topmostSubform[0].Page1[0].Exemptions[0].f1_5[0]').setText((submission.response.W9_EXEMPTPAYEECODE || ""));
        form.getTextField('topmostSubform[0].Page1[0].Exemptions[0].f1_6[0]').setText((submission.response.W9_EXEMPTFATCA || ""));
          
        if(!submission.response.W9_EIN || submission.response.W9_EIN.length<5){
            let SSN = submission.response.W9_SSN.replace(/[^0-9]/ig,"").toString().padStart(9, '0');
            let SSN0=SSN.substr(0,3), 
                SSN1=SSN.substr(3,2), 
                SSN2=SSN.substr(5,4);
            form.getTextField('topmostSubform[0].Page1[0].SSN[0].f1_11[0]').setText((SSN0 || ""));
            form.getTextField('topmostSubform[0].Page1[0].SSN[0].f1_12[0]').setText((SSN1 || ""));
            form.getTextField('topmostSubform[0].Page1[0].SSN[0].f1_13[0]').setText((SSN2 || ""));
        }
        
        if(!submission.response.W9_SSN || submission.response.W9_SSN.length<5){
            let EIN = submission.response.W9_EIN.replace(/[^0-9]/ig,"").toString();
            let EIN0=EIN.substr(0,2), 
                EIN1=EIN.substr(2,7);            
            form.getTextField('topmostSubform[0].Page1[0].EmployerID[0].f1_14[0]').setText((EIN0 || ""));
            form.getTextField('topmostSubform[0].Page1[0].EmployerID[0].f1_15[0]').setText((EIN1 || ""));
        }

        form.getTextField('date').setText(dayjs(submission.response.updated).format("MM/DD/YYYY"));

        const page = pdfDoc.getPage(0);
        const pngImage = await pdfDoc.embedPng(Buffer.from(submission.response.W9_CERTSIGNATURE.replace("data:image/png;base64,",""), 'base64'));
        const pngDims = pngImage.scale(0.25);        
        page.drawImage(pngImage, {
            x: 130,
            y: 220,
            width: pngDims.width,
            height: pngDims.height,
        })
        
        return await pdfDoc.save();
    }catch(e){
        console.log(e);
        return e.toString();
    }
};