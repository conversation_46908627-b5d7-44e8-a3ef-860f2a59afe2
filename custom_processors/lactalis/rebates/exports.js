const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");
const _ = require("lodash");
const dayjs = require("dayjs");
dayjs.extend(require('dayjs/plugin/relativeTime'));
const ExcelJS = require('exceljs');
const common = require(process.cwd()+"/libs/common.js");



module.exports=async function(req, res){
    try{

        

        const SKUS=[
            {"id":"0001021", "aev":80, "amt":5},
            {"id":"0002021", "aev":80, "amt":5},
            {"id":"0160615", "aev":60, "amt":5},
            {"id":"0160625", "aev":60, "amt":5},
            {"id":"0160645", "aev":60, "amt":5},
            {"id":"0160795", "aev":60, "amt":5},
            {"id":"0160695", "aev":60, "amt":5},
            {"id":"0160725", "aev":60, "amt":5},
            {"id":"0160910", "aev":60, "amt":5},
            {"id":"0160922", "aev":60, "amt":5},

            {"id":"0002101", "aev":40, "amt":3},
            {"id":"0161700", "aev":80, "amt":3},
            {"id":"0042001", "aev":64, "amt":3},

            //2025 SKUS

            {"id":"0081465", "aev":24, "amt":10},
            {"id":"0081405", "aev":24, "amt":10},
            
            {"id":"0081710", "aev":24, "amt":5},
            {"id":"0081635", "aev":24, "amt":5},
            {"id":"0081615", "aev":24, "amt":5},
            {"id":"0081400", "aev":24, "amt":5},
            {"id":"5004000", "aev":24, "amt":5}

        ];

        let sheetsToReturn={
            "raw":{
                "name":"Raw Data",
                "data":[]
            },
            "summaries":{}
        };
        let categorySummaries={};

        let rebateDetails = {};
        let rebates = await mdb.client().collection("rebates").find({"org_id":req.SESSION.org_id}).toArray()
        rebates.forEach(function(rebate){
            rebateDetails[rebate._id.toString()]=rebate;
            
        });
    
        async function exportOperatorFormData(){
            let dataToReturn=[], obj={};
    
            let match={
                "org_id":req.SESSION.org_id,                
                "rebate_id":{"$in":[]}
            };
    
            if(typeof req.body.rebate_ids==="string"){
                req.body.rebate_ids=[req.body.rebate_ids];
            }
    
            for(let rebateId of req.body.rebate_ids){
                match.rebate_id["$in"].push(mdb.objectId(rebateId));

                let sheetRebate = _.find(rebates,{"_id":mdb.objectId(rebateId.toString())});
                sheetsToReturn.summaries[rebateId.toString()]={
                    "name":sheetRebate.name,                    
                    "data":[]
                };
                categorySummaries[rebateId.toString()]={
                    "anticipated":{},
                    "total_possible":{},
                    "ltd_payment":{
                        "value":0,
                        "label":`Current WON launch_to_date_payment`
                    },                                      
                };

                categorySummaries[rebateId.toString()].anticipated[dayjs().format("YYYY").toString()]={
                    "value":0,
                    "label":`Current WON anticipated_volume_lbs (${dayjs().format("YYYY").toString()})`
                };
                categorySummaries[rebateId.toString()].anticipated[dayjs().add(1,"year").format("YYYY").toString()]={
                    "value":0,
                    "label":`Current WON anticipated_volume_lbs (${dayjs().add(1,"year").format("YYYY").toString()})`
                };
                categorySummaries[rebateId.toString()].total_possible[dayjs().format("YYYY").toString()]={
                    "value":0,
                    "label":`Total possible anticipated_ytg_volume_lbs (${dayjs().format("YYYY").toString()})`
                };
                categorySummaries[rebateId.toString()].total_possible[dayjs().add(1,"year").format("YYYY").toString()]={
                    "value":0,
                    "label":`Total possible anticipated_ytg_volume_lbs (${dayjs().add(1,"year").format("YYYY").toString()})`
                };                
                
            }

            Object.keys(categorySummaries).forEach((rebateId)=>{
                sheetsToReturn.summaries[rebateId.toString()].data = categorySummaries[rebateId];
            })
            
            if(typeof req.body.createdFrom==="string"){  req.body.createdFrom=dayjs(req.body.createdFrom).toDate();  }
            if(typeof req.body.createdTo==="string"){  req.body.createdTo=dayjs(req.body.createdTo).add(1,"day").toDate();  }
    
            if(typeof req.body.createdFrom!=="undefined" || typeof req.body.createdTo!=="undefined"){  match["$and"]=[];  }
            
            if(typeof req.body.createdFrom!=="undefined"){  match["$and"].push({"created":{"$gte":req.body.createdFrom}});  }
            if(typeof req.body.createdTo!=="undefined"){  match["$and"].push({"created":{"$lt":req.body.createdTo}});  }
    
            let rebateSubmissions = await mdb.client().collection("rebate_submissions").aggregate([
                {"$match":match},
                {"$lookup":{
                    "from":"formflow_submissions",
                    "localField":"_id",
                    "foreignField":"rebatesubmission_id",
                    "as":"forms"
                }},
                {"$lookup":{
                    "from":"accounts",
                    "localField":"account_id",
                    "foreignField":"_id",
                    "as":"owner"
                }}
            ]).toArray();

            
    
            for(let rebateSubmission of rebateSubmissions){
                let TOTALS={}, CUTS={};
                const YRS={
                    "CURRENT":dayjs().format("YYYY").toString(),
                    "NEXT":dayjs().add(1,"year").format("YYYY").toString(),
                    "PREVIOUS":dayjs().add(-1,"year").format("YYYY").toString()
                };

                CUTS[YRS.PREVIOUS]=0;
                CUTS[YRS.CURRENT]=0;
                CUTS[YRS.NEXT]=0;

                rebates.forEach(function(rebate){
                    TOTALS[rebate._id.toString()]={
                        "cases":0,
                        "aev":0
                    };                                           
                });

                obj={};
                obj.id=rebateSubmission._id.toString();
                obj.rebate=rebateDetails[rebateSubmission.rebate_id.toString()].name;
                obj.date_created = dayjs(rebateSubmission.created).format("MM-DD-YYYY");
                obj.submission_age_days = dayjs().diff(dayjs(rebateSubmission.created),"day");
                obj.owner = rebateSubmission.owner[0].fields.FNAME+" "+rebateSubmission.owner[0].fields.LNAME;
                obj.owner_email = rebateSubmission.owner[0].email;
                obj.days_with_owner = dayjs().diff(dayjs(rebateSubmission.created),"day");
                obj.status = rebateSubmission.status;            
    
                obj.operator_name="";
                obj.operator_contact="";
                obj.address="";
                obj.city="";
                obj.state="";
                obj.zip="";
                obj.operator_email="";
                obj.restaurant_units="";
                obj.distributor_name="";
                obj.first_shipment_date="";
    
                obj.verified_payment_amount = ((rebateSubmission.meta && typeof rebateSubmission.meta.paymentAmount !=="undefined") ? Number(rebateSubmission.meta.paymentAmount) : "");
                if(rebateSubmission.status==="Closed / Won"){                    
                    categorySummaries[rebateSubmission.rebate_id.toString()].ltd_payment.value += obj.verified_payment_amount;
                }

                SKUS.forEach(function(sku){
                    obj[sku.id]="";
                    obj[sku.id+"_annual_estimated_volume_lbs"] = "";
                    obj[sku.id+"_anticipated_ytg_volume_lbs"] = "";
                    obj[sku.id+"_estimated_payment"] = "";                    
                });
            
                //Now lets grab the form data to fill out the rest of the fields.
    
                let response = _.find(rebateSubmission.forms, {"formflow_id":mdb.objectId("65492626a60f9e2163b663e3")});
                    if(!response){
                        response = _.find(rebateSubmission.forms, {"formflow_id":mdb.objectId("65b90a70ef38d02bb8518d8a")})
                    }
                    if(!response){
                        response = _.find(rebateSubmission.forms, {"formflow_id":mdb.objectId("6631906ef36536520569a984")})
                    }
                    if(!response){
                        response = _.find(rebateSubmission.forms, {"formflow_id":mdb.objectId("67aa280fea4b46461eda6f5f")})
                    }


                if(response && typeof response.response==="object" && Object.keys(response.response).length>0){
                    //console.log(response.response,"----");
                    obj.operator_name = response.response.OPERATORNAME;
                    obj.operator_contact = response.response.OPERATORCONTACT;
                    obj.address = response.response.ADDRESS;
                    obj.city = response.response.CITY;
                    obj.state = response.response.STATE;
                    obj.zip = response.response.ZIP;
                    obj.operator_email = response.response.EMAIL;
                    obj.restaurant_units = response.response.RESTAURANTUNITS;
                    obj.distributor_name = response.response.DISTRIBUTORNAME;
                    obj.first_shipment_date = response.response.FIRSTSHIPMENTDATE;                    
    
                    SKUS.forEach(function(sku){
                        if(typeof response.response[sku.id]!=="undefined"){                        
                            obj[sku.id]=response.response[sku.id];
                            obj[sku.id+"_annual_estimated_volume_lbs"]=response.response[sku.id]*sku.aev;
                            obj[sku.id+"_anticipated_ytg_volume_lbs"]= Math.round(((response.response[sku.id]*sku.aev)/12) * (12-Number(dayjs(response.response.FIRSTSHIPMENTDATE).format("M"))));
                            obj[sku.id+"_estimated_payment"]=response.response[sku.id]*sku.amt;
                            
                            TOTALS[rebateSubmission.rebate_id.toString()].cases+=response.response[sku.id];
                            TOTALS[rebateSubmission.rebate_id.toString()].aev+=obj[sku.id+"_annual_estimated_volume_lbs"];
                        }                    
                    });

                    rebates.forEach(function(rebate){
                        obj[rebate.name+" Total_Cases"] = TOTALS[rebate._id.toString()].cases;
                        obj[rebate.name+" Total_annual_est_volume_lbs"] = TOTALS[rebate._id.toString()].aev;                        
                    });
                    
                }

                
                if(obj.first_shipment_date && obj.first_shipment_date.length>0){
                    if(typeof CUTS[dayjs(obj.first_shipment_date).format("YYYY")]==="number"){

                        if(obj[rebateDetails[rebateSubmission.rebate_id.toString()].name+" Total_annual_est_volume_lbs"]>0){

                            const FSD={
                                "YYYY":dayjs(obj.first_shipment_date).format("YYYY"),
                                "MM":((Number(dayjs(obj.first_shipment_date).format("D"))>=15) ? Number(dayjs(obj.first_shipment_date).format("M")) : ((Number(dayjs(obj.first_shipment_date).format("M"))>1) ? Number(dayjs(obj.first_shipment_date).add(-1,"month").format("M")) : 1))
                            }

                            

                            if(FSD.MM>1){
                                CUTS[dayjs(obj.first_shipment_date).format("YYYY")] = obj[rebateDetails[rebateSubmission.rebate_id.toString()].name+" Total_annual_est_volume_lbs"] * ((12-(FSD.MM-1))/12) 
                                CUTS[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")] = obj[rebateDetails[rebateSubmission.rebate_id.toString()].name+" Total_annual_est_volume_lbs"] * ((FSD.MM-1)/12)
                                
                            }else{
                                CUTS[dayjs(obj.first_shipment_date).format("YYYY")] = obj[rebateDetails[rebateSubmission.rebate_id.toString()].name+" Total_annual_est_volume_lbs"]; 
                                CUTS[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")] = 0;
                            }

                            
                            if(rebateSubmission.status==="Closed / Won"){
                                if(typeof categorySummaries[rebateSubmission.rebate_id.toString()].anticipated[dayjs(obj.first_shipment_date).format("YYYY")]==="undefined"){
                                    categorySummaries[rebateSubmission.rebate_id.toString()].anticipated[dayjs(obj.first_shipment_date).format("YYYY")]={
                                        "value":0,
                                        "label":`Current WON anticipated_volume_lbs (${dayjs(obj.first_shipment_date).format("YYYY")})`
                                    };
                                }
                                categorySummaries[rebateSubmission.rebate_id.toString()].anticipated[dayjs(obj.first_shipment_date).format("YYYY")].value += CUTS[dayjs(obj.first_shipment_date).format("YYYY")];
                                categorySummaries[rebateSubmission.rebate_id.toString()].anticipated[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")].value += CUTS[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")];
                            }
                            if(rebateSubmission.status!=="Closed / Lost"){
                                if(typeof categorySummaries[rebateSubmission.rebate_id.toString()].total_possible[dayjs(obj.first_shipment_date).format("YYYY")]==="undefined"){
                                    categorySummaries[rebateSubmission.rebate_id.toString()].total_possible[dayjs(obj.first_shipment_date).format("YYYY")]={
                                        "value":0,
                                        "label":`Total possible anticipated_ytg_volume_lbs (${dayjs(obj.first_shipment_date).format("YYYY")})`
                                    };
                                }
                                categorySummaries[rebateSubmission.rebate_id.toString()].total_possible[dayjs(obj.first_shipment_date).format("YYYY")].value += CUTS[dayjs(obj.first_shipment_date).format("YYYY")];
                                categorySummaries[rebateSubmission.rebate_id.toString()].total_possible[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")].value += CUTS[dayjs(obj.first_shipment_date).add(1,"year").format("YYYY")];
                            }

                            
                        }

                    }
                }

                Object.keys(CUTS).forEach((cut)=>{
                    obj["Year: "+cut] = CUTS[cut];
                })


                if(typeof rebateSubmission.logs==="object" && rebateSubmission.logs.length>0){
                    rebateSubmission.logs.forEach(function(log){
                        if(log.m===`Updated owner account to: <code>${rebateSubmission.owner[0].email}</code>`){                    
                            obj.days_with_owner = dayjs().diff(dayjs(log.d),"day");
                        }
                    });
                }
                
                obj.link = `https://lactalisculinarystp.bmghub.us/rebate/${rebateSubmission._id.toString()}/details`;

                //console.log(obj,"----");
                dataToReturn.push(obj);
            }

            //console.log(dataToReturn);
            
            sheetsToReturn.raw.data=dataToReturn;

            let fs = require("fs");
            fs.writeFileSync(process.cwd()+"/tmp/SAMPLE.json", JSON.stringify(sheetsToReturn));
        
            const workbook = new ExcelJS.Workbook();
            const sheetRawData = workbook.addWorksheet('Raw Data');            
            sheetRawData.addRows(common.objsToArray(sheetsToReturn.raw.data));

            
           /
        
            Object.keys(sheetsToReturn.summaries).forEach((rebateId)=>{                
                let summarySheet = workbook.addWorksheet(sheetsToReturn.summaries[rebateId].name);
                let rowIndex=2;

                Object.keys(sheetsToReturn.summaries[rebateId].data.anticipated).forEach((year)=>{
                    summarySheet.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
                    summarySheet.getRow(rowIndex).getCell(1).value=sheetsToReturn.summaries[rebateId].data.anticipated[year].label;
                    summarySheet.getRow(rowIndex).getCell(2).value=Math.round(sheetsToReturn.summaries[rebateId].data.anticipated[year].value);
                    rowIndex++;
                });
                Object.keys(sheetsToReturn.summaries[rebateId].data.total_possible).forEach((year)=>{
                    summarySheet.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
                    summarySheet.getRow(rowIndex).getCell(1).value=sheetsToReturn.summaries[rebateId].data.total_possible[year].label;
                    summarySheet.getRow(rowIndex).getCell(2).value=Math.round(sheetsToReturn.summaries[rebateId].data.total_possible[year].value);
                    rowIndex++;
                });

                summarySheet.getRow(rowIndex).getCell(1).font={"bold":true,"name":"Arial"};
                summarySheet.getRow(rowIndex).getCell(1).value=sheetsToReturn.summaries[rebateId].data.ltd_payment.label;
                summarySheet.getRow(rowIndex).getCell(2).value=Math.round(sheetsToReturn.summaries[rebateId].data.ltd_payment.value);
                                
            });

            workbook.xlsx.writeFile(process.cwd()+"/tmp/SAMPLE.xlsx");
            console.log("done");
        
           return dataToReturn; //rebateSubmissions;
    
        }

        switch(req.params.exportId.trim().toUpperCase()){
            case "OPERATORFORM":            
                response.success(res, await exportOperatorFormData());            
            break;    
        }

    }catch(e){
        console.log(e);
        response.error(res, e);
    }
    
    

    



    
    

}

