const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");
const _ = require("lodash");

const PRIZES={
    "1":[
        
        {
            "id": "102412",
            "name": "Cracker Barrel Notebook with Pen",
            "week": "1",
            "wheel_index": 14
        },
        {
            "id": "102413",
            "name": "Cracker Barrel Notebook with Pen",
            "week": "1",
            "wheel_index": 14
        }
    ],
    "2":[
        {
            "id": "102414",
            "name": "Galbani & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102415",
            "name": "Galbani & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102416",
            "name": "Galbani & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102417",
            "name": "Galbani & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102418",
            "name": "<PERSON><PERSON><PERSON>i & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102419",
            "name": "Galbani & Président Umbrella",
            "week": "2",
            "wheel_index": 12
        },
        {
            "id": "102420",
            "name": "Président Notebook with Pen",
            "week": "2",
            "wheel_index": 2
        },
        {
            "id": "102422",
            "name": "A case of Président Camembert Cheese",
            "week": "2",
            "wheel_index": 7
        },
        {
            "id": "102423",
            "name": "A case of Président Camembert Cheese",
            "week": "2",
            "wheel_index": 7
        }
    ],
    "3":[
       
        {
            "id": "102427",
            "name": "A jar of Kraft Natural Cheese Grated Parmesan",
            "week": "3",
            "wheel_index": 6
        }
    ],
    "4":[
        {
            "id": "102424",
            "name": "A case of Kraft Natural Cheese Parmesan Packets",
            "week": "3",
            "wheel_index": 11
        },
        {
            "id": "102425",
            "name": "A bag of Kraft Natural Cheese Parmesan Fancy Shred",
            "week": "3",
            "wheel_index": 1
        },
        //Week 3 Above
        {
            "id": "102401",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102402",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102403",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102404",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102405",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102406",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102407",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "1",
            "wheel_index": 9
        },
        {
            "id": "102411",
            "name": "Cracker Barrel Cooler Bag",
            "week": "1",
            "wheel_index": 4
        },
        /// Week 1 Above
        {
            "id": "102408",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "4",
            "wheel_index": 9
        },
        {
            "id": "102409",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "4",
            "wheel_index": 9
        },
        {
            "id": "102410",
            "name": "Cracker Barrel 'Go for the Bold' T-shirt",
            "week": "4",
            "wheel_index": 9
        },
        {
            "id": "102421",
            "name": "Président Notebook with Pen",
            "week": "4",
            "wheel_index": 2
        },
        {
            "id": "102426",
            "name": "A bag of Kraft Natural Cheese Parmesan Fancy Shred",
            "week": "4",
            "wheel_index": 1
        },
        {
            "id": "102428",
            "name": "A jar of Kraft Natural Cheese Grated Parmesan",
            "week": "4",
            "wheel_index": 6
        },
        {
            "id": "102429",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102430",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102431",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102432",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102433",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102434",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102435",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102436",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102437",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102438",
            "name": "A pair of Galbani Pizza Socks",
            "week": "4",
            "wheel_index": 13
        },
        {
            "id": "102439",
            "name": "A Galbani Pizza Peel",
            "week": "4",
            "wheel_index": 3
        },
        {
            "id": "102440",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102441",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102442",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102443",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102444",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102445",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102446",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102447",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        },
        {
            "id": "102448",
            "name": "A Galbani Measuring Cup",
            "week": "4",
            "wheel_index": 8
        }
    ]
}

const TRYAGAINSLICES=[0,5,10];

module.exports=async function(req, res, contest){
    

    req.body.week = req.body.week.toString().trim();    
    req.params.contestKey = req.params.contestKey.trim().toLowerCase();
    req.params.playerId = req.params.playerId.trim().toLowerCase();

    if(req.params.playerId!=="test"){
        let contestPlayerPlays = await mdb.client().collection("contest_plays").find({"player":req.params.playerId, "meta.week":req.body.week}).toArray();

        if(contestPlayerPlays && contestPlayerPlays.length>0){
            response.fail(res,"It looks like you have already spun the wheel. Sorry, limit one play per week per person.");
            return false;
        }
    }

    


    /* ==========================================================================
        Grab all the prizes won for this week and create an array of available prizes
    ============================================================================= */

    let contestWeekPlays = await mdb.client().collection("contest_plays").find({"meta.week":req.body.week}).toArray();
    
    let availablePrizes=[];
    let prizesWon=[];

    if(contestWeekPlays && contestWeekPlays.length>0){
        for(let play of contestWeekPlays){
            if(typeof play.result.prize!=="undefined" && typeof play.result.prize.id==="string"){
                prizesWon.push(play.result.prize.id);
            }            
        }
    }

    for(let prize of PRIZES[req.body.week]){
        if(prizesWon.indexOf(prize.id)<0){
            availablePrizes.push(prize.id);
        }
    }

    /*=============================================================================
        We have available prizes.  Now calculate if the player won or not
    ===============================================================================*/
    let winner=false,  
        selectedPrize={},
        selectedWheelIndex = TRYAGAINSLICES[_.random(0,2,false)],
        selectedPrizeName = "One Spin per email. Try again with the next email!";

    if(availablePrizes.length>0){
        switch(req.body.week){
            case "1":
                //if([0].indexOf(_.random(0,14,false))>=0){
                    winner=true;
                //}
            break;
            case "2":
                if([0].indexOf(_.random(0,3,false))>=0){
                    winner=true;
                }
            break;
            case "3":
                if([0].indexOf(_.random(0,20,false))>=0){
                    winner=true;
                }
            break;
            case "4":
               // if([0].indexOf(_.random(0,5,false))>=0){
                    winner=true;
                //}
            break;
        }
        

        if(winner){                    
            selectedPrize = _.find(PRIZES[req.body.week],{"id":availablePrizes.pop()});

            if(typeof selectedPrize==="object" && typeof selectedPrize.wheel_index==="number"){
                selectedWheelIndex = selectedPrize.wheel_index;
                selectedPrizeName = selectedPrize.name
            }        
        }
    }

    let dataToReturn={
        "winner":winner,
        "wheel_index":selectedWheelIndex,
        "name":selectedPrizeName
    };


    let dataToSave={
        "contest_key":req.params.contestKey,
        "player":req.params.playerId,
        "result":{
            "win":winner
        },        
        "meta":{
            "week":req.body.week
        },
        "created":new Date()
    }

    if(winner && Object.keys(selectedPrize).length>0){ dataToSave.result.prize = selectedPrize; }

    if(req.params.playerId!=="test"){    
        await mdb.client().collection("contest_plays").insertOne(dataToSave);
    }

    response.success(res, dataToReturn);
}