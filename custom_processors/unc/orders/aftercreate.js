const mdb = require(process.cwd()+"/libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require(process.cwd()+"/libs/aws.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const activityModel = require(process.cwd()+"/models/model_activity.js");
const _ = require("lodash");
const response = require(process.cwd()+"/libs/response.js");
const orderProcessor = require(process.cwd()+"/libs/orders.js");
const BMGUSER="bmg";


/***********************************************
    PARAMS
        - reqBody
        - org
        - newOrderId
        - orderTemplateData        
************************************************/
module.exports=async function(PARAMS){    
    let ISTEST = false;  
    
    // Make sure we cast the form fields as the correct type
    Object.keys(PARAMS.reqBody.fields).forEach((f)=>{          
        switch(PARAMS.org.data_configs.orders.fields[f].type){
            case "text":
                if(PARAMS.reqBody.fields[f] && PARAMS.reqBody.fields[f].trim().toLowerCase()==="test"){
                    if(PARAMS.orderTemplateData.account.tags.indexOf(BMGUSER)>=0){
                        ISTEST=true;
                        console.log("TEST UNC ORDER");
                    }            
                }
            break;
        }
    });
    
   // console.log("PARAMS",PARAMS);
    // SEND EMAIL TO BMG ---------------------------
    console.log("TO",((ISTEST) ? PARAMS.orderTemplateData.account.email  : PARAMS.org.server.config.contacts.bmg_warehouse));
    aws.email({
        "to": ((ISTEST) ? PARAMS.orderTemplateData.account.email  : PARAMS.org.server.config.contacts.bmg_warehouse),
        "from": `${PARAMS.org.server.templates.order.bmgWarehouse.from} <${PARAMS.org.server.config.contacts.from}>`,
        "subject": (ISTEST ? "**TEST** ":"")+HB(PARAMS.org.server.templates.order.bmgWarehouse.subject.trim(), {"order":PARAMS.orderTemplateData}),
        "body":HB(PARAMS.org.server.templates.order.bmgWarehouse.body,{"order":PARAMS.orderTemplateData, "items":PARAMS.orderItems})
    }).then(r=>{       
        console.log(r);         
    }).catch(e=>{
        console.log("AWS EMAIL error",e)        
    });
}