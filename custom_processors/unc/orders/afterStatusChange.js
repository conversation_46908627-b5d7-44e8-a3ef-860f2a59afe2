const mdb = require(process.cwd()+"/libs/mdb.js");
const aws = require(process.cwd()+"/libs/aws.js");
const _ = require("lodash");
const HB = require(process.cwd()+"/libs/handlebars.js");

/***********************************************
    p - Params contain
        - order
        - org
        - newStatus
    
    If new status === APPROVED
        - Send email to account is TRUE
        - Also send email to Warehouse

    If status === DENIED
        - Send email to account
        - DO NOT send one to recipient
************************************************/    
module.exports=async function(p){
    const STATUS_APPROVED="approved";

    let response = {
        "emailAccount":true,
        "emailRecipient":false
    };

    if(p.newStatus===STATUS_APPROVED){
        response.emailAccount=false;

        let orderApprovalData={
            "approved_by":{
                "name":"N/A",
                "email":p.session.email
            }
        };
                
        let approvalAccount = await mdb.client().collection("accounts").findOne({"_id":p.session.account_id});
        orderApprovalData.approved_by.name = approvalAccount.fields.FNAME+" "+approvalAccount.fields.LNAME;

        await mdb.client().collection("orders").updateOne({"_id":p.order._id}, {"$set":orderApprovalData});

        // --- SEND ORDER EMAIL TO ACCOUNT -----------------------------------
        aws.email({
            "to":p.order.account.email,
            "from": `${p.org.server.templates.order.newToAccount.from} <${p.org.server.config.contacts.from}>`,
            "subject": HB(p.org.server.templates.order.newToAccount.subject.trim(),{"order":p.order}),
            "body":HB(p.org.server.templates.order.newToAccount.body,{"order":p.order, "items":p.orderItems})
        }).then(r=>{
            console.log("ORDER APPROVED - ACCOUNT EMAIL SENT:",p.order.account.email, p.order._id.toString());
        }).catch(e=>{
            console.log("AWS EMAIL error",e)        
        });

        // --- SEND ORDER EMAIL TO RECIPIENT -----------------------------------
        aws.email({
            "to":p.order.fields.EMAIL.split(",").map(function(s){ return s.trim(); }),
            "reply":p.session.email,
            "from": `${p.org.server.templates.order.newToRecipient.from} <${p.org.server.config.contacts.from}>`,
            "subject": HB(p.org.server.templates.order.newToRecipient.subject.trim(),{"order":p.order}),
            "body":HB(p.org.server.templates.order.newToRecipient.body,{"order":p.order, "items":p.orderItems})
        }).then(r=>{
            console.log("ORDER APPROVED - RECIPIENT EMAIL SENT:",p.order.fields.EMAIL.split(",").map(function(s){ return s.trim(); }), p.order._id.toString());                
        }).catch(e=>{
            console.log("AWS EMAIL error",e)        
        });


        // --- SEND WAREHOUSE EMAIL -----------------------------------
        if(p.order.warehouse==="CLIENT"){
            aws.email({
                "to":((process.env["ISDEV"]) ? "<EMAIL>" :p.org.server.config.contacts.client_warehouse),
                "from": `${p.org.server.templates.order.clientWarehouse.from} <${p.org.server.config.contacts.from}>`,
                "subject": HB(p.org.server.templates.order.clientWarehouse.subject.trim(),{"order":p.order}),
                "body":HB(p.org.server.templates.order.clientWarehouse.body,{"order":p.order, "items":p.orderItems})
            }).then(r=>{
                console.log("ORDER APPROVED - CLIENT WAREHOUSE EMAIL SENT:",p.org.server.config.contacts.client_warehouse, p.order._id.toString());
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });

        }else{
            aws.email({
                "to":((process.env["ISDEV"]) ? "<EMAIL>" :p.org.server.config.contacts.bmg_warehouse),
                "from": `${p.org.server.templates.order.bmgWarehouse.from} <${p.org.server.config.contacts.from}>`,
                "subject": HB(p.org.server.templates.order.bmgWarehouse.subject.trim(),{"order":p.order}),
                "body":HB(p.org.server.templates.order.bmgWarehouse.body,{"order":p.order, "items":p.orderItems})
            }).then(r=>{
                console.log("ORDER APPROVED - BMG WAREHOUSE EMAIL SENT:",p.org.server.config.contacts.bmg_warehouse, p.order._id.toString());
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });

        }
        
    }

    return response;

};