const mdb = require(process.cwd()+"/libs/mdb.js");
const dayjs = require("dayjs");
const aws = require(process.cwd()+"/libs/aws.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const _ = require("lodash");
const response = require(process.cwd()+"/libs/response.js");
const orderProcessor = require(process.cwd()+"/libs/orders.js");

module.exports=async function(req,res,org){
    let reqBody = ((req.body) ? req.body : {}); 
    let ISTEST = false;

    let orderAccount={}

    const ORGID=mdb.objectId("67bc91fcdd8f51b8134d7b54"),
        BMG_FULFILLED_ITEM_TAGS=[],
        CLIENT_FULFILLED_ITEM_TAGS=[],        
        ACCOUNT_TAGS_NEEDING_APPROVAL=["fullView","dsd","retail","foodservice"],
        ACCOUNT_TAG_APPROVAL_MGR=["approvalManager"],
        STATUS_PENDING="pendingApproval",
        BMGUSER="bmg";

    let failReason = false;

    if(!reqBody.items){
        response.fail(res, "We could not place your order because your cart is empty.");
        return;
    }

    try{
        // ---- Find the approval manager for the person who placed the order -----------------------------------------------------
        let approvalManagers={};
        ACCOUNT_TAGS_NEEDING_APPROVAL.forEach(function(t){
            approvalManagers[t]=[];
        })
        orderAccount = await mdb.client().collection("accounts").findOne({"_id":req.SESSION.account_id});
        let approvalManagersAccounts = await mdb.client().collection("accounts").find({"org_id":ORGID,"tags":{"$in":ACCOUNT_TAG_APPROVAL_MGR}}).toArray();
        approvalManagersAccounts.forEach(function(account){
            approvalManagers[_.intersection(account.tags,ACCOUNT_TAGS_NEEDING_APPROVAL)[0]].push({
                "email":account.email,
                "name":account.fields.FNAME+" "+account.fields.LNAME
            });
        });

        // ---- Make sure we cast the form fields as the correct type ----------------------------------
        Object.keys(reqBody.fields).forEach((f)=>{   
        
            // ---- Allow BMG users to send TEST orders
            if(reqBody.fields[f].trim().toLowerCase()==="test"){
                if(orderAccount.tags.indexOf(BMGUSER)>=0){
                    ISTEST=true;
                }            
            }
        
            switch(org.data_configs.orders.fields[f].type){
                case "date":
                    reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
                break;
                case "number":
                    reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
                break;
            }
        });

        // ---- Find the order Items -----------------------------------------------
        let itemsInOrder=[];
        Object.keys(reqBody.items).forEach(function(itemId){        
            itemsInOrder.push(mdb.objectId(itemId));
        });

        let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemsInOrder}}).toArray();

        /* -------------------------------------------------------------------------------
            Now loop the order items to find out if we have BMG and/or Client warehouse items            
        ---------------------------------------------------------------------------------*/
        let bmgFulfilledItems=[], clientFulfilledItems=[], singleOrderItems=[];        
            
        for(let orderItem of orderItems){              
            if(_.intersection(orderItem.tags,CLIENT_FULFILLED_ITEM_TAGS).length>0){            
                clientFulfilledItems.push(Object.assign({"org_tag":"UNC"},reqBody.items[orderItem._id.toString()],orderItem));
            }else{            
                bmgFulfilledItems.push(Object.assign({"org_tag":"UNC"},reqBody.items[orderItem._id.toString()],orderItem));
            } 

            singleOrderItems.push(Object.assign({"org_tag":"UNC"},reqBody.items[orderItem._id.toString()],orderItem));    
        }


        /* -------------------------------------------------------------------------------
            Check to see if the account needs approval.  
        ---------------------------------------------------------------------------------*/
        let orderNeedApproval=false, approvalManagersToSendTo=[];
        if(_.intersection(req.SESSION.tags,ACCOUNT_TAG_APPROVAL_MGR).length===0){
            if(_.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0){
                let accountTagNeedingApproval = _.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL)[0];
                approvalManagersToSendTo = approvalManagers[accountTagNeedingApproval];
                orderNeedApproval=true;
                org.data_configs.orders.events.onCreate.emailAccount=false;
                org.data_configs.orders.events.onCreate.emailRecipient=false;
            }
        }

        /************************************************
         * BMG FULFILLED ORDER
         ************************************************/
        if(bmgFulfilledItems.length>0){
            let bmgItems={};
            bmgFulfilledItems.forEach(i=>{            
                bmgItems[i._id]=reqBody.items[i._id];
            });

            let bmgOrder=Object.assign({"org_tag":"UNC"},reqBody,{"items":bmgItems});

            bmgOrder.limited_to_tags = BMG_FULFILLED_ITEM_TAGS; 
            bmgOrder.warehouse = "BMG";

            if(orderNeedApproval){
                bmgOrder.status = STATUS_PENDING;
            }

            let orderTemplateData = await orderProcessor.create(bmgOrder, req.SESSION, org, true);

            if(typeof orderTemplateData==="object"){
                if(orderNeedApproval){                    
                    approvalManagersToSendTo.forEach(function(approvalManager){
                        aws.email({
                            "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : approvalManager.email) ),
                            "from": `${org.server.templates.order.approvalNeeded.from} <${org.server.config.contacts.from}>`,
                            "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.approvalNeeded.subject.trim(), {"order":orderTemplateData}),
                            "body":HB(org.server.templates.order.approvalNeeded.body,{"order":orderTemplateData, "items":bmgFulfilledItems})
                        }).then(r=>{                
                        }).catch(e=>{
                            console.log("AWS EMAIL error",e)        
                        });
                    })                                             
                }else{                    
                    aws.email({
                        "to": ((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : "<EMAIL>")),
                        "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                        "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":bmgFulfilledItems})
                    }).then(r=>{                
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });
                }
            }else{
                failReason = orderTemplateData;
            }
        }

        /************************************************
         * CLIENT FULFILLED ORDER
         ************************************************/
        if(clientFulfilledItems.length>0){
            let clientItems={};
            clientFulfilledItems.forEach(i=>{
                clientItems[i._id]=reqBody.items[i._id];
            });
            let clientOrder=Object.assign({"org_tag":"UNC"},reqBody,{"items":clientItems});
            clientOrder.limited_to_tags = CLIENT_FULFILLED_ITEM_TAGS; 
            clientOrder.warehouse = "CLIENT";

            // Set the status to pending if needing approval
            if(orderNeedApproval){ clientOrder.status = STATUS_PENDING; }

            let orderTemplateData = await orderProcessor.create(clientOrder, req.SESSION, org);

            if(typeof orderTemplateData==="object"){
                if(orderNeedApproval){
                    approvalManagersToSendTo.forEach(function(approvalManager){
                        aws.email({
                            "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : approvalManager.email) ),
                            "from": `${org.server.templates.order.approvalNeeded.from} <${org.server.config.contacts.from}>`,
                            "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.approvalNeeded.subject.trim(), {"order":orderTemplateData}),
                            "body":HB(org.server.templates.order.approvalNeeded.body,{"order":orderTemplateData, "items":clientFulfilledItems})
                        }).then(r=>{                
                        }).catch(e=>{
                            console.log("AWS EMAIL error",e)        
                        });
                    });                                    
                }else{
                    
                    aws.email({
                        "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : org.server.config.contacts.client_warehouse)),
                        "from": `${org.server.templates.order.clientWarehouse.from} <${org.server.config.contacts.from}>`,
                        "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.clientWarehouse.subject.trim(), {"order":orderTemplateData}),
                        "body":HB(org.server.templates.order.clientWarehouse.body, {"order":orderTemplateData, "items":clientFulfilledItems})
                    }).then(r=>{     
                    }).catch(e=>{
                        console.log("AWS EMAIL error",e)        
                    });  
                }
            }else{
                failReason = orderTemplateData;
            }
        }

        if(failReason){
            console.log("UNC ORDER FAILED!");
            console.log(failReason);
            response.fail(res, failReason);
        }else{
            response.success(res);        
        }



    }catch(e){
        console.log(e);
        response.error(res, "Order processor error");
    }
    
};