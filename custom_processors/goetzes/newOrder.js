const mdb = require("../../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require("../../libs/aws.js");
const HB = require("../../libs/handlebars.js");
const activityModel = require("../../models/model_activity.js");
const _ = require("lodash");
const response = require("../../libs/response.js");
const orderProcessor = require("../../libs/orders.js");

const BMG_FULFILLED_ITEM_TAGS=[""],
    CLIENT_FULFILLED_ITEM_TAGS=["nMZxKXDFJR"],
    BMGUSER="bmg";

module.exports=async function(req,res,org){

    let reqBody = ((req.body) ? req.body : {}); 
    let ISTEST = false;

    if(!reqBody.items){
        response.fail(res, "We could not place your order because your cart is empty.");
        return;
    }

    // ---- Find the account that placed order ---------------------------------------
    let orderAccount = await mdb.client().collection("accounts").findOne({"_id":req.SESSION.account_id});


    // ---- Make sure we cast the form fields as the correct type ----------------------------------
    Object.keys(reqBody.fields).forEach((f)=>{   

        // ---- Allow BMG users to send TEST orders
        if(reqBody.fields[f].trim().toLowerCase()==="test"){
            if(orderAccount.tags.indexOf(BMGUSER)>=0){
                ISTEST=true;
            }            
        }

        switch(org.data_configs.orders.fields[f].type){
            case "date":
                reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
            break;
            case "number":
                reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
            break;
        }
    });

    // ---- Find the order Items -----------------------------------------------
    let itemsInOrder=[];
    Object.keys(reqBody.items).forEach(function(itemId){        
        itemsInOrder.push(mdb.objectId(itemId));
    });

    let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemsInOrder}}).toArray();    

    let bmgFulfilledItems=[], clientFulfilledItems=[], singleOrderItems=[];
    
    for(let orderItem of orderItems){              
        if(_.intersection(orderItem.tags,CLIENT_FULFILLED_ITEM_TAGS).length>0){            
            clientFulfilledItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        }else{            
            bmgFulfilledItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        } 
        singleOrderItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
    }



    // =========================================================================================
    // This order has BOTH types of items and we need to split it.
    if(bmgFulfilledItems.length>0 && clientFulfilledItems.length>0){

        //=== Construct BMG Order =============================
        let bmgItems={};
        bmgFulfilledItems.forEach(i=>{            
            bmgItems[i._id]=reqBody.items[i._id];
        });

        let bmgOrder=Object.assign({},reqBody,{"items":bmgItems});

        bmgOrder.exclude_tags = CLIENT_FULFILLED_ITEM_TAGS; 

        orderProcessor.create(bmgOrder, req.SESSION, org).then((orderTemplateData)=>{
            aws.email({
                "to": ((ISTEST) ? orderAccount.email  : org.server.config.contacts.bmg_warehouse),
                "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":bmgFulfilledItems})
            }).then(r=>{                
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            }); 
        }).catch(err=>{
            console.log(err);
            response.fail(res);
        });

        //=== Construct CLIENT Order =============================
        let clientItems={};
        clientFulfilledItems.forEach(i=>{
            clientItems[i._id]=reqBody.items[i._id];
        });
        let clientOrder=Object.assign({},reqBody,{"items":clientItems});
        clientOrder.limited_to_tags = CLIENT_FULFILLED_ITEM_TAGS; 

        orderProcessor.create(clientOrder, req.SESSION, org).then((orderTemplateData)=>{
            aws.email({
                "to":((ISTEST) ? orderAccount.email  : org.server.config.contacts.client_warehouse),
                "from": `${org.server.templates.order.clientWarehouse.from} <${org.server.config.contacts.from}>`,
                "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.clientWarehouse.subject.trim(), {"order":orderTemplateData}),
                "body":HB(org.server.templates.order.clientWarehouse.body, {"order":orderTemplateData, "items":clientFulfilledItems})
            }).then(r=>{     
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });  
        }).catch(err=>{
            console.log(err);
            response.fail(res);
        });

        response.success(res);

    }else{ // Just create one order

        if(clientFulfilledItems.length>0){
            reqBody.limited_to_tags = CLIENT_FULFILLED_ITEM_TAGS;
        }else{
            reqBody.exclude_tags = CLIENT_FULFILLED_ITEM_TAGS;
        }     

        orderProcessor.create(reqBody, req.SESSION, org).then((orderTemplateData)=>{

            if(clientFulfilledItems.length>0){
                                
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : org.server.config.contacts.client_warehouse),
                    "from": `${org.server.templates.order.clientWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.clientWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.clientWarehouse.body, {"order":orderTemplateData, "items":singleOrderItems})
                }).then(r=>{     
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });  
                
            }else{
                
                aws.email({
                    "to": ((ISTEST) ? orderAccount.email  : org.server.config.contacts.bmg_warehouse),
                    "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":singleOrderItems})
                }).then(r=>{                
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                }); 
            }

            response.success(res);

        }).catch(err=>{
            console.log(err);
            response.fail(res);
        });

    }

}