const mdb = require("../libs/mdb.js");
const dayjs = require("dayjs");
const COLLECTION = "orders";
const aws = require("../libs/aws.js");
const Handlebars = require("handlebars");
const activityModel = require("../models/model_activity.js");
const _ = require("lodash");
const response = require("../libs/response.js");

module.exports=async function(req,res,org){
  let reqBody = ((req.body) ? req.body : {});

  if(!reqBody.items){
    response.fail(res, "We could not place your order because your cart is empty.");
    return;
  }

  // Find the status to use for a new order
  let status = _.find(org.data_configs.orders.statuses,{"default":true});

  // Make sure we cast the form fields as the correct type
  Object.keys(reqBody.fields).forEach((f)=>{    
    switch(org.data_configs.orders.fields[f].type){
      case "date":
        reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
      break;
      case "number":
        reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
      break;
    }
  });

  // Construct the data to save
  let dataToSave={
    "org_id":req.SESSION.org_id,
    "account_id":req.SESSION.account_id,            
    "created":new Date(),
    "status":((status) ? status.label : ((org.data_configs.orders.statuses) ? org.data_configs.orders.statuses[0].label : "NEW")),
    "fields":reqBody.fields,
    "items":reqBody.items
  };

  // Save new order
  let newOrder = await mdb.client().collection(COLLECTION).insertOne(dataToSave);

  if(newOrder){

    // Clear Users Cart
    await mdb.client().collection("accounts").updateOne({"_id":req.SESSION.account_id},{"$set":{"cart":{}}});

    // ********** Check the event configs ************

    // ADJUST INVENTORY ---------------------------
    if(org.data_configs.orders.events.onCreate.adjustInventory){      
      for(let itemId of Object.keys(reqBody.items)){
        await mdb.client().collection("items").updateOne({_id:mdb.objectId(itemId)}, {"$inc":{"inventory.qty": (reqBody.items[itemId]).qty * ((status.adjustInventory) ? status.adjustInventory : 0) }});
      }
    }

    // ASSEMBLE ORDER ITEMS FOR TEMPLATES ---------------------------
    let orderItems=[];
    if(org.data_configs.orders.events.onCreate.emailAccount || org.data_configs.orders.events.onCreate.emailRecipient){
      let orgItems = JSON.parse(JSON.stringify(await mdb.client().collection("items").find({"org_id":req.SESSION.org_id,"actions.order":true},{"projection":{"name":true, "image":true}}).toArray()));      
      Object.keys(reqBody.items).forEach((itemId)=>{    
        orderItems.push(Object.assign({"qty":Number(reqBody.items[itemId].qty)},_.find(orgItems, {"_id":itemId})))    
      });
      orgItems=null;
    }

    // SEND EMAIL TO ACCOUNT---------------------------
    if(org.data_configs.orders.events.onCreate.emailAccount){
      aws.email({
        "to":req.SESSION.email,
        "from": `${org.server.templates.order.newToAccount.from} <${org.server.config.contacts.from}>`,
        "subject": org.server.templates.order.newToAccount.subject.trim(),
        "body":Handlebars.compile(org.server.templates.order.newToAccount.body)({"order_id":newOrder.insertedId, "fields":reqBody.fields, "items":orderItems})
      }).then(r=>{                
      }).catch(e=>{
        console.log("AWS EMAIL error",e)        
      });      
    }

    // SEND EMAIL TO RECIPIENT ---------------------------
    if(org.data_configs.orders.events.onCreate.emailRecipient && reqBody.fields.EMAIL){
      aws.email({
        "to":reqBody.fields.EMAIL,
        "reply":req.SESSION.email,
        "from": `${org.server.templates.order.newToRecipient.from} <${org.server.config.contacts.from}>`,
        "subject": org.server.templates.order.newToRecipient.subject.trim(),
        "body":Handlebars.compile(org.server.templates.order.newToRecipient.body)({"order_id":newOrder.insertedId, "fields":reqBody.fields, "items":orderItems})
      }).then(r=>{                
      }).catch(e=>{
        console.log("AWS EMAIL error",e)        
      });
    }

    // LOG ACTIVITY ---------------------------
    let activityDataToLog=[{
        "org_id":req.SESSION.org_id,          
        "account_id":req.SESSION.account_id,
        "event":{
          "system":"order",
          "action":"placed"   
        },          
        "data":{
          "items":dataToSave.items
        }
      }];

    orderItems.forEach(function(orderedItem){
      activityDataToLog.push({
        "org_id":req.SESSION.org_id,          
        "account_id":req.SESSION.account_id,
        "event":{
          "system":"item",
          "action":"ordered"   
        },          
        "data":{
          "item_name":orderedItem.name,
          "item_id":mdb.objectId(orderedItem._id),
          "qty":orderedItem.qty
        }
      });  
    });
    
    activityModel.log(activityDataToLog);


    // RESPOND TO USER ---------------------------
    response.success(res);
    
  }else{
    response.fail(res, "Failed to place your order.");
  }
  
}