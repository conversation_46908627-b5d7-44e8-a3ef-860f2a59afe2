const mdb = require(process.cwd()+"/libs/mdb.js");
const aws = require(process.cwd()+"/libs/aws.js");
const _ = require("lodash");
const HB = require(process.cwd()+"/libs/handlebars.js");

/***********************************************
    p - Params contain
        - order
        - org
        - newStatus
    
    If new status === APPROVED
        - Send email to account is TRUE
        - Also send email to Warehouse

    If status === DENIED
        - Send email to account
        - DO NOT send one to recipient


************************************************/    
module.exports=async function(p){
    const STATUS_APPROVED="wW9DK";

    let response = {
        "emailAccount":true,
        "emailRecipient":false
    };

    if(p.newStatus===STATUS_APPROVED){
        let orderApprovalData={
            "approved_by":{
                "name":"N/A",
                "email":p.session.email
            }
        };
                
        let approvalAccount = await mdb.client().collection("accounts").findOne({"_id":p.session.account_id});
        orderApprovalData.approved_by.name = approvalAccount.fields.FNAME+" "+approvalAccount.fields.LNAME;


        await mdb.client().collection("orders").updateOne({"_id":p.order._id}, {"$set":orderApprovalData});

        if(p.order.warehouse==="CLIENT"){
            aws.email({
                "to":((process.env["ISDEV"]) ? "<EMAIL>" :p.org.server.config.contacts.sahlen_warehouse),
                "from": `${p.org.server.templates.order.clientWarehouse.from} <${p.org.server.config.contacts.from}>`,
                "subject": HB(p.org.server.templates.order.clientWarehouse.subject.trim(),{"order":p.order}),
                "body":HB(p.org.server.templates.order.clientWarehouse.body,{"order":p.order, "items":p.orderItems})
            }).then(r=>{                    
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });

        }else{
            aws.email({
                "to":((process.env["ISDEV"]) ? "<EMAIL>" :p.org.server.config.contacts.bmg_warehouse),
                "from": `${p.org.server.templates.order.bmgWarehouse.from} <${p.org.server.config.contacts.from}>`,
                "subject": HB(p.org.server.templates.order.bmgWarehouse.subject.trim(),{"order":p.order}),
                "body":HB(p.org.server.templates.order.bmgWarehouse.body,{"order":p.order, "items":p.orderItems})
            }).then(r=>{                    
            }).catch(e=>{
                console.log("AWS EMAIL error",e)        
            });

        }
        
    }

    return response;

};