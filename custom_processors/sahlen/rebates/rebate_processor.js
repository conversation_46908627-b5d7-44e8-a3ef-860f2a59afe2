const aws = require(process.cwd()+"/libs/aws.js");
const mdb = require(process.cwd()+"/libs/mdb.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const _ = require("lodash");

module.exports=function(_get){    
    return {
        "afterClone":async function(req, newDocument){

        let contacts={
            "territoryManagers":req.ORG.server.config.contacts.rebates.territoryManagers,
            "bmg":req.ORG.server.config.contacts.rebates.bmg
        };

        let submission = await _get(newDocument._id.toString());

        let to=[];
            to.push(submission.broker.email);
            

        let matchingTerritory=_.intersection(submission.broker.tags, contacts.territoryManagers);
        if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to = to.concat(contacts.territoryManagers[t]);
            });
        }            
        
        let templateData={
            SUBMISSIONID:submission._id.toString(),
            REBATENAME:submission.rebate.name,
            BROKERNAME:submission.broker.fields.FNAME + " " + submission.broker.fields.LNAME,
            BROKEREMAIL:submission.broker.email,
            OPERATORNAME:submission.operator.name,
            OPERATORADDRESS:submission.operator.address,
            OPERATORCITY:submission.operator.city,
            OPERATORSTATE:submission.operator.state,
            OPERATORZIP:submission.operator.zip
        };        

        await aws.email({
            "to": ((process.env.ISDEV) ? "<EMAIL>"  : to),
            "from": `${req.ORG.server.templates.rebate.cloned.from} <${req.ORG.server.config.contacts.from}>`,
            "subject": HB(req.ORG.server.templates.rebate.cloned.subject,templateData),
            "body":HB(req.ORG.server.templates.rebate.cloned.body,templateData)
        });

        return true;

    }
    }
}