const mdb = require(process.cwd()+"/libs/mdb.js");
const common = require(process.cwd()+"/libs/common.js");
const response = require(process.cwd()+"/libs/response.js");
const aws = require(process.cwd()+"/libs/aws.js");
const operatorsModel = require(process.cwd()+"/models/model_operators.js");
const dayjs = require("dayjs");
const _ = require("lodash");

module.exports={
/*****************************************
    AFTER SAVE
*****************************************/
    afterSave:async function(req, res, data){               
        let formFlowSubmissionId =  mdb.objectId(req.params.id);

        //Find the Form Flow Submission ---------------------------------------------------
        let formFlowSubmission = await mdb.client().collection("formflow_submissions").findOne({
            "_id":formFlowSubmissionId,
            "org_id":req.SESSION.org_id
        });

        if(formFlowSubmission){    

            //Find the Rebate Submission ---------------------------------------------------
            let rebateSubmission = await mdb.client().collection("rebate_submissions").findOne({
                "_id":formFlowSubmission.rebatesubmission_id,
                "org_id":req.SESSION.org_id
            });

            // Is there already an operator associated with this rebate?
            let operatorId="";
            if(rebateSubmission && !rebateSubmission.operator_id){
                // No operator? Then create one and assign it ---------------------------------------------------
                operatorId = await operatorsModel.create({
                    "org_id":req.SESSION.org_id,
                    "name":data.OPERATORNAME,
                    "address":data.ADDRESS,
                    "city":data.CITY,
                    "state":data.STATE,
                    "zip":data.ZIP,
                    "phone":data.PHONE,
                    "contacts":[
                        {
                            "email":data.OPERATORCONTACTEMAIL,
                            "name":data.OPERATORCONTACTNAME
                        }
                    ],
                    "created":new Date(),
                    "updated_by":req.SESSION.email
                });

            }else{
                //Update Operator Record
                operatorId = rebateSubmission.operator_id;
                await operatorsModel.update(rebateSubmission.operator_id, {
                    "name":data.OPERATORNAME,
                    "address":data.ADDRESS,
                    "city":data.CITY,
                    "state":data.STATE,
                    "zip":data.ZIP,
                    "phone":data.PHONE,
                    "contacts":[
                        {
                            "email":data.OPERATORCONTACTEMAIL,
                            "name":data.OPERATORCONTACTNAME
                        }
                    ],
                    "updated":new Date(),
                    "updated_by":req.SESSION.email
                });

            }

            await mdb.client().collection("rebate_submissions").updateOne({
                "_id":formFlowSubmission.rebatesubmission_id,
                "org_id":req.SESSION.org_id
            },{
                "$set":{
                    "operator_id":operatorId,
                    "updated":new Date(),
                    "updated_by":req.SESSION.email
                }
            });

            response.success(res);

        }else{
            response.fail(res, "Form Flow Submission Not Found");
        }
        

    }
}