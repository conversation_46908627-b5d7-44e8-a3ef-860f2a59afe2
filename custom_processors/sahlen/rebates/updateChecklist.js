const mdb = require(process.cwd()+"/libs/mdb.js");
const _ = require("lodash");
const aws = require(process.cwd()+"/libs/aws.js");
const response = require(process.cwd()+"/libs/response.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const SAHLEN = require(process.cwd()+"/custom_processors/sahlen/common.js");

module.exports=async function(req,res){

    let TERRITORYMGRS=req.ORG.server.config.contacts.rebates.territoryManagers;
    let BMG=req.ORG.server.config.contacts.rebates.bmg;


    let submission = await mdb.client().collection("rebate_submissions").aggregate([
        {"$match":{"_id":mdb.objectId(req.params.rebateSubmissionId)}}, 
        {"$limit":1},       
        { "$lookup": { "from": "rebates", "localField": "rebate_id", "foreignField": "_id", "as": "rebate" } },
        { "$lookup": { "from": "operators", "localField": "operator_id", "foreignField": "_id", "as": "operator" } },
        { "$lookup": { "from": "accounts", "localField": "account_id", "foreignField": "_id", "as": "broker" } },
        { "$lookup": { "from": "formflow_submissions", "localField": "formflow_submissions", "foreignField": "_id", "as": "formFlowSubmissions" } },
      ]).toArray();     

      if(!submission || submission.length===0){
        response.fail(res);
        return;
      }

    submission = submission[0];
    submission.rebate = submission.rebate[0];
    submission.operator = submission.operator[0];
    submission.broker = submission.broker[0];
    
    

    const CONTACTSETS=["1","2"];
    let to={};
    let TEMPLATE={};

    CONTACTSETS.forEach(function(cs){
        to[cs]=[];
        TEMPLATE[cs]="";
    })

    let dataToSet={ "closed":false };    
    let templateData={};

    let matchingTerritory=_.intersection(submission.broker.tags, Object.keys(TERRITORYMGRS))

    console.log(submission.checklist)
    if( // CLOSED / DISCONTINUED ////////////////////////////////////////////
        submission.checklist.closedDiscontinued
    ){

        dataToSet.status = "Closed / Discontinued";
        dataToSet.closed = true;

        to["1"].push(submission.broker.email);
        
        if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }
        
        TEMPLATE["1"]="checklistClosedDiscontinued";
        TEMPLATE["2"]="checklistClosedDiscontinued";

    }else if( // CLOSED / LOST ////////////////////////////////////////////
        submission.checklist.closedLost
    ){

        dataToSet.status = "Closed / Lost";
        dataToSet.closed = true;

        to["1"].push(submission.broker.email);
        
        if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }
        
        TEMPLATE["1"]="checklistClosedLost";
        TEMPLATE["2"]="checklistClosedLost";

    }else if( // PAYMENT SENT ////////////////////////////////////////////
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.operatorIncentiveApproved
        && submission.checklist.proofOfPurchaseComplete 
        && (typeof submission.checklist.proofOfPromotionsComplete==="undefined" || submission.checklist.proofOfPromotionsComplete)
        && submission.checklist.ssrReviewComplete 
        && submission.checklist.paymentSent                
    ){
        dataToSet.status = "Payment Sent";
        dataToSet.closed = true;
        to["1"].push(submission.broker.email);
        
        if(submission.collaborators.length>0){
            to["1"] = to["1"].concat(submission.collaborators);
        }
        
        if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }
        
        TEMPLATE["1"]="checklistPaymentSent";
        TEMPLATE["2"]="checklistPaymentSent";

    }else if( // PENDING PAYMENT ////////////////////////////////////////////
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.operatorIncentiveApproved
        && submission.checklist.proofOfPurchaseComplete 
        && (typeof submission.checklist.proofOfPromotionsComplete==="undefined" || submission.checklist.proofOfPromotionsComplete)
        && submission.checklist.ssrReviewComplete         
    ){
        dataToSet.status = "Pending Payment";
        
        to["1"] = to["1"].concat(["<EMAIL>"]);     //BMG  

        TEMPLATE["1"]="checklistPendingPayment";

    }else if( // Step 3: Pending Sahlen Review ////////////////////////////////////////////
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.operatorIncentiveApproved
        && submission.checklist.proofOfPurchaseComplete 
    ){
        dataToSet.status = "Pending Sahlen Review";
        
        /*if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }*/   
        
        to["2"].push("<EMAIL>");
        TEMPLATE["2"]="checklistSahlenReview";


    }else if( // Step 2b: In "Pending Reporting" until both are complete ////////////////////////////////////////////
        submission.checklist.operatorRegistrationComplete 
        && submission.checklist.operatorIncentiveApproved
        && !submission.checklist.proofOfPurchaseComplete
    ){
        dataToSet.status = "Operator is Buying";

        to["1"].push(submission.broker.email);
        
        if(submission.collaborators.length>0){
            to["1"] = to["1"].concat(submission.collaborators);
        }
        
        TEMPLATE["1"]="checklistOperatorIsBuying";

        /*if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }*/
        
        TEMPLATE["2"]="checklistOperatorIsBuying";

    }else if( // Step 1: PENDING APPROVAL ///////////////////////////////////////
        submission.checklist.operatorRegistrationComplete
    ){
        dataToSet.status = "Pending Approval";

        if(matchingTerritory.length>0){
            matchingTerritory.forEach(function(t){
                to["2"] = to["2"].concat(TERRITORYMGRS[t]);
            });
        }
        
        TEMPLATE["2"]="checklistPendingApproval";

    }else{ /////////////// ELSE /////////////////////////////////////
        dataToSet.status = "Pending Data Collection";        
    }
    

    await mdb.client().collection("rebate_submissions").updateOne({
        "_id":mdb.objectId(req.params.rebateSubmissionId),
        "org_id":req.SESSION.org_id
    },{
        "$set":dataToSet
    });    
    
    let SUBMISSIONREBATELEVEL="-";
    if(typeof submission.rebate.meta_fields==="object"){
        if(submission.rebate._id.toString()==="675894edb7865a02c021e833"){            
            if(submission.meta.rebateLevelJSON && submission.meta.rebateLevelJSON.length>0){
                try{
                    submission.meta.rebateLevelJSON = Buffer.from(submission.meta.rebateLevelJSON,'base64').toString('utf8');
                    submission.meta.rebateLevel = JSON.parse(submission.meta.rebateLevelJSON);
                    SUBMISSIONREBATELEVEL=`<ul>`
                    if(submission.meta.rebateLevel.hotdogs){
                        SUBMISSIONREBATELEVEL+=`<li>Hot Dogs: ${submission.meta.rebateLevel.hotdogs} @ $0.10/lb</li>`;
                    }
                    if(submission.meta.rebateLevel.ham){
                        SUBMISSIONREBATELEVEL+=`<li>Ham: ${submission.meta.rebateLevel.ham} @ $0.10/lb</li>`;
                    }
                    if(submission.meta.rebateLevel.poultry){
                        SUBMISSIONREBATELEVEL+=`<li>Poultry: ${submission.meta.rebateLevel.poultry} @ $0.20/lb</li>`;
                    }
                    SUBMISSIONREBATELEVEL+=`</ul>`;
                }catch(e){
                    console.log("Error parsing rebateLevel",e);
                }
                
            }
            
        }else{
            let metaFieldDetials = _.find(submission.rebate.meta_fields,{"field_id":"rebateLevel"});
            if(metaFieldDetials && submission.meta && submission.meta.rebateLevel){
                let selectedOption = _.find(metaFieldDetials.options,{"value":submission.meta.rebateLevel});
                SUBMISSIONREBATELEVEL = (selectedOption) ? selectedOption.label : "-";
            }
        }
    }

    templateData = Object.assign(templateData, {
        "REBATENAME":submission.rebate.name,
        "OPERATORNAME":((submission.operator && submission.operator.name) ? submission.operator.name : "(Operator name not set)"),
        "OPERATORCONTACT":((submission.operator && typeof submission.operator.contacts==="object" && submission.operator.contacts.length>0 && typeof submission.operator.contacts[0].name==="string") ? submission.operator.contacts[0].name : "(Operator contact name not set)"),
        "OPERATORCONTACTEMAIL":((submission.operator && typeof submission.operator.contacts==="object" && submission.operator.contacts.length>0 && typeof submission.operator.contacts[0].email==="string") ? submission.operator.contacts[0].email : "(Operator contact email not set)"),
        "OPERATORADDRESS":((submission.operator && submission.operator.address) ? submission.operator.address : "-"),
        "OPERATORCITY":((submission.operator && submission.operator.city) ? submission.operator.city : "-"),
        "OPERATORSTATE":((submission.operator && submission.operator.state) ? submission.operator.state : "-"),
        "OPERATORZIP":((submission.operator && submission.operator.zip) ? submission.operator.zip : "-"),        
        "BROKERNAME":submission.broker.fields.FNAME + " " + submission.broker.fields.LNAME,
        "BROKEREMAIL":submission.broker.email,
        "BROKERADDRESS":submission.broker.fields.STREET + ((submission.broker.fields.STREET_2.length>0) ?  " " + submission.broker.fields.STREET_2:""),
        "BROKERCITY":submission.broker.fields.CITY,
        "BROKERSTATE":submission.broker.fields.STATE,
        "BROKERZIP":submission.broker.fields.ZIP,
        "STATUS":dataToSet.status,
        "SUBMISSIONID":req.params.rebateSubmissionId,
        "COMMENT":((req.body.comment) ? req.body.comment : ""),
        "VERIFIEDPAYMENTAMOUNT":((submission.meta && typeof submission.meta.paymentAmount!=="undefined") ? submission.meta.paymentAmount : "-"),
        "REBATELEVEL":SUBMISSIONREBATELEVEL,
        "DELIVERYOPTION":"-",//((submission.meta && typeof submission.meta.deliveryOption!=="undefined") ? submission.meta.deliveryOption : "-"),
        "TERRITORYREP":((matchingTerritory.length>0) ? TERRITORYMGRS[matchingTerritory[0]][0] : "-"),
        "TERM":SAHLEN.dateToQuarter(submission.created)
    });

    if(typeof submission.meta==="object"){
        templateData.META = submission.meta;
    }    

    if(submission.formFlowSubmissions.length>0){
        let regForm = _.find(submission.formFlowSubmissions,{"formflow_id": mdb.objectId("677c209c9a6a0406767562f5") });
        if(regForm && typeof regForm.response==="object" && Object.keys(regForm.response).length>0){
            templateData.DELIVERYOPTION = ((typeof regForm.response.DELIVERYOPTION!=="undefined") ? regForm.response.DELIVERYOPTION : "");
        }        
    }


    let ISTEST=false;
    if(req.SESSION.tags.indexOf("tester")>=0){
        ISTEST=true;
    }


    CONTACTSETS.forEach(function(i){    

        if(to[i].length>0 && TEMPLATE[i] && TEMPLATE[i].length>0){
                        
            try{
                aws.email({
                    "to": ((process.env.ISDEV) ? "<EMAIL>"  : ((ISTEST) ? req.SESSION.email : to[i])),
                    "from": `${req.ORG.server.templates.rebate[TEMPLATE[i]].from} <${req.ORG.server.config.contacts.from}>`,
                    "subject": HB(req.ORG.server.templates.rebate[TEMPLATE[i]].subject,templateData),
                    "body":HB(req.ORG.server.templates.rebate[TEMPLATE[i]].body,templateData)
                }).then(r=>{
                                    
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });
            }catch(e){
                console.log("Sahlen Rebate Email error",e);
            }
        
        
        }

    });

        
    

    response.success(res, dataToSet);
}