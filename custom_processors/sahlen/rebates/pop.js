const mdb = require(process.cwd()+"/libs/mdb.js");
const objectStorage = require(process.cwd()+"/libs/objectstorage.js");
const _ = require("lodash");

module.exports=function(FIELDNAME){
    return {
        /*****************************************
            BEFORE SAVE
        *****************************************/
            beforeSave:async function(req, flow, dataToSave){
                let dataToReturn = {};
                    dataToReturn[FIELDNAME]=[];
        
                let submission = await mdb.client().collection("formflow_submissions").findOne({"_id": mdb.objectId(req.params.id)});
        
                if(typeof submission.response==="object" && typeof submission.response[FIELDNAME]==="object" && submission.response[FIELDNAME].length>0){
                    dataToReturn[FIELDNAME] = dataToReturn[FIELDNAME].concat(submission.response[FIELDNAME]);
                }
                
                if(submission && typeof submission.response!=="undefined"){
                    // --- Save File Object -----------------------------------------------
                    try{
                        let newObjectId = await objectStorage.create(Object.assign({ "meta": {
                            "rebate_id":submission.rebate_id,
                            "formflow_submissions_id":mdb.objectId(req.params.id)
                        }, 
                            "org_id":req.ORG._id,
                            "account_id":req.SESSION.account_id
                        },dataToSave[FIELDNAME]));
            
                        if(newObjectId){
                            dataToReturn[FIELDNAME].push(newObjectId);
                        }
                    }catch(e){
                        console.log(dataToSave);
                        console.log("Sahlen Proof of Purchase before save",e);    
                    }
                }
        
                return dataToReturn;
                
            },
        
            /*****************************************
                BEFORE SET
            *****************************************/
            beforeSet:async function(req, flow, dataToSave){
                let dataToReturn = {};
                    dataToReturn[FIELDNAME]=[], 
                    fileIdToDelete="";
        
                // Find the current submission so we can compare the current file ids with the new file ids
                let submission = await mdb.client().collection("formflow_submissions").findOne({"_id": mdb.objectId(req.params.id)});
        
                if(typeof submission.response==="object" && typeof submission.response[FIELDNAME]==="object" && submission.response[FIELDNAME].length>0){
        
                    // Cast the current file ids to strings so we can compare them with the new file ids
                    let currentIds=[];
                    submission.response[FIELDNAME].forEach(function(id, i){
                        currentIds.push(id.toString());
                    });
        
                    fileIdsToDelete = _.difference(currentIds,dataToSave[FIELDNAME]);
        
                    console.log("fileIdsToDelete", fileIdsToDelete);
        
        
                    // Cast the new file ids to ObjectIds so we can save them
                    dataToSave[FIELDNAME].forEach(function(id, i){
                        dataToSave[FIELDNAME][i] = mdb.objectId(id.toString());
                    });
        
                    // This will be the data that is returned to the main formflow submission processor and saved.
                    dataToReturn[FIELDNAME] = dataToReturn[FIELDNAME].concat(dataToSave[FIELDNAME]);            
                }   
        
                // Now we need to delete the files that were removed from the submission
                try{
                   if(fileIdsToDelete.length>0){
        
                        // Cast the file ids to ObjectIds so we can send it to the objectStorage library
                        fileIdsToDelete.forEach(function(id, i){
                            fileIdsToDelete[i] = mdb.objectId(id);
                        });
        
                        await objectStorage.deleteMany({
                            "ids":fileIdsToDelete,
                            "orgId":req.ORG._id
                        });            
        
                   }                  
                }catch(e){
                    console.log(dataToReturn);
                    console.log("Sahlen Proof of Purchase before save",e);    
                }
        
                console.log("dataToReturn",dataToReturn);
                if(dataToReturn[FIELDNAME].length===0){ delete dataToReturn[FIELDNAME]; }
                return dataToReturn;
            }
        }
}