const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");
const _ = require("lodash");
const dayjs = require("dayjs");
dayjs.extend(require('dayjs/plugin/relativeTime'));
const ExcelJS = require('exceljs');
const common = require(process.cwd()+"/libs/common.js");
let workbook = null;

module.exports=async function(req, res, org){
    try{
        
        let rebateDetails = {};
        let rebates = await mdb.client().collection("rebates").find({"org_id":req.SESSION.org_id}).toArray()
        rebates.forEach(function(rebate){
            rebateDetails[rebate._id.toString()]=rebate;
        });

        async function exportOperatorFormData(){
            let dataToReturn=[], obj={};
            workbook = new ExcelJS.Workbook();
            let mainSheet = workbook.addWorksheet('Rebate List');
           
            
    
            let match={
                "org_id":req.SESSION.org_id,                
                "rebate_id":{"$in":[]}
            };
    
            if(typeof req.body.rebate_ids==="string"){
                req.body.rebate_ids=[req.body.rebate_ids];
            }
    
            for(let rebateId of req.body.rebate_ids){
                match.rebate_id["$in"].push(mdb.objectId(rebateId));
            }
            
            if(typeof req.body.createdFrom==="string"){  req.body.createdFrom=dayjs(req.body.createdFrom).toDate();  }
            if(typeof req.body.createdTo==="string"){  req.body.createdTo=dayjs(req.body.createdTo).add(1,"day").toDate();  }
    
            if(typeof req.body.createdFrom!=="undefined" || typeof req.body.createdTo!=="undefined"){  match["$and"]=[];  }
            
            if(typeof req.body.createdFrom!=="undefined"){  match["$and"].push({"created":{"$gte":req.body.createdFrom}});  }
            if(typeof req.body.createdTo!=="undefined"){  match["$and"].push({"created":{"$lt":req.body.createdTo}});  }
    
            let rebateSubmissions = await mdb.client().collection("rebate_submissions").aggregate([
                {"$match":match},
                {"$lookup":{
                    "from":"formflow_submissions",
                    "localField":"_id",
                    "foreignField":"rebatesubmission_id",
                    "as":"forms"
                }},
                {"$lookup":{
                    "from":"accounts",
                    "localField":"account_id",
                    "foreignField":"_id",
                    "as":"owner"
                }}
            ]).toArray();

            
    
            for(let rebateSubmission of rebateSubmissions){
                
                obj={};
                obj.id=rebateSubmission._id.toString();
                obj.rebate=rebateDetails[rebateSubmission.rebate_id.toString()].name;
                obj.date_created = dayjs(rebateSubmission.created).format("MM-DD-YYYY");
                obj.submission_age_days = dayjs().diff(dayjs(rebateSubmission.created),"day");
                obj.owner = rebateSubmission.owner[0].fields.FNAME+" "+rebateSubmission.owner[0].fields.LNAME;
                obj.owner_email = rebateSubmission.owner[0].email;
                obj.status = rebateSubmission.status;
                obj.payment_amount = ((typeof rebateSubmission.meta==="object" && typeof rebateSubmission.meta.paymentAmount==="number")?rebateSubmission.meta.paymentAmount:"");
                obj.rebate_level = ((typeof rebateSubmission.meta==="object" && typeof rebateSubmission.meta.rebateLevel==="string")?rebateSubmission.meta.rebateLevel.toUpperCase():"");
    
                obj.operator_name="";                
                obj.address="";
                obj.city="";
                obj.state="";
                obj.zip="";
                obj.rep="";
                obj.operator_contact="";
                obj.operator_contact_title="";
                obj.operator_email="";
                obj.operator_phone="";
                obj.restaurant_units="";
                obj.distributor_name="";
                obj.distributor_rep_name="";
                obj.first_shipment_date="";
                obj.delivery_option="";
    
               
            
                //Now lets grab the form data to fill out the rest of the fields.
    
                let response = _.find(rebateSubmission.forms, (o)=>{
                    return [
                        mdb.objectId("677c209c9a6a0406767562f5"),
                        mdb.objectId("675888bbb7865a02c021e82c")
                    ].some(id => id.equals(o.formflow_id));
                });
                
         

                if(response && typeof response.response==="object" && Object.keys(response.response).length>0){
                    //console.log(response.response,"----");
                    obj.operator_name = response.response.OPERATORNAME;
                    obj.operator_contact = response.response.OPERATORCONTACTNAME;
                    obj.address = response.response.ADDRESS;
                    obj.city = response.response.CITY;
                    obj.state = response.response.STATE;
                    obj.zip = response.response.ZIP;
                    obj.rep = response.response.SAHLENREP;
                    obj.operator_contact_title = response.response.OPERATORCONTACTTITLE;
                    obj.operator_email = response.response.OPERATORCONTACTEMAIL;
                    obj.operator_phone = response.response.OPERATORCONTACTPHONE;
                    obj.restaurant_units = response.response.NUMBEROFUNITS;
                    obj.distributor_name = response.response.DISTRIBUTORNAME;
                    obj.distributor_rep_name = response.response.DISTRIBUTORREPNAME;
                    obj.first_shipment_date = response.response.FIRSTSHIPMENTDATE;                                                            
                    obj.delivery_option = response.response.DELIVERYOPTION;                                                            
                }
                                
                obj.link = `https://sahlen.bmghub.us/rebate/${rebateSubmission._id.toString()}/details`;     
                

                
                dataToReturn.push(obj);
            }
            
            mainSheet.addRows(common.objsToArray(dataToReturn));
           return dataToReturn; //rebateSubmissions;
    
        }

        switch(req.params.exportId.trim().toUpperCase()){
            case "OPERATORFORM":            
                //response.success(res, await exportOperatorFormData());
                await exportOperatorFormData();
                if(workbook){
                    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                    res.setHeader('Content-Disposition', 'attachment; filename="report.xlsx"');                        
                    await workbook.xlsx.write(res);
                    res.end();
                }else{
                    response.fail(res, "No data found for the criteria set.");
                }            
            break;
    
        }
    }catch(e){
        console.log(e);
        response.error(res, e);
    }
    
    

    



    
    

}

