const mdb = require(process.cwd()+"/libs/mdb.js");
const response = require(process.cwd()+"/libs/response.js");
const _ = require("lodash");
const dayjs = require("dayjs");
dayjs.extend(require('dayjs/plugin/relativeTime'));

module.exports=async function(req, res, org){
    try{
        
        let rebateDetails = {};
        let rebates = await mdb.client().collection("rebates").find({"org_id":req.SESSION.org_id}).toArray()
        rebates.forEach(function(rebate){
            rebateDetails[rebate._id.toString()]=rebate;
        });

        async function exportOperatorFormData(){
            let dataToReturn=[], obj={};
    
            let match={
                "org_id":req.SESSION.org_id,                
                "rebate_id":{"$in":[]}
            };
    
            if(typeof req.body.rebate_ids==="string"){
                req.body.rebate_ids=[req.body.rebate_ids];
            }
    
            for(let rebateId of req.body.rebate_ids){
                match.rebate_id["$in"].push(mdb.objectId(rebateId));
            }
            
            if(typeof req.body.createdFrom==="string"){  req.body.createdFrom=dayjs(req.body.createdFrom).toDate();  }
            if(typeof req.body.createdTo==="string"){  req.body.createdTo=dayjs(req.body.createdTo).add(1,"day").toDate();  }
    
            if(typeof req.body.createdFrom!=="undefined" || typeof req.body.createdTo!=="undefined"){  match["$and"]=[];  }
            
            if(typeof req.body.createdFrom!=="undefined"){  match["$and"].push({"created":{"$gte":req.body.createdFrom}});  }
            if(typeof req.body.createdTo!=="undefined"){  match["$and"].push({"created":{"$lt":req.body.createdTo}});  }
    
            let rebateSubmissions = await mdb.client().collection("rebate_submissions").aggregate([
                {"$match":match},
                {"$lookup":{
                    "from":"formflow_submissions",
                    "localField":"_id",
                    "foreignField":"rebatesubmission_id",
                    "as":"forms"
                }},
                {"$lookup":{
                    "from":"accounts",
                    "localField":"account_id",
                    "foreignField":"_id",
                    "as":"owner"
                }}
            ]).toArray();

            
    
            for(let rebateSubmission of rebateSubmissions){
                
                obj={};
                obj.id=rebateSubmission._id.toString();
                obj.rebate=rebateDetails[rebateSubmission.rebate_id.toString()].name;
                obj.date_created = dayjs(rebateSubmission.created).format("MM-DD-YYYY");
                obj.submission_age_days = dayjs().diff(dayjs(rebateSubmission.created),"day");
                obj.owner = rebateSubmission.owner[0].fields.FNAME+" "+rebateSubmission.owner[0].fields.LNAME;
                obj.owner_email = rebateSubmission.owner[0].email;
                obj.status = rebateSubmission.status;
                obj.payment_amount = ((typeof rebateSubmission.meta==="object" && typeof rebateSubmission.meta.paymentAmount==="number")?rebateSubmission.meta.paymentAmount:"");
                obj.rebate_level = ((typeof rebateSubmission.meta==="object" && typeof rebateSubmission.meta.rebateLevel==="string")?rebateSubmission.meta.rebateLevel.toUpperCase():"");
    
                obj.operator_name="";
                obj.operator_contact="";
                obj.address="";
                obj.city="";
                obj.state="";
                obj.zip="";
                obj.operator_email="";
                obj.restaurant_units="";
                obj.distributor_name="";
                obj.first_shipment_date="";
    
               
            
                //Now lets grab the form data to fill out the rest of the fields.
    
                let response = _.find(rebateSubmission.forms, {"formflow_id":mdb.objectId("675888bbb7865a02c021e82c")});                                        

                if(response && typeof response.response==="object" && Object.keys(response.response).length>0){
                    //console.log(response.response,"----");
                    obj.operator_name = response.response.OPERATORNAME;
                    obj.operator_contact = response.response.OPERATORCONTACT;
                    obj.address = response.response.ADDRESS;
                    obj.city = response.response.CITY;
                    obj.state = response.response.STATE;
                    obj.zip = response.response.ZIP;
                    obj.operator_email = response.response.EMAIL;
                    obj.restaurant_units = response.response.RESTAURANTUNITS;
                    obj.distributor_name = response.response.DISTRIBUTORNAME;
                    obj.first_shipment_date = response.response.FIRSTSHIPMENTDATE;                                                            
                }
                                
                obj.link = `https://sahlen.bmghub.us/rebate/${rebateSubmission._id.toString()}/details`;                
                dataToReturn.push(obj);
            }
            
           return dataToReturn; //rebateSubmissions;
    
        }

        switch(req.params.exportId.trim().toUpperCase()){
            case "OPERATORFORM":            
                response.success(res, await exportOperatorFormData());            
            break;
    
        }
    }catch(e){
        console.log(e);
        response.error(res, e);
    }
    
    

    



    
    

}

