const mdb = require(process.cwd()+"/libs/mdb.js");
const operatorsModel = require(process.cwd()+"/models/model_operators.js");

module.exports={
    syncFieldMapper:function(data){    
            return data;
    },

    syncProcessRequest:function(req, P){
        return new Promise(async (resolve, reject)=>{
            try{
                const OWNERACCOUNTID = mdb.objectId("64b1648a442df01f0714c939"); // <PERSON>
                const REGISTRATIONFORMIDS=["675888bbb7865a02c021e82c","677c209c9a6a0406767562f5"];
            
                const ORGID = P.REBATE.org_id;
                let formData = module.exports.syncFieldMapper(req.body);
                let rebateSubmissionId = mdb.objectId();
    
                // We need to create an operator 
                let operatorId = await operatorsModel.create({
                    "org_id":ORGID,
                    "name":formData["Company"],
                    "address":formData["Street Address "],
                    "city":formData["City"],
                    "state":formData["State"]["name"],
                    "zip":formData["Zip Code"],
                    "phone":formData["Phone Number"],
                    "contacts":[
                        {
                            "email":formData["contact_email_address"],
                            "name":formData["contact_first_name"] + " " + formData["contact_last_name"]
                        }
                    ],
                    "created":new Date(),
                    "updated_by":"BMG Sync"
                });
    
                // Create base rebate submission record
                let dataToInsert={
                    "_id":rebateSubmissionId,
                    "operator_id":operatorId,
                    "org_id":ORGID,
                    "account_id":OWNERACCOUNTID,
                    "rebate_id":P.REBATEID,
                    "collaborators":[formData["Email Address"]],
                    "formflows":[],
                    "formflow_submissions":[],
                    "checklist":{},
                    "status":"Created",
                    "created":new Date(),
                    "updated":new Date()
                };
            
                //Now Create formflow submissions
                for(let formFlow of P.REBATE.formflows){           
                    
                    let responseData={};
                    if(REGISTRATIONFORMIDS.indexOf(formFlow.id.toString())>=0){
                        responseData={
                            "OPERATORNAME" : formData["Company"],
                            "ADDRESS" : formData["Street Address "],
                            "CITY" : formData["City"],
                            "STATE" : formData["State"]["name"],
                            "ZIP" : formData["Zip Code"],
                            "OPERATORCONTACTNAME" : formData["First Name"] + " " + formData["Last Name"],
                            "OPERATORCONTACTTITLE" : formData["Title/Position"],
                            "OPERATORCONTACTPHONE" : formData["Phone Number"],
                            "OPERATORCONTACTEMAIL" : formData["Email Address"],
                            "SAHLENREP" : formData["Sales Rep Name"],
                            "DISTRIBUTORNAME" : formData["Distributor Name:"],
                            "DISTRIBUTORREPNAME" : formData["Distributor Rep Name"],
                            "FIRSTSHIPMENTDATE" : new Date(formData["Shipment Date "]),
                            "NUMBEROFUNITS":Number(formData["Number of Units"])
                        }
                    }
    
                    let formFlowSubmission = await mdb.client().collection("formflow_submissions").insertOne({
                        "org_id":ORGID,
                        "account_id":OWNERACCOUNTID,
                        "rebate_id":P.REBATEID,
                        "rebatesubmission_id":rebateSubmissionId,
                        "formflow_id":formFlow.id,
                        "name":"",
                        "response":responseData,
                        "created":new Date()
                    });
    
                    dataToInsert.formflow_submissions.push(formFlowSubmission.insertedId);
                }
    
                P.REBATE.formflows.forEach(function(formFlow){
                    dataToInsert.formflows.push(formFlow.id)
                });
    
                P.REBATE.checklist.items.forEach(function(checklistItem){
                    dataToInsert.checklist[checklistItem.id]=false
                });
    
                let newRebateSubmission = await mdb.client().collection("rebate_submissions").insertOne(dataToInsert);
                
                if(newRebateSubmission && newRebateSubmission.insertedId){
                    resolve();
                }else{
                    reject("Failed to record Sync Rebate Submission - E1");
                    console.log("Failed to record Sync Rebate Submission - E1",dataToInsert);            
                }
    
            }catch(e){
                reject("Failed to record Sync Rebate Submission - E2");
                console.log("Failed to record Sync Rebate Submission - E2");
                console.log(e);
            }
        });
    }
}