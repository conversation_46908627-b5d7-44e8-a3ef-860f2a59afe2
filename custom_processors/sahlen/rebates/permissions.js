const _ = require("lodash");

module.exports=function(req){
    
    let match=[{}];
    let accountTagsWhoSeeAll=["rebateManager", "dev", "bmg"], //  Just set this to the new rebate mgr tag    
        accountTagsWhoSeeZone={
            "64b164fb442df01f0714c93b":["territoryMike"], 
            "64b164c9442df01f0714c93a":["territoryKen"], 
            "64b165bf442df01f0714c93c":["territoryScott"],
            "66fae0673060097034d0879b":["territoryJoe"]        
        };

    // ALL REQUESTS -------------------------------------
    if(req.SESSION && req.SESSION.tags && accountTagsWhoSeeAll.length>0 && _.intersection(req.SESSION.tags,accountTagsWhoSeeAll).length>0){
        match = [{
            "org_id":req.SESSION.org_id
        }];

    // ZONE REQUESTS -------------------------------------
    }else if(req.SESSION && req.SESSION.account_id && typeof accountTagsWhoSeeZone[req.SESSION.account_id.toString()]!=="undefined"){

        // "broker.0.fields.STATE":{"$in":accountTagsWhoSeeZone[req.SESSION.account_id.toString()]}
        match = [{
            "org_id":req.SESSION.org_id
        },{                      
            "$or":[
                {"broker.0.tags":{"$in":accountTagsWhoSeeZone[req.SESSION.account_id.toString()]}},
                {"account_id":req.SESSION.account_id}
            ]                       
        }];

    // JUST MY REQUESTS -----------------------------------
    }else{
        
        match = {
            "org_id":req.SESSION.org_id
        };

        if(req.SESSION && req.SESSION.account_id){
            match.account_id = req.SESSION.account_id;
        }else{
            match.collaborators = req.SESSION.email;
        }
        match=[match];
    }
    console.log(match);
    return match;
}