const mdb = require(process.cwd()+"/libs/mdb.js");
const dayjs = require("dayjs");
const aws = require(process.cwd()+"/libs/aws.js");
const HB = require(process.cwd()+"/libs/handlebars.js");
const _ = require("lodash");
const response = require(process.cwd()+"/libs/response.js");
const orderProcessor = require(process.cwd()+"/libs/orders.js");

const ITEMTAGS={
    pos_materials:"hBqPWxiAmN",
    promo_items:"dtxncYkJuB",
    product:"omvGbMgMmm",
    sellsheets:"UfgJEPAQFH",
    rebates:"22ZZXoSmQs",
    tradeshows:"QteCAHFnt3"

};
const ACCOUNTTAGS={
    broker:"cjNhcjen",
    bmg:"bmg"
};
const BMG_FULFILLED_ITEM_TAGS=[ITEMTAGS.pos_materials, ITEMTAGS.promo_items, ITEMTAGS.sellsheets, ITEMTAGS.rebates, ITEMTAGS.tradeshows], 
    CLIENT_FULFILLED_ITEM_TAGS=[ITEMTAGS.product], 
    ITEMS_NEEDING_APPROVAL_TAGS=[ITEMTAGS.pos_materials, ITEMTAGS.promo_items, ITEMTAGS.product],
    ACCOUNT_TAGS_NEEDING_APPROVAL=[ACCOUNTTAGS.broker],
    BMGUSER=ACCOUNTTAGS.bmg,
    STATUS_PENDING="c3yf7";

module.exports=async function(req,res,org){

    let reqBody = ((req.body) ? req.body : {}); 
    let ISTEST = false;
    let failReason=false;

    if(!reqBody.items){
        response.fail(res, "We could not place your order because your cart is empty.");
        return;
    }

    // ---- Find the account that placed order ---------------------------------------
    let orderAccount = await mdb.client().collection("accounts").findOne({"_id":req.SESSION.account_id});


    // ---- Make sure we cast the form fields as the correct type ----------------------------------
    Object.keys(reqBody.fields).forEach((f)=>{   

        // ---- Allow BMG users to send TEST orders
        if(reqBody.fields[f].trim().toLowerCase()==="test"){
            if(orderAccount.tags.indexOf(BMGUSER)>=0){
                ISTEST=true;
            }            
        }

        switch(org.data_configs.orders.fields[f].type){
            case "date":
                reqBody.fields[f] = ((reqBody.fields[f] && reqBody.fields[f]!=="") ? dayjs(reqBody.fields[f]).toDate() : null);
            break;
            case "number":
                reqBody.fields[f] =((reqBody.fields[f]) ? Number(reqBody.fields[f]) : null);
            break;
        }
    });


    // ---- Find the order Items -----------------------------------------------
    let itemsInOrder=[];
    Object.keys(reqBody.items).forEach(function(itemId){        
        itemsInOrder.push(mdb.objectId(itemId));
    });

    let orderItems = await mdb.client().collection("items").find({"_id":{"$in":itemsInOrder}}).toArray();    


    /* -------------------------------------------------------------------------------
        Now loop the order items to find out if we have BMG and/or Client warehouse items
        and if any of the items qualify as needing approval. 
    ---------------------------------------------------------------------------------*/
    let bmgFulfilledItems=[], clientFulfilledItems=[], singleOrderItems=[];
    let orderItemsMightNeedApproval=false;
    
    for(let orderItem of orderItems){              
        if(_.intersection(orderItem.tags,CLIENT_FULFILLED_ITEM_TAGS).length>0){            
            clientFulfilledItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        }else{            
            bmgFulfilledItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));
        } 
        singleOrderItems.push(Object.assign({},reqBody.items[orderItem._id.toString()],orderItem));

        if(_.intersection(orderItem.tags,ITEMS_NEEDING_APPROVAL_TAGS).length>0){ 
            orderItemsMightNeedApproval=true;
        } 
    }

    /*-------------------------------------------------------------------------------------
        Now, if the order items may require approval.  Check to see if the account that place
        the order is one that needs to be approved. If YES, then find their approval manager
    --------------------------------------------------------------------------------------*/
    let approvalManager=null;    
    if(orderItemsMightNeedApproval && _.intersection(req.SESSION.tags,ACCOUNT_TAGS_NEEDING_APPROVAL).length>0){
        approvalManager = ((reqBody.fields.STATE && req.ORG.server.config && req.ORG.server.config.contacts && req.ORG.server.config.contacts.approval_managers && req.ORG.server.config.contacts.approval_managers[reqBody.fields.STATE]) ? req.ORG.server.config.contacts.approval_managers[reqBody.fields.STATE] : "");
            
        if(_.intersection(req.SESSION.tags,["territoryKen"]).length>0){
            approvalManager={
                "name":"Ken Voelker",
                "email":"<EMAIL>"
            }
        }else if(_.intersection(req.SESSION.tags,["territoryMike"]).length>0){
            approvalManager={
                "name":"Mike Eckert",
                "email":"<EMAIL>"
            }
        }else if(_.intersection(req.SESSION.tags,["territoryScott"]).length>0){
            approvalManager={
                "name":"Scott Morgante",
                "email":"<EMAIL>"
            }
        }else if(_.intersection(req.SESSION.tags,["territoryJoe"]).length>0){
            approvalManager={
                "name":"Joe Annunziato",
                "email":"<EMAIL>"
            }
        }

        if(approvalManager && typeof approvalManager.email==="string"){
            reqBody.status = STATUS_PENDING;
        }
    }

/* ==============================================================================================================================================

    VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV   Now create orders and send out emails.   VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV

 ==============================================================================================================================================*/

    

    /************************************************
     * BMG FULFILLED ORDER
     ************************************************/
    if(bmgFulfilledItems.length>0){    
        let bmgItems={};
        bmgFulfilledItems.forEach(i=>{            
            bmgItems[i._id]=reqBody.items[i._id];
        });

        let bmgOrder=Object.assign({},reqBody,{"items":bmgItems});

        bmgOrder.limited_to_tags = BMG_FULFILLED_ITEM_TAGS; 
        bmgOrder.warehouse = "BMG";

        let orderTemplateData = await orderProcessor.create(bmgOrder, req.SESSION, org);
        if(typeof orderTemplateData==="object"){
            if(approvalManager && typeof approvalManager.email==="string"){
            
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : approvalManager.email) ),
                    "from": `${org.server.templates.order.approvalNeeded.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.approvalNeeded.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.approvalNeeded.body,{"order":orderTemplateData, "items":bmgFulfilledItems})
                }).then(r=>{                
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });
            
            }else{
                
                aws.email({
                    "to": ((ISTEST) ? orderAccount.email  : "<EMAIL>"),
                    "from": `${org.server.templates.order.bmgWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.bmgWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.bmgWarehouse.body,{"order":orderTemplateData, "items":bmgFulfilledItems})
                }).then(r=>{                
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });
            }
        }else{
            failReason = orderTemplateData;
        }   
    }

    /************************************************
     * CLIENT FULFILLED ORDER
     ************************************************/
    if(clientFulfilledItems.length>0){        
        let clientItems={};
        clientFulfilledItems.forEach(i=>{
            clientItems[i._id]=reqBody.items[i._id];
        });
        let clientOrder=Object.assign({},reqBody,{"items":clientItems});
        clientOrder.limited_to_tags = CLIENT_FULFILLED_ITEM_TAGS; 
        clientOrder.warehouse = "CLIENT";

        let orderTemplateData = await orderProcessor.create(clientOrder, req.SESSION, org);
        if(typeof orderTemplateData==="object"){
            if(approvalManager && typeof approvalManager.email==="string"){
            
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : ((process.env.ISDEV) ? "<EMAIL>" : approvalManager.email) ),
                    "from": `${org.server.templates.order.approvalNeeded.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.approvalNeeded.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.approvalNeeded.body,{"order":orderTemplateData, "items":clientFulfilledItems})
                }).then(r=>{                
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });
            
            }else{
                
                aws.email({
                    "to":((ISTEST) ? orderAccount.email  : org.server.config.contacts.sahlen_warehouse),
                    "from": `${org.server.templates.order.clientWarehouse.from} <${org.server.config.contacts.from}>`,
                    "subject": (ISTEST ? "**TEST** ":"")+HB(org.server.templates.order.clientWarehouse.subject.trim(), {"order":orderTemplateData}),
                    "body":HB(org.server.templates.order.clientWarehouse.body, {"order":orderTemplateData, "items":clientFulfilledItems})
                }).then(r=>{     
                }).catch(e=>{
                    console.log("AWS EMAIL error",e)        
                });  
            }
        }else{
            failReason = orderTemplateData;
        }

    }

    if(failReason){
        response.fail(res, failReason);
    }else{
        response.success(res);        
    }

}