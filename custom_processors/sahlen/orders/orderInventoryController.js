const mdb = require(process.cwd()+"/libs/mdb.js");

/***********************************
    P =
        order_id
        new_status_id
        current_status_id        
        org
        session
*************************************/

module.exports=async function(P){
    
    return new Promise(async (resolve, reject)=>{

        const SUBMITTED = "7yc3f";
        const PENDING_APPROVAL = "c3yf7";
        const APPROVED = "wW9DK";
        const CANCELED = "EFDgh";
        const REJECTED = "xYjGO";
        
        let dataToSend={adjustInventory:false, direction:0};
        let dataToSet={"updated":new Date()};
        let lastInventoryDirection=0;

        let order = await mdb.client().collection("orders").findOne({_id:P.order_id});

        if(order && typeof order.last_inventory_direction!="undefined"){
            lastInventoryDirection = order.last_inventory_direction;
        }

        if( // This is a new order, subtract inventory
            (P.new_status_id==SUBMITTED) 
            && lastInventoryDirection!==-1 
            && (!P.current_status_id || P.current_status_id==CANCELED || P.current_status_id==REJECTED )
        ){
            dataToSend = {adjustInventory:true, direction:-1};

        }else if( // Was pending approval, now approved, subtract inventory
            P.new_status_id==APPROVED 
            && lastInventoryDirection!==-1 
            && P.current_status_id==PENDING_APPROVAL
        ){
            dataToSend = {adjustInventory:true, direction:-1};
        
        }else if( // Order is canceled. Return inventory if the previous state is one submitted or approved.
            (P.new_status_id==CANCELED || P.new_status_id==REJECTED)  
            && lastInventoryDirection!==1
        ){ 
            dataToSend = {adjustInventory:true, direction:1};
        
        }

        if(dataToSend.direction!=0){ dataToSet.last_inventory_direction=dataToSend.direction; }

        await mdb.client().collection("orders").updateOne({_id:P.order_id},{
            "$push":{
                "logs":{"status_id":P.new_status_id, "by":P.session.email, "at":new Date(), "adjusted":dataToSend.adjustInventory, "direction":dataToSend.direction}
            },
            "$set":dataToSet
        });

        resolve(dataToSend);

        
                
    });

    
}